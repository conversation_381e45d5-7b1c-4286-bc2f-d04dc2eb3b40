/**
 * 工具类使用示例
 * 展示如何使用request和websocket工具类
 */

// 导入工具类
import request from './request.js'
import { WebSocketClient, getWebSocketInstance } from './websocket.js'

// ========== Request 使用示例 ==========

// 1. 基本GET请求
export async function getUserInfo(userId) {
  try {
    const data = await request.get('/user/info', { id: userId })
    console.log('用户信息:', data)
    return data
  } catch (error) {
    console.error('获取用户信息失败:', error.message)
    throw error
  }
}

// 2. POST请求
export async function createUser(userData) {
  try {
    const data = await request.post('/user/create', userData)
    console.log('创建用户成功:', data)
    return data
  } catch (error) {
    console.error('创建用户失败:', error.message)
    throw error
  }
}

// 3. 文件上传
export async function uploadFile(file) {
  try {
    const data = await request.upload('/upload', file)
    console.log('文件上传成功:', data)
    return data
  } catch (error) {
    console.error('文件上传失败:', error.message)
    throw error
  }
}

// 4. 设备相关API示例
export const deviceAPI = {
  // 获取设备列表
  getDeviceList: () => request.get('/device/list'),
  
  // 获取设备状态
  getDeviceStatus: (deviceId) => request.get(`/device/${deviceId}/status`),
  
  // 控制设备
  controlDevice: (deviceId, command) => request.post(`/device/${deviceId}/control`, command),
  
  // 获取环境信息
  getEnvironmentInfo: () => request.get('/environment/info'),
  
  // 水泵控制
  controlWaterPump: (action, params) => request.post('/device/water-pump/control', { action, ...params }),
  
  // 水炮控制
  controlWaterCannon: (action, params) => request.post('/device/water-cannon/control', { action, ...params }),
  
  // 雾炮控制
  controlFogCannon: (action, params) => request.post('/device/fog-cannon/control', { action, ...params })
}

// ========== WebSocket 使用示例 ==========

// 1. 创建WebSocket连接
export function createWebSocketConnection() {
  const ws = new WebSocketClient({
    reconnectInterval: 3000,
    maxReconnectAttempts: 10,
    heartbeatInterval: 30000
  })
  
  // 监听连接成功
  ws.on('open', () => {
    console.log('WebSocket连接成功')
    // 可以在这里发送认证信息
    ws.send({
      type: 'auth',
      token: localStorage.getItem('token')
    })
  })
  
  // 监听消息
  ws.on('message', (data) => {
    console.log('收到消息:', data)
    handleWebSocketMessage(data)
  })
  
  // 监听错误
  ws.on('error', (error) => {
    console.error('WebSocket错误:', error)
  })
  
  // 监听连接关闭
  ws.on('close', (event) => {
    console.log('WebSocket连接关闭:', event)
  })
  
  // 连接到服务器
  ws.connect('ws://localhost:8080/ws')
  
  return ws
}

// 2. 使用单例模式
export function initWebSocket() {
  const ws = getWebSocketInstance({
    url: 'ws://localhost:8080/ws',
    reconnectInterval: 5000,
    maxReconnectAttempts: 5
  })
  
  ws.on('message', handleWebSocketMessage)
  ws.connect()
  
  return ws
}

// 3. 处理WebSocket消息
function handleWebSocketMessage(data) {
  switch (data.type) {
    case 'device_status':
      console.log('设备状态更新:', data.payload)
      // 更新设备状态到Vue组件
      break
      
    case 'environment_data':
      console.log('环境数据更新:', data.payload)
      // 更新环境数据到Vue组件
      break
      
    case 'alarm':
      console.log('收到报警:', data.payload)
      // 处理报警信息
      break
      
    case 'notification':
      console.log('收到通知:', data.payload)
      // 显示通知
      break
      
    default:
      console.log('未知消息类型:', data)
  }
}

// 4. 发送设备控制命令
export function sendDeviceCommand(ws, deviceId, command) {
  if (!ws || !ws.isConnected()) {
    console.warn('WebSocket未连接，无法发送命令')
    return false
  }
  
  const message = {
    type: 'device_command',
    deviceId,
    command,
    timestamp: Date.now()
  }
  
  return ws.send(message)
}

// 5. 订阅设备数据
export function subscribeDeviceData(ws, deviceIds) {
  if (!ws || !ws.isConnected()) {
    console.warn('WebSocket未连接，无法订阅数据')
    return false
  }
  
  const message = {
    type: 'subscribe',
    devices: deviceIds,
    timestamp: Date.now()
  }
  
  return ws.send(message)
}

// ========== Vue组件中的使用示例 ==========

// 在Vue组件中使用的示例代码
export const vueComponentExample = {
  data() {
    return {
      ws: null,
      deviceList: [],
      environmentData: {}
    }
  },
  
  async mounted() {
    // 初始化WebSocket
    this.ws = initWebSocket()
    
    // 获取设备列表
    try {
      this.deviceList = await deviceAPI.getDeviceList()
    } catch (error) {
      console.error('获取设备列表失败:', error)
    }
    
    // 订阅设备数据
    if (this.ws && this.deviceList.length > 0) {
      const deviceIds = this.deviceList.map(device => device.id)
      subscribeDeviceData(this.ws, deviceIds)
    }
  },
  
  methods: {
    // 控制水泵
    async controlWaterPump(action, params) {
      try {
        await deviceAPI.controlWaterPump(action, params)
        // 同时通过WebSocket发送实时命令
        sendDeviceCommand(this.ws, 'water-pump', { action, ...params })
      } catch (error) {
        console.error('控制水泵失败:', error)
      }
    },
    
    // 控制水炮
    async controlWaterCannon(action, params) {
      try {
        await deviceAPI.controlWaterCannon(action, params)
        sendDeviceCommand(this.ws, 'water-cannon', { action, ...params })
      } catch (error) {
        console.error('控制水炮失败:', error)
      }
    },
    
    // 控制雾炮
    async controlFogCannon(action, params) {
      try {
        await deviceAPI.controlFogCannon(action, params)
        sendDeviceCommand(this.ws, 'fog-cannon', { action, ...params })
      } catch (error) {
        console.error('控制雾炮失败:', error)
      }
    }
  },
  
  beforeUnmount() {
    // 组件销毁前关闭WebSocket连接
    if (this.ws) {
      this.ws.close()
    }
  }
}