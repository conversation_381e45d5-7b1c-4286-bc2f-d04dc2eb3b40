/*
    列表，的混入方法
*/ 
export default {
    data() {
      return {
        dictKeyList:[],//字典的key值数组
        selectedList:[],
        loading:false,
        configDefault:{
            keys:"id",
            rowKey:"id",
            checkbox:true, //选择多行数据
            selection:true,     //是否需要选择
            amountToJudge:false, //需要合计
            align:"center", //文字位置
            border:true, //边框线
            operate:true,//操作按钮
            textOverflow:false, //文字超出是否隐藏
        },
      }
    },
    created(){

    },
    destroyed(){

    },
    computed: {
        // 列表返回最终的列表数据
        finalList: {
          get(){
            if(this.tableKeysList.length==0) return []
          // 是否展示列表某字段 listable  // 排序列表
          let list = this.tableKeysList.filter(x=>{ return x.listable;}).sort((Old,New)=>{return Old.sort - New.sort});
          return list; 
          },
          set(value){
            this.finalList = value
          }
          
        },
    },
    methods: {

    }
  }
  