<template>
    <div id="home" class="pr10 pt20 pl30 dataAnalysis">
        <!-- 第一个块 -->
      <el-row class="dataAnalysis_one" ref="visitsRef">
        <el-col :span="18">
          <div v-for="(item, index) in totallList" :key="index">
            <el-col :offset="1" :span="3">
              <div class="dataAnalysis_one_title">{{ item.label }}</div>
              <div class="dataAnalysis_one_data">{{ item.value }}</div>
            </el-col>
            <el-col :span="1" class="dataAnalysis_vertical"></el-col>
          </div>
        </el-col>
        <el-col :span="5" class="row dataAnalysis_one_nav auto">
          <div :class="['dataAnalysis_one_nav_item pt6 pb6', termIndex == (index + 1) ? 'on' : '']" @click="termClick(item, index + 1)"
            v-for="item, index in termList" :key="index">{{ item.name }}</div> 
        </el-col>
        <div class=" " style="margin-left: 98%;">
          <svg-icon class="mt10" :icon-class="isFullscreen?'exit-fullscreen':'fullscreen'" @click="open" />
        </div>
      </el-row>
  
      <div class="across mt10"></div>
  
      <!-- 第二个块  -->
      <el-row class="dataAnalysis_tow mt20">
        <!-- 营业额 , 运营成本 , 收入分布 -->
        <el-col :span="5" :offset="index == 0 ? 0 : 1" v-for="item, index in towCardList" :key="index"
          class="dataAnalysis_tow_item">
          <el-card shadow="never" :body-style="{ padding: '10px 20px' }">
            <div class="row-between">
              <div>
                <div class="dataAnalysis_tow_item_title">
                  {{ item.title }}
                  <span>{{ item.subTitle }}</span>
                </div>
                <div class="mt15 dataAnalysis_tow_item_money">
                  <span class="ml8">{{ item.value }}</span>
                </div>
                <div class="mt15 row">
                  <div class="row alignCenter mr8" v-for="i, index in item.subclassList" :key="index">
                    <div :style="{ background: i.color }" class="dataAnalysis_tow_item_spot mr5"></div>
                    <div class="dataAnalysis_tow_item_spotTitle">{{ i.title }}</div>
                  </div>
                </div>
              </div>
              <dataAnalysisCake v-if="item.cakeOneTitleColorList" :list="item.cakeOneTitleColorList"></dataAnalysisCake>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
  
      <div class="across mt20 mb10"></div>
      <div >
        <div v-if="xdata.length ==0" style="text-align:center;font-size: 10px;color: gray;">暂无排行，请选择其它时间范围</div>
        <div style="margin-top: 2%;" ref="generalDataChart" :style="{ width: '100%', height: dataAnalysisRightheight }"></div>  
      </div>
      
    </div>
    
  </template>
  
  <script>
  import screenfull from 'screenfull'
  import dataAnalysisCake from "@/components/index/dataAnalysisCake.vue";
  import { statisticsAll, listPanel, listRank } from "@/api/home/<USER>";
  export default {
    name: "Index",
    components: {
      dataAnalysisCake,
    },
    data() {
      return {
        // 版本号
        version: "3.8.6",
        timer:null,
        isFullscreen:false,
        // 营业额数据
        turnoverObj: {},
        rateTurnoverJudge: false,
        visitsObj: {}, //访问量
        subscribeObj: {}, //预约
        dataAnalysisRightWidth: null, //历史趋势
        dataAnalysisRightheight: null, //历史趋势
        totallList: [],
        termList: [
          { id: 1, name: "今天" },
          { id: 2, name: "本周" },
          { id: 3, name: "本月" },
          { id: 4, name: "本季度" },
          { id: 5, name: "本年度" }
        ],
        ydata: [],
        xdata: [],
        termIndex: 3,
        towCardList: [],
        // 运营成本
        rateOperatingCostsJudge: false,
        annularlist: [],
        // 波动图数据
        turnover: [], //营业额
        operationCosts: [], //运营成本
        profit: [], //利润
      };
    },
    // 轮询-
    destroyed() {
      //离开页面是销毁
      clearInterval(this.timer);
      this.timer = null;
    },
    created() {
      const height = window.innerHeight
      console.log(height)
      this.dataAnalysisRightheight = height*0.6 +'px';
      console.log(this.dataAnalysisRightheight);
   
      // 实现轮询
      this.timer = window.setInterval(() => {
        setTimeout(this.getlist(3),this.generalDataChartEvent(), 0);
      }, 60000);
    },
    mounted() {
        
      this.getlist(3);
      this.generalDataChartEvent();
      this.open()
      // this.dataAnalysisRightheight = this.$refs["dataAnalysisLeft"].$el.offsetHeight +'px';
      // console.log(this.$refs["dataAnalysisLeft"].$el.offsetWidth,this.$refs["dataAnalysisLeft"].$el.offsetHeight);
      // this.dataAnalysisRightWidth =this.$refs["dataAnalysisLeft"].$el.offsetWidth +'px';
      // this.dataAnalysisRightheight = this.$refs["dataAnalysisLeft"].$el.offsetHeight +'px';
      },
    computed: {
    },
    methods: {
      open(){
        
        const element = document.getElementById('home');//指定全屏区域元素
        if (!screenfull.isEnabled) {
          this.$message({ message: '你的浏览器不支持全屏', type: 'warning' })
          return false
        }
        screenfull.toggle(element);
            const height = window.innerHeight
      console.log(height)
      this.dataAnalysisRightheight = height*0.6 +'px';
      console.log(this.dataAnalysisRightheight);   
      },
      goTarget(href) {
        window.open(href, "_blank");
      },
      // 轮询-------------
      GetMsgNum() {
        // GetMsgNum({ userid: this.userid }).then((res) => {
        //   this.value = res.data;
  
        // });
      },
      stop() {
        clearInterval(this.timer);
        this.timer = null;
      },
      // 查数据
      getlist(type) {
        statisticsAll().then(response => {
          this.totallList = response.data
        });
        this.searchCard(type)
        this.getlistRank(type)
      },
      searchCard(date) {
        listPanel({ dateRange: date }).then(response => {
          this.towCardList = response.data.current
          this.annularlist = response.data.current
          console.log(response);
          response.data.last?.forEach((item, index) => {
            this.annularlist[index].oldValue = item.value
          })
          response.data.current.forEach((item, index) => {
            var subclassList = []
            var cakeOneTitleColorList = []
            
            if (item.itemList?.length > 0) {
              item.itemList.forEach((item2, index) => {
                cakeOneTitleColorList.push({ value: item2.value, color: item2.color, name: item2.title })
              })
              subclassList = item.itemList
  
            } else {
              cakeOneTitleColorList = [
                { value: item.value, color: item.color, name: item.title },
              ]
  
            }
  
            this.$set(this.towCardList, index, {
              ...this.towCardList[index], cakeOneTitleColorList, subclassList
            });
          })
        });
      },
      getlistRank(date) {
        this.ydata = []
        this.xdata = []
        listRank({ dateRange: date }).then(res => {
          console.log(res);
          res.data?.forEach(item => {
            this.ydata.unshift(item.label)
            this.xdata.unshift(item.value)
          })
          this.generalDataChartEvent()
        })
      },
      // 第一个块 的 期限选择
      termClick(obj, index) {
        this.termIndex = index;
        this.searchCard(obj.id)
        this.getlistRank(obj.id)
      },
      generalDataChartEvent() {
        var chartDom = this.$refs["generalDataChart"];
        var myChart = this.echarts.init(chartDom);
  
        let ydata = this.ydata
        let xdata = this.xdata
  
        var option = {
          color: ['#1890FF'],
          textStyle: {
            fontSize: 14,
            fontStyle: 'normal',
            fontWeight: 'bold',
          },
          tooltip: {
            trigger: "axis",
          },
  
          title: {
            left: "left",
            text: "报工排行"
          },
          grid: {
            top: '5%',
            left: '1%',
            right: '4%',
            bottom: '16%',
            containLabel: true,
            borderWidth: 0,
            y: 80,
            y2: 60
          },
          xAxis: {
            type: 'value',
            fontSize:'50px',
            // max:50,
            axisLine: {
              show: true,
              lineStyle: {
                color: '#fefef',
              },
            },
            axisLabel: {
              show: true,
              interval: 0,
              formatter: '{value}' // 给每个数值添加%
            }
          },
          yAxis: {
            axisLine: {
              show: true,
              lineStyle: {
                color: '#fefef',
              },
            },
            axisLabel: {
              fontSize:20
            }, 
            type: 'category',
            data: this.ydata
          },
          series: [
            {
              itemStyle: {
                normal: {
                  color: function (params) {
                    // build a color map as your need.
                    var colorList = [
                      '#C1232B',
                      '#B5C334',
                      '#FCCE10',
                      '#F0805A',
                      '#E87C25',
                      '#27727B',
                      '#FE8463',
                      '#9BCA63',
                      '#FAD860',
                      '#F3A43B',
                      '#60C0DD',
                      '#D7504B',
                      '#C6E579',
                      '#F4E001',
                      '#F0805A',
                      '#26C0C0'
                    ];
                    return colorList[params.dataIndex];
                  },
                  label: {
                    show: true,
                    position: 'right',
                    formatter: '{c}' //这是关键，在需要的地方加上就行了
                  },
                  //设置柱子圆角
                  barBorderRadius: [0, 20, 20, 0]
                },
                backgroundStyle: {
                  color: '#EBEEF5'
                }
              },
              data: this.xdata,
              type: 'bar',
              barWidth: 30 //柱图宽度
            }
          ]
        };
  
        myChart.setOption(option);
      }
    }
  
  };
  </script>
  
  <style lang="scss" scoped>
  #home{
    background: #fff;
    height: 100%;
    width: 100%;
  }
  .dataAnalysis {
    user-select: none;
  
    .across {
      width: 100%;
      height: 1px;
      background: #f0f0f0;
    }
  
    &_vertical {
      height: 50px;
      width: 1px;
      background: #b9b8b6;
    }
  
    // 第一个块
    .dataAnalysis_one {
      &_title {
        color: #b1b3d2;
        font-size: 14px;
      }
  
      &_data {
        font-size: 25px;
      }
  
      .dataAnalysis_one_nav {
        margin-top: 10px;
  
        .on {
          background: linear-gradient(to right, #81b8f8, #0278fd);
          border-radius: 30px;
          color: #fffefb;
        }
  
        &_item {
          width: 100px;
          text-align: center;
          cursor: pointer;
        }
      }
    }
  
    // 第二个块
    .dataAnalysis_tow {
      cursor: pointer;
  
      &_item {
        min-width: 250px;
  
        &_title {
          font-size: 16px;
  
          span {
            color: #bfc1d8;
            font-size: 10px;
          }
        }
  
        &_money {
          font-size: 22px;
  
          span {
            font-size: 35px;
          }
        }
  
        &_spot {
          width: 8px;
          height: 8px;
          background: red;
          border-radius: 50%;
        }
  
        &_spotTitle {
          font-size: 12px;
          color: rgb(189, 189, 189);
        }
      }
    }
  
    // 第三个块
    .dataAnalysis_three {
      color: #fffff8;
      background: #0278fd;
  
      &_left {
        &_one {
          width: 100%;
          height: 160px;
          background: #4577ee;
          border-radius: 10px;
          box-sizing: border-box;
  
          &_rate {
            font-size: 45px;
          }
        }
  
        &_tow {
          width: 450px;
          height: 180px;
          background: #4577ee;
          border-radius: 10px;
          box-sizing: border-box;
        }
      }
    }
  }
  </style>
  
  