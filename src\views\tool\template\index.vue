<template>
    <div class="app-container">
        <!-- 搜索过滤条件 -->
        <searchForm @change="searchFormChange" :searchList.sync="queryList" :keys="url"
            @scopedSlotsEmit="scopedSlotsEmit" @getList="getList" :dicts="dicts" :formItemNum="formItemNum"
            :defaultQueryKeys="defaultQueryKeys" :parentName="$options.name">
            <template #left>
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                    v-hasPermi="['system:demo:add']">新增</el-button>
                <el-button type="success" plain icon="el-icon-edit" append :disabled="single" size="mini"
                    @click="handleUpdate" v-hasPermi="['system:demo:edit']">修改</el-button>
                <el-button type="danger" plain icon="el-icon-delete" append size="mini" :disabled="multiple"
                    @click="handleDelete" v-hasPermi="['system:demo:remove']">删除</el-button>
                <el-dropdown trigger="click" style="margin: 0 10px;">
                    <el-button type="warning" plain icon="el-icon-download" size="mini" class="el-dropdown-link"
                        v-hasPermi="['system:demo:export']">导出</el-button>
                    <el-dropdown-menu slot="dropdown" class="dropdownMenu">
                        <el-dropdown-item class="clearfix">
                            <div @click="handleExport">全部导出</div>
                        </el-dropdown-item>
                        <el-dropdown-item class="clearfix">
                            <div @click="handleExportForIds">选中导出</div>
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </template>
  
        </searchForm>

        <!-- 表格 -->
        <defineTable :dicts="dicts" :tableKeysList="queryList" :parentName="$options.name"
            @selection-change="handleSelectionChange" :tableDataList.sync="tableDataList" :config="tableConfig">
            <!-- 右击选项窗口--默认开启，传了插槽就会关闭-->
            <!-- <template #rightOption="{ row }">
                <el-link size="mini" icon="el-icon-document-copy" @click="copySelectedUnits(row)">复制选中的单元</el-link>
                <el-link size="mini" icon="el-icon-document-delete" @click="uncheck(row)">取消行</el-link>
            </template> -->
            <template #id="{ row }">
                <el-tooltip class="item" effect="dark" content="查看详情" placement="right">
                    <el-link type="success" @click="goDetail(row)">{{ row.id }}</el-link>
                </el-tooltip>
            </template>
            <template #image="{ row }">
                <el-link size="mini" type="primary" :disabled="!row.image" @click="annexEvent(row.image)">查看
                    ({{ row.image ? row.image.split(",").length : 0 }})</el-link>
            </template>
            <template #image2="{ row }">
                <el-link size="mini" type="primary" :disabled="!row.image2" @click="annexEvent(row.image2)">查看
                    ({{ row.image2 ? row.image2.length : 0 }})</el-link>
            </template> 
        </defineTable>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改demo对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body :close-on-click-modal="false">
            <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入名称" />
                </el-form-item>
                <el-form-item label="编号" prop="code">
                    <el-input v-model="form.code" placeholder="请输入编号" />
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
                </el-form-item>
                <!-- 普通字典下拉 -->
                <el-form-item label="通知类型" prop="noticeType">
                    <el-select v-model="form.noticeType" placeholder="请选择通知类型">
                        <el-option v-for="dict in dicts.lx_demo_notice_type" :key="dict.value" :label="dict.label"
                            :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="搜索下拉" prop="customerId">
                    <!-- 远程搜索下拉选择 -->
                    <el-select v-model="form.customerId" filterable remote reserve-keyword placeholder="请输入客户名称"
                        :remote-method="remoteMethod" :loading="remoteData.selectLoading" @change="selectChange"
                        value-key="id">
                        <el-option v-for="item in remoteData.options" :key="item.id" :label="item.title"
                            :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="文件上传组件（字符串）" required>
                    <fileUpload v-model="form.image" uploadType="file" :limit="5" valueType="String" key="file"
                        :fileType="['png', 'jpg', 'jpeg', 'doc', 'xls', 'ppt', 'txt', 'pdf', 'docx']"
                        @delete="deleteOss" />
                </el-form-item>
                <el-form-item label="文件上传组件（数组）" required>
                    <fileUpload v-model="form.image2" uploadType="image" :limit="5" valueType="Array" key="image"
                        :fileType="['png', 'jpg', 'jpeg']" @delete="deleteOss" />
                </el-form-item>

                <el-form-item label="弹窗选择(多选)" v-if="open">

                    <MultipleDialog :getData="getData" @backData="backData" keyWord="subDemoList" />
                </el-form-item>

                <el-form-item label="弹窗选择(单选)" v-if="open">
                    <SingleDialog :getData="getDataOne" @backData="backData" keyWord="subDemo" />
                </el-form-item>

            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" :loading="loading" @click="submitForm">确 定</el-button>
                <el-button @click="cancel" :loading="loading">取 消</el-button>
            </div>
        </el-dialog>
        <!-- 图片查看 -->
        <lookImage :show.sync="imageShow" v-model="imageAnnex" :fileDialog.sync="fileShow"></lookImage>


    </div>
</template>

<script>
import pageMixins from "@/components/A_custom/mixins/page.js"
import rightOptionMixins from "@/components/A_custom/mixins/rightOption.js"
import { listDemo, getDemo, delDemo, addDemo, updateDemo, listBySelector } from "@/api/system/demo";
import userTable from "@/views/tool/template/demo/index.vue";
import MultipleDialog from "@/components/multipleDialog/index.vue"
import SingleDialog from "@/components/singleDialog/index.vue"
export default {
    mixins: [pageMixins, rightOptionMixins],
    name: "Demo",
    components: {
        userTable, MultipleDialog, SingleDialog
    },
    data() {
        return {
            //弹窗选择控制参数
            tableDialog: {
                dialogShow: false,
                name: '',
                tableList: []
            },
            //调存储桶删除文件列表
            deleteFileList: [],

            //远程搜索下拉选择参数
            remoteData: {
                selectLoading: false,
                options: [],
            },
            formItemNum: 4, //默认搜索展示多少个条件
            // ——————————————————————————————————————————————————————————————————————————————
            // 默认查询参数
            defaultQueryKeys: {
                //默认条件查询
                // name: {
                //     value: 'E',
                //     resetable: true  // 是否可冲重置，默认为否
                // }
            },
            //页面进来调接口的默认参数
            tableListApi: listDemo, // 发送接口请求
            url: "tool/template",
            tableConfig: {
                butStatus: {
                    "修改": (row = {}) => { return true },
                    "删除": (row = {}) => { return false },
                },
                rowKey: "id",
                // rowKey:"id",
                // checkbox:true, //选择多行数据
                // selection:true,     //是否需要选择
                // amountToJudge:false, //需要合计
                // align:"center", //文字位置
                // border:true, //边框线
                // textOverflow:false, //文字超出是否隐藏
                // checkboxDisable: (row) => { return true;}        // 判断行是否允许选择
            },
            // ——————————————————————————————————————————————————————————————————————————————
            // demo表格数据
            queryList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 表单参数
            form: {},
            // 表单校验
            rules: {
            },
        };
    },
    methods: {
        // 触发刷新
        // updataForm() {
        //     this.$refs.searchForm2Ref.upDataSearchListComputed()
        // },
        //远程搜索下拉选择方法
        remoteMethod(value) {
            if (value !== '') {
                let queryParams = {
                    pageNum: 1,
                    pageSize: 10,
                    key: value //用户输入关键字
                }
                this.remoteData.selectLoading = true;
                setTimeout(() => {
                    //后端查询接口
                    listBySelector(queryParams).then(res => {
                        this.remoteData.selectLoading = false;
                        this.remoteData.options = res.rows
                    })
                }, 200);
            } else {
                this.remoteData.options = [];
            }
        },
        //远程搜索下拉选择改变事件
        selectChange(e) {
            console.log(e);
        },
        //记录要删除的存储桶文件
        deleteOss(list) {
            this.deleteFileList = list
        },
        //存储桶删除文件
        deleteOssFile() {
            if (this.deleteFileList.length > 0) {
                this.deleteFileList.forEach(it => {
                    this.deleteFile(it)
                })
            }
        },
        //查看详情
        goDetail(row) {
            let routeUrl = this.$router.resolve({ path: "/demoDetail", query: { id: row.id } })
            window.open(routeUrl.href, '_blank')
        },
        // 操作事件
        commandDropdownClick(key, row) {
            switch (key) {
                case "修改": this.handleUpdate(row); break;
                case "删除": this.handleDelete(row); break;
            }
        },
        // 选中列表返回事件
        selectionReturnChange(selection) {
            /* 不能重名；ids,single,multiple,selectedList */
        },
        // 取消按钮
        cancel() {
            //删除取消的文件
            this.deleteOssFile()
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                name: null,
                code: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null,
                remark: null, // 单选
                popUp: null, // 双选
                customerId: null,
                image: null,
                nameList: null, // 双选数组名字
                subDemoList: [], // 双选数组对象
                subDemo: null, // 单选的整个对象
            };
            this.resetForm("form");
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加demo";
        },

        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids
            getDemo(id).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改demo";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        updateDemo(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addDemo(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                    //删除取消的文件
                    this.deleteOssFile()
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id ? [row.id] : this.ids;
            let name = this.deleteName(ids);
            this.$modal.confirm('是否确认删除demo编号为"' + name + '"的数据项？').then(function () {
                return delDemo(ids);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => { });
        },
        //删除行的提示名
        deleteName(ids) {
            let resultMap = {};
            this.tableDataList.forEach((item, index) => {
                resultMap[item.id] = item;
            });
            return ids.map(id => resultMap[id].code).join(',')
        },
        /** 导出按钮操作--根据筛选 */
        handleExport() {
            console.log('执行了')
            this.queryParams.columnList = JSON.stringify(this.queryList.filter(x => { return (x.value && x.isDefault && x.selectable) }))
            this.download('system/demo/export', {
                ...this.queryParams
            }, `demo_${new Date().getTime()}.xlsx`)
        },
        /** 导出按钮操作--根据选中的id */
        handleExportForIds() {
            let ids = this.selectedList.map(item => item.id).join(',')
            if (ids) {
                this.download('system/demo/exportbyid', { ids }, `demo_${new Date().getTime()}.xlsx`)
            } else {
                this.$message.warning(`请选择需要导出的数据`);
                return
            }
        },

        getData() {
            return {
                title: '用户管理列表(多选)', // 标题
                interface: listDemo, // 列表数据请求接口
                configUrl: 'order/template/demo', // config.js文件路径
                formInfo: this.form
            }
        },
        getDataOne() {
            return {
                title: '用户管理列表(单选)',
                interface: listDemo,
                // queryList: fieldList.filter(item => item.listable == true).sort((a, b) => {
                //     return a.sort - b.sort;
                // }),
                configUrl: 'order/template/demo',
                formInfo: this.form
            }
        },

        // 子组件返回的数据
        backData(data, isOneSelect) {
            if (isOneSelect) { // 单选
                //单选处理数据
                this.form.remark = data.id?.toString()
                // 判断是不是空对象
                if (Object.keys(data).length) {
                    this.form.subDemo = data
                } else {
                    this.form.subDemo = null
                }
            } else { // 多选
                //多选处理数据
                this.form.popUp = data.map(item => item.id)
                this.form.nameList = data.map(item => item.name).join(',')
                this.form.subDemoList = data
            }
        }
    }
};
</script>

<style lang="scss">
.clearfix {
    padding: 0;

    div {
        width: 100%;
        padding: 0 14px;
    }
}

.clearfix:hover {
    background: #fff8e6;
    color: #ffba00;
}
</style>