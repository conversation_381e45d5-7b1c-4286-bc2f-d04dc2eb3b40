// 输入框混入
export const inputMixin = {
    props:{
        value:{
            type:[String,Array,Date,Number,Object],
        },
        size:{
            type:[String],
            default:()=>{
                return "mini"
            }
        },
        config:{
            type:Object,
            default:()=>{return {
                data:{},//数据值
                multiple:false,//是否多选
                disabled:false,//禁用
                range:false,//范围
            }}
        },
        placeholder:{
            type:String,
            default:()=>{
                return "请输入内容"
            }
        }
    },
    data(){
        return{

        }
    },
    methods: {
        inputClean(){ // 当选择某个操作符时候就清空 内容值
            this.$emit('input',undefined);
            let { selectType } = this.config.data
            if(selectType==5){
                // 弹框
                this.$refs.baseFrame.toggleSelectionAll(); //情况弹框之前选择的数据
            }
            if(selectType==4||selectType==3){
                //时间//数字
                this.rangeFront=undefined;
                this.rangeAfter=undefined;
            }
        }
    }
}
