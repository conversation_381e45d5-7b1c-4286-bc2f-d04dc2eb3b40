from ast import mod
from operator import add
from pydoc import cli
from pymodbus.client import ModbusTcpClient
from pymodbus.payload import BinaryPayloadDecoder, BinaryPayloadBuilder
from pymodbus.constants import Endian
import time
import re
import traceback

class ModbusTCPUtils:
    def __init__(self, ip, port=502, timeout=10, 
                 byte_order=Endian.BIG, word_order=Endian.BIG):
        """
        初始化Modbus客户端
        :param ip: 设备IP地址
        :param port: 端口号（默认502）
        :param timeout: 超时时间
        :param byte_order: 字节序（Endian.BIG/Endian.LITTLE，默认大端）
        :param word_order: 字序（Endian.BIG/Endian.LITTLE，默认大端）
        """
        self.client = ModbusTcpClient(ip, port=port, timeout=timeout)
        self.connected = False
        self.byte_order = byte_order  # 字节高低位配置
        self.word_order = word_order  # 字高低位配置

    def connect(self):
        """连接到Modbus服务器"""
        if not self.connected:
            self.connected = self.client.connect()
        return self.connected

    def close(self):
        """关闭连接"""
        if self.connected:
            self.client.close()
            self.connected = False

    def _parse_coil_address(self, address):
        """
        解析线圈地址 (Vxxxxx.x 格式)
        :param address: 地址字符串，如 "V4000.0"
        :return: 解析后的地址整数值
        """
        # 验证地址格式
        if not re.match(r'^V\d+\.\d+$', address):
            raise ValueError(f"线圈地址格式错误: {address}，应为 Vxxxxx.x 格式")
            
        # 地址转换：V4000.0 -> 线圈地址4000*8 + 0 = 32000
        parts = address[1:].split('.')
        return int(parts[0]) * 8 + int(parts[1])

    def _parse_register_address(self, address):
        """
        解析寄存器地址 (VDxxxxx 格式)
        :param address: 地址字符串，如 "VD4004"
        :return: 解析后的地址整数值
        """
        # 验证地址格式
        if not re.match(r'^VD\d+$', address):
            raise ValueError(f"寄存器地址格式错误: {address}，应为 VDxxxxx 格式")
            
        # 地址转换：VD4004 -> 寄存器起始地址4004
        return int(address[2:])

    def read_coil(self, address, count=1, unit=1):
        """读取开关量（线圈）- 对应文档中Vxxxxx.x类型地址"""
        if not self.connected:
            return None, "未建立连接"
        try:
            # 解析地址
            # coil_addr = self._parse_coil_address(address)
            coil_addr = address
            response = self.client.read_coils(address=coil_addr, count=count, slave=unit)
            if response.isError():
                raise Exception(f"读取失败: {response}")
                return None, f"读取失败: {response}"
            return response.bits, None
        except ValueError as e:
            traceback.print_exc()
            return None, f"地址格式错误: {str(e)}"
        except Exception as e:
            traceback.print_exc()
            return None, f"异常: {str(e)}"

    def write_coil(self, address, value, unit=1):
        """写入开关量（线圈）- 对应文档中Vxxxxx.x类型地址"""
        if not self.connected:
            return False, "未建立连接"
        try:
            # 解析地址
            coil_addr = self._parse_coil_address(address)
            response = self.client.write_coil(address=coil_addr, value=value, slave=unit)
            if response.isError():
                return False, f"写入失败: {response}"
            return True, "写入成功"
        except ValueError as e:
            traceback.print_exc()
            return False, f"地址格式错误: {str(e)}"
        except Exception as e:
            traceback.print_exc()
            return False, f"异常: {str(e)}"

    def read_holding_register_float(self, address, unit=1):
        """读取浮点数（保持寄存器）- 对应文档中VDxxxxx类型地址"""
        if not self.connected:
            return None, "未建立连接"
        try:
            # 解析地址
            reg_addr = self._parse_register_address(address)
            response = self.client.read_holding_registers(address=reg_addr, count=2, slave=unit)  # 浮点数占2个寄存器
            if response.isError():
                raise Exception(f"读取失败: {response}")
                return None, f"读取失败: {response}"
            # 根据配置解析32位浮点数（支持高低位反转）
            decoder = BinaryPayloadDecoder.fromRegisters(
                response.registers, 
                byteorder=self.byte_order, 
                wordorder=self.word_order
            )
            value = decoder.decode_32bit_float()
            return value, None
        except ValueError as e:
            traceback.print_exc()
            return None, f"地址格式错误: {str(e)}"
        except Exception as e:
            traceback.print_exc()
            return None, f"异常: {str(e)}"

    def read_holding_register_int16(self, address, unit=1):
        """读取16位整数（保持寄存器）- 对应文档中VDxxxxx类型地址"""
        if not self.connected:
            return None, "未建立连接"
        try:
            # 解析地址
            # reg_addr = self._parse_register_address(address)
            reg_addr = address
            response = self.client.read_holding_registers(address=reg_addr, count=1, slave=unit)  # 16位整数占1个寄存器
            if response.isError():
                raise Exception(f"读取失败: {response}")
                return None, f"读取失败: {response}"
            # 根据配置解析16位整数（支持高低位反转）
            decoder = BinaryPayloadDecoder.fromRegisters(
                response.registers, 
                byteorder=self.byte_order, 
                wordorder=self.word_order
            )
            value = decoder.decode_16bit_int()
            return value
        except ValueError as e:
            traceback.print_exc()
            return None, f"地址格式错误: {str(e)}"
        except Exception as e:
            traceback.print_exc()
            return None, f"异常: {str(e)}"

    def read_holding_register_int32(self, address, unit=1):
        """读取32位整数（保持寄存器）- 对应文档中VDxxxxx类型地址"""
        if not self.connected:
            return None, "未建立连接"
        try:
            # 解析地址
            # reg_addr = self._parse_register_address(address)
            reg_addr = address
            response = self.client.read_holding_registers(address=reg_addr, count=2, slave=unit)  # 32位整数占2个寄存器
            if response.isError():
                raise Exception(f"读取失败: {response}")
                return None, f"读取失败: {response}"
            # 根据配置解析32位整数（支持高低位反转）
            decoder = BinaryPayloadDecoder.fromRegisters(
                response.registers, 
                byteorder=self.byte_order, 
                wordorder=self.word_order
            )
            value = decoder.decode_32bit_int()
            return value
        except ValueError as e:
            traceback.print_exc()
            return None, f"地址格式错误: {str(e)}"
        except Exception as e:
            traceback.print_exc()
            return None, f"异常: {str(e)}"

    def write_holding_register_int16(self, address, value, unit=1):
        """写入整数（保持寄存器）- 对应文档中VDxxxxx类型地址"""
        if not self.connected:
            return False, "未建立连接"
        try:
            # 解析地址
            # reg_addr = self._parse_register_address(address)
            reg_addr = address
            builder = BinaryPayloadBuilder(
                byteorder=self.byte_order, 
                wordorder=self.word_order
            )
            builder.add_16bit_int(value)  # 整数占1个寄存器
            payload = builder.to_registers()
            response = self.client.write_register(address=reg_addr, value=value, slave=unit)
            if response.isError():
                return False, f"写入失败: {response}"
            return True, "写入成功"
        except ValueError as e:
            traceback.print_exc()
            return False, f"地址格式错误: {str(e)}"
        except Exception as e:
            traceback.print_exc()
            return False, f"异常: {str(e)}"

    def write_holding_register_int32(self, address, value, unit=1):
        """写入32位整数（保持寄存器）- 对应文档中VDxxxxx类型地址"""
        if not self.connected:
            return False, "未建立连接"
        try:
            # 解析地址
            reg_addr = self._parse_register_address(address)
            builder = BinaryPayloadBuilder(
                byteorder=self.byte_order, 
                wordorder=self.word_order
            )
            builder.add_32bit_int(value)  # 32位整数占2个寄存器
            payload = builder.to_registers()
            response = self.client.write_holding_registers(address=reg_addr, values=payload, slave=unit)
            if response.isError():
                return False, f"写入失败: {response}"
            return True, "写入成功"
        except ValueError as e:
            traceback.print_exc()
            return False, f"地址格式错误: {str(e)}"
        except Exception as e:
            traceback.print_exc()
            return False, f"异常: {str(e)}"

    def write_holding_register_float(self, address, value, unit=1):
        """新增：写入浮点数（保持寄存器）- 扩展功能"""
        if not self.connected:
            return False, "未建立连接"
        try:
            # 解析地址
            reg_addr = self._parse_register_address(address)
            builder = BinaryPayloadBuilder(
                byteorder=self.byte_order, 
                wordorder=self.word_order
            )
            builder.add_32bit_float(value)  # 浮点数占2个寄存器
            payload = builder.to_registers()
            response = self.client.write_holding_registers(address=reg_addr, values=payload, slave=unit)
            if response.isError():
                return False, f"写入失败: {response}"
            return True, "写入成功"
        except ValueError as e:
            traceback.print_exc()
            return False, f"地址格式错误: {str(e)}"
        except Exception as e:
            traceback.print_exc()
            return False, f"异常: {str(e)}"


if __name__ == "__main__":
    modbus = ModbusTCPUtils('*************')
    modbus.connect()
    res = modbus.read_holding_register_int16(3)
    print(res)
    print(bin(res))
    modbus.close()
