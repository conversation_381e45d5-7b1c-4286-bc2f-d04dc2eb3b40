# device_control_debugger.py (pymodbus v3, with <PERSON>st Cannon)
# -*- coding: utf-8 -*-

"""
设备控制调试脚本
功能: 通过 Modbus TCP 协议连接并控制水炮、雾炮和水泵设备。
依赖: pymodbus (请先通过 pip install pymodbus 安装)
"""

import time
import struct
from pymodbus.client import ModbusTcpClient
from pymodbus.payload import BinaryPayloadDecoder
from pymodbus.constants import <PERSON>ian
from pymodbus.exceptions import ModbusException

# --- 地址映射说明 ---
# V-Bit (e.g., V4000.0) -> Coil Address (V-Word * 8 + V-Bit)
# VD (e.g., VD4004) -> Holding Register Address (VD Address)
# Water Pump Registers -> Holding Register Address (Address - 1 for 0-based)

# --- 水炮地址常量 ---
WC_COILS_BASE = {
    "pump_running": 4000 * 8 + 0, "h_reverse": 4000 * 8 + 1, "h_forward": 4000 * 8 + 2,
    "v_reverse": 4000 * 8 + 3, "v_forward": 4000 * 8 + 4, "far_shot": 4000 * 8 + 5,
    "sprinkler": 4000 * 8 + 6, "remote_mode": 4000 * 8 + 7, "pump_linkage": 4001 * 8 + 0,
    "v_cruise_linkage": 4001 * 8 + 1, "fault": 4001 * 8 + 2,
    "cruise_spray": 4050 * 8 + 0, "fault_reset": 4050 * 8 + 1,
}
WC_REGS_BASE = {
    "h_angle": 4004, "v_angle": 4008, "h_cruise_low": 4012, "h_cruise_high": 4016,
    "v_cruise_low": 4020, "v_cruise_high": 4024, "cruise_run_time": 4054,
    "cruise_interval_time": 4056,
}

# --- 雾炮地址常量 ---
MC_COILS_BASE = {
    "fan_running": 4006 * 8 + 0, "pump_running": 4006 * 8 + 1, "rising": 4006 * 8 + 2,
    "falling": 4006 * 8 + 3, "rotating_left": 4006 * 8 + 4, "rotating_right": 4006 * 8 + 5,
    "manual_mode": 4006 * 8 + 6, "remote_controller_mode": 4006 * 8 + 7,
    "remote_mode": 4007 * 8 + 0, "fault": 4007 * 8 + 1, "rc_running": 4007 * 8 + 2,
    "water_shortage_fault": 4007 * 8 + 3, # 读写
    "spray_control": 4000 * 8 + 0, # 写
    "spray_mode_select": 4002 * 8 + 0, # 写 (1摇摆, 0定点)
    "set_zero_position": 4016 * 8 + 0, # 写
    "fault_reset": 4004 * 8 + 0, # 写
}
MC_REGS_BASE = {
    "current_rotation_pos": 4026, "current_pitch_pos": 4020,
    "fixed_rotation_angle": 4038, "fixed_pitch_angle": 4042,
    "swing_start_angle": 4030, "swing_end_angle": 4034,
    "left_limit_set": 4046, "right_limit_set": 4052, "pulses_per_revolution": 4056,
}

# --- 水泵地址常量 (0-based) ---
WP_REGS_BASE = {
    "output_freq": 0, "output_current": 1, "output_voltage": 2, "output_power": 3,
    "current_pressure": 4, "set_pressure": 5, "run_control": 6, "fault_type": 7,
}

class DeviceController:
    """Modbus设备控制器基类"""
    def __init__(self, host, port=502, unit_id=1):
        self.host = host
        self.port = port
        self.unit_id = unit_id
        self.client = ModbusTcpClient(host, port=port)

    def read_float(self, address):
        try:
            rr = self.client.read_holding_registers(address, 2, slave=self.unit_id)
            if rr.isError(): return None
            decoder = BinaryPayloadDecoder.fromRegisters(rr.registers, byteorder=Endian.BIG, wordorder=Endian.BIG)
            return decoder.decode_32bit_float()
        except ModbusException: return None

    def write_float(self, address, value):
        try:
            packed_bytes = struct.pack('>f', value)
            payload = struct.unpack('>HH', packed_bytes)
            wr = self.client.write_registers(address, list(payload), slave=self.unit_id)
            return not wr.isError()
        except ModbusException: return False

    def read_int32(self, address):
        try:
            rr = self.client.read_holding_registers(address, 2, slave=self.unit_id)
            if rr.isError(): return None
            decoder = BinaryPayloadDecoder.fromRegisters(rr.registers, byteorder=Endian.BIG, wordorder=Endian.BIG)
            return decoder.decode_32bit_int()
        except ModbusException: return None
    
    def write_int32(self, address, value):
        try:
            packed_bytes = struct.pack('>i', value)
            payload = struct.unpack('>HH', packed_bytes)
            wr = self.client.write_registers(address, list(payload), slave=self.unit_id)
            return not wr.isError()
        except ModbusException: return False

class WaterCannon(DeviceController):
    """水炮控制器"""
    # ... (Implementation from previous version, no changes needed)
    def __init__(self, host, port=502):
        super().__init__(host, port)
        print(f"--- 已初始化水炮控制器: {host} ---")

    def get_status(self):
        print("\n--- 读取水炮状态 ---")
        coil_addrs = list(WC_COILS_BASE.values())
        min_addr, max_addr = min(coil_addrs), max(coil_addrs)
        count = max_addr - min_addr + 1
        try:
            rr = self.client.read_coils(min_addr, count, slave=self.unit_id)
            if not rr.isError():
                for name, addr in WC_COILS_BASE.items():
                    value = rr.bits[addr - min_addr]
                    print(f"{name}: {value}")
            for name, addr in WC_REGS_BASE.items():
                val = self.read_int32(addr) if "time" in name else self.read_float(addr)
                if val is not None: print(f"{name}: {val}")
        except ModbusException as e: print(f"读取出错: {e}")
        print("--- 读取完成 ---\n")

    def set_cruise_spray(self, start=True):
        print(f"--- 设置巡航喷洒: {'启动' if start else '停止'} ---")
        self.client.write_coil(WC_COILS_BASE["cruise_spray"], start, slave=self.unit_id)

    def set_fault_reset(self):
        print("--- 故障复位 ---")
        self.client.write_coil(WC_COILS_BASE["fault_reset"], True, slave=self.unit_id)
        time.sleep(0.5)
        self.client.write_coil(WC_COILS_BASE["fault_reset"], False, slave=self.unit_id)

    def set_cruise_time(self, run_time, interval_time):
        print(f"--- 设置巡航时间: 运行 {run_time}min, 间隔 {interval_time}min ---")
        self.write_int32(WC_REGS_BASE["cruise_run_time"], run_time)
        self.write_int32(WC_REGS_BASE["cruise_interval_time"], interval_time)

class MistCannon(DeviceController):
    """雾炮控制器"""
    def __init__(self, host, port=502):
        super().__init__(host, port)
        print(f"--- 已初始化雾炮控制器: {host} ---")

    def get_status(self):
        print("\n--- 读取雾炮状态 ---")
        coil_addrs = list(MC_COILS_BASE.values())
        min_addr, max_addr = min(coil_addrs), max(coil_addrs)
        count = max_addr - min_addr + 1
        try:
            rr = self.client.read_coils(min_addr, count, slave=self.unit_id)
            if not rr.isError():
                for name, addr in MC_COILS_BASE.items():
                    value = rr.bits[addr - min_addr]
                    print(f"{name}: {value}")
            for name, addr in MC_REGS_BASE.items():
                val = self.read_int32(addr)
                if val is not None: print(f"{name}: {val}")
        except ModbusException as e: print(f"读取出错: {e}")
        print("--- 读取完成 ---\n")

    def set_spray(self, start=True):
        print(f"--- 设置喷雾: {'启动' if start else '停止'} ---")
        self.client.write_coil(MC_COILS_BASE["spray_control"], start, slave=self.unit_id)

    def set_spray_mode(self, swing=True):
        print(f"--- 设置喷射方式: {'摇摆' if swing else '定点'} ---")
        self.client.write_coil(MC_COILS_BASE["spray_mode_select"], swing, slave=self.unit_id)

    def set_fixed_angles(self, rotation, pitch):
        print(f"--- 设置定点角度: 旋转 {rotation}, 俯仰 {pitch} ---")
        self.write_int32(MC_REGS_BASE["fixed_rotation_angle"], rotation)
        self.write_int32(MC_REGS_BASE["fixed_pitch_angle"], pitch)

    def set_swing_angles(self, start, end):
        print(f"--- 设置摇摆范围: 起始 {start}, 终止 {end} ---")
        self.write_int32(MC_REGS_BASE["swing_start_angle"], start)
        self.write_int32(MC_REGS_BASE["swing_end_angle"], end)

    def fault_reset(self):
        print("--- 故障复位 ---")
        self.client.write_coil(MC_COILS_BASE["fault_reset"], True, slave=self.unit_id)
        time.sleep(0.5)
        self.client.write_coil(MC_COILS_BASE["fault_reset"], False, slave=self.unit_id)

class WaterPump(DeviceController):
    """水泵控制器"""
    # ... (Implementation from previous version, no changes needed)
    def __init__(self, host, port=502):
        super().__init__(host, port)
        print(f"--- 已初始化水泵控制器: {host} ---")

    def get_status(self):
        print("\n--- 读取水泵状态 ---")
        try:
            rr = self.client.read_holding_registers(0, len(WP_REGS_BASE), slave=self.unit_id)
            if not rr.isError():
                for name, addr in WP_REGS_BASE.items():
                    print(f"{name}: {rr.registers[addr]}")
        except ModbusException as e: print(f"读取出错: {e}")
        print("--- 读取完成 ---\n")

    def set_run_control(self, start=True):
        print(f"--- 设置水泵运行: {'启动' if start else '停止'} ---")
        self.client.write_register(WP_REGS_BASE["run_control"], 1 if start else 0, slave=self.unit_id)

    def set_pressure(self, pressure):
        print(f"--- 设置水泵压力: {pressure} ---")
        self.client.write_register(WP_REGS_BASE["set_pressure"], int(pressure), slave=self.unit_id)

def get_ip(device_name):
    return input(f"请输入 {device_name} 的IP地址: ")

def create_menu(device_class, menu_map):
    ip = get_ip(device_class.__name__)
    device = device_class(ip)
    if not device.client.connect():
        print(f"无法连接到 {ip}。请检查IP地址和网络连接。")
        return

    while True:
        print(f"\n--- {device_class.__name__} 控制菜单 ---")
        for key, (text, _) in menu_map.items(): print(f"{key}. {text}")
        print("0. 返回主菜单")
        choice = input("请选择操作: ")
        if choice == '0': break
        if choice in menu_map:
            _, func = menu_map[choice]
            func(device)
    device.client.close()

def main_menu():
    wc_menu_map = {
        '1': ("读取状态", lambda dev: dev.get_status()),
        '2': ("启动巡航", lambda dev: dev.set_cruise_spray(True)),
        '3': ("停止巡航", lambda dev: dev.set_cruise_spray(False)),
        '4': ("故障复位", lambda dev: dev.set_fault_reset()),
        '5': ("设置巡航时间", lambda dev: dev.set_cruise_time(int(input("运行时间(min): ")), int(input("间隔时间(min): ")))),
    }
    mc_menu_map = {
        '1': ("读取状态", lambda dev: dev.get_status()),
        '2': ("启动喷雾", lambda dev: dev.set_spray(True)),
        '3': ("停止喷雾", lambda dev: dev.set_spray(False)),
        '4': ("设为摇摆模式", lambda dev: dev.set_spray_mode(True)),
        '5': ("设为定点模式", lambda dev: dev.set_spray_mode(False)),
        '6': ("设置定点角度", lambda dev: dev.set_fixed_angles(int(input("旋转角度: ")), int(input("俯仰角度: ")))),
        '7': ("设置摇摆范围", lambda dev: dev.set_swing_angles(int(input("起始角度: ")), int(input("终止角度: ")))),
        '8': ("故障复位", lambda dev: dev.fault_reset()),
    }
    wp_menu_map = {
        '1': ("读取状态", lambda dev: dev.get_status()),
        '2': ("启动水泵", lambda dev: dev.set_run_control(True)),
        '3.': ("停止水泵", lambda dev: dev.set_run_control(False)),
        '4': ("设定压力", lambda dev: dev.set_pressure(int(input("目标压力: ")))),
    }

    main_menu_map = {
        '1': ("控制水炮", lambda: create_menu(WaterCannon, wc_menu_map)),
        '2': ("控制雾炮", lambda: create_menu(MistCannon, mc_menu_map)),
        '3': ("控制水泵", lambda: create_menu(WaterPump, wp_menu_map)),
    }

    print("\n==============================================")
    print("  欢迎使用设备控制调试脚本 (pymodbus v3)")
    print("==============================================")
    while True:
        print("\n--- 主菜单 ---")
        for key, (text, _) in main_menu_map.items(): print(f"{key}. {text}")
        print("q. 退出程序")
        choice = input("请选择设备类型: ")
        if choice.lower() == 'q': break
        if choice in main_menu_map:
            main_menu_map[choice][1]()

if __name__ == "__main__":
    main_menu()
