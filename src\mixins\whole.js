/*
  页面转换弹框组件配置
*/ 
export default {
    props:{
        dialogShow:{
            type:Boolean,
            default:()=>true
        },
        value:{
          type:[Array,String],
        },
    },
    data() {
      return {

      }
    },
    mounted(){
        let dom = this.$parent.$parent; //获取弹框的处理事件
        dom && this.$refs?.stepTable && this.getStepTable();
        dom && dom.searchInputSelectRows && dom.searchInputSelectRows(this.value)
    },
    methods: {
        // 添加列表事件
        getStepTable(){
            this.$refs.stepTable._events = {
              ...this.$refs.stepTable._events,
              select:[this.select],
              "select-all":[this.selectAll]
            }
            this.toggleSelection(this.value)
        },
        // 列表全选择
        selectAll(rows){
          let dom = this.$parent.$parent; //获取弹框的处理事件
          dom && dom.searchInputSelectRows && dom.searchInputSelectRows(rows)
        },
        // 列表选择
        select(selection, row){
          let dom = this.$parent.$parent; //获取弹框的处理事件
          let multiples = dom.multiples? true :false;
          if(!multiples){
              dom && dom.searchInputSelectRows && dom.searchInputSelectRows(row)
              // 清除 所有勾选项
              this.$refs.stepTable.clearSelection()
              // 当表格数据都没有被勾选的时候 就返回
              // 主要用于将当前勾选的表格状态清除
              if(selection.length == 0) return 
              this.$refs.stepTable.toggleRowSelection(row, true);
          }else{
            dom && dom.searchInputSelectRows && dom.searchInputSelectRows(selection)
          }
        },
        toggleSelection(rows) {
          if (rows) {
            rows.forEach(row => {
              this.$refs.stepTable.toggleRowSelection(row);
            });
          } else {
            this.$refs.stepTable.clearSelection();
          }
        },
        cellClass(row){
          let dom = this.$parent.$parent; //获取弹框的处理事件
          if (row.columnIndex === 0&&!dom.multiples) {          
            return 'disabledCheck'     
          }
        },
    }
}
  