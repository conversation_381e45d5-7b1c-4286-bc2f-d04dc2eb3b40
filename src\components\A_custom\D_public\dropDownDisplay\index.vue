<template>
    <!-- 下拉展示 -->
        <div class="dropDownDisplay">
            <div :class="['dropDownDisplay_titleBox']" >
                <span :class="['dropDownDisplay_title',{'downward':downward}]" @click="titleClick">{{title}}</span>
                <div v-show="downward" >
                    <slot name="title"></slot>
                    <!-- <el-button v-if="returnBut" type="warning" plain size="small"  @click="returnEvent" >返回</el-button> -->
                </div>
            </div>
            <div v-show="downward" class="dropDownDisplay_content" >
                <slot></slot>
            </div>
        </div>
    </template>
    
    <script>
        export default {
            props:{
                title:{
                    type:String,
                    default:"",
                },
                returnBut:{
                    type:Boolean,
                    default:false,
                },
            },
            data(){
                return{
                    downward:true,
                }
            },
            methods:{
                titleClick(){
                    this.downward = !this.downward;
                },
                returnEvent(){
                    this.$confirm('返回会清除所有填写内容,请确定后再返回!', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                         this.$router.go(-1) 
                    }).catch(() => {
                        console.log("取消返回")
                    });
                           
                },
            },
        }
    </script>
    
    <style lang="scss" scoped>
        .dropDownDisplay{
            // 内容
            &_content{
                box-sizing:border-box;
                padding:15px 15px;
                width:100%;
                background:#ffffff;
                border-bottom:1px solid #dcdcdc;
            }
            // 表头语言
            .dropDownDisplay_titleBox{
                padding:15px 15px;
                box-sizing:border-box;
                background:#f3f8ff;
                font-size:18px;
                border-bottom:1px solid #dcdcdc;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            &_title{
                line-height:32px;
                height:32px;
                box-sizing:border-box;
                cursor:pointer;
                &::before{
                    position: relative;
                    text-align:center;
                    width:24px;
                    height:28px;
                    top: 2px;
                    content:"⏷";
                    transform:rotate(270deg);
                    display: inline-block;
                }
            }
            .downward{
                &::before{
                    top: -2px;
                    transform:rotate(0deg);
                }
            }
        }
    </style>