<template>
    <div class="app-container">

        <!-- 搜索过滤条件 -->
        <searchForm @change="searchFormChange" :searchList.sync="queryList" :keys="url"
            @scopedSlotsEmit="scopedSlotsEmit" @getList="getList">
            <template #center>
                <searchForm2 @change="searchFormChange" :searchList.sync="queryList" :keys="url"></searchForm2>
            </template>
        </searchForm>

        <!-- 表格 -->
        <defineTable :parentName="$options.name" v-loading="loading" :dicts="dicts" :tableKeysList="queryList"
            @selection-change="handleSelectionChange" :tableDataList.sync="tableDataList" :config="tableConfig">
        </defineTable>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
            @pagination="getList" />

        <!-- 添加或修改demo对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
            <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入名称" />
                </el-form-item>
                <el-form-item label="编号" prop="code">
                    <el-input v-model="form.code" placeholder="请输入编号" />
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
                </el-form-item>
                <el-form-item label="性别" prop="gender">
                    <el-select v-model="form.gender" placeholder="请选择性别">
                        <el-option v-for="dict in dict.type.lx_demo_gender" :key="dict.value" :label="dict.label"
                            :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-select v-model="form.status" placeholder="请选择状态">
                        <el-option v-for="dict in dict.type.lx_demo_status" :key="dict.value" :label="dict.label"
                            :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="通知类型" prop="noticeType">
                    <el-select v-model="form.noticeType" placeholder="请选择通知类型">
                        <el-option v-for="dict in dict.type.lx_demo_notice_type" :key="dict.value" :label="dict.label"
                            :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import pageMixins from "@/components/A_custom/mixins/page.js"
import rightOptionMixins from "@/components/A_custom/mixins/rightOption.js"
import { listDemo, getDemo, delDemo, addDemo, updateDemo } from "@/api/system/demo";

export default {
    mixins: [pageMixins, rightOptionMixins],
    name: "Demo",
    dicts: ['lx_demo_notice_type', 'lx_demo_gender', 'lx_demo_status'],
    data() {
        return {
            // ——————————————————————————————————————————————————————————————————————————————
            // 默认查询参数
            defaultQueryKeys: {

            },
            //页面进来调接口的默认参数
            tableListApi: listDemo,
            url: "order/demo",
            tableConfig: {
                butStatus: {
                    "修改": (row = {}) => { return !row.name },
                    "删除": (row = {}) => { return !row.name },
                },
                // rowKey:"id",
                // checkbox:true, //选择多行数据
                // selection:true,     //是否需要选择
                // amountToJudge:false, //需要合计
                // align:"center", //文字位置
                // border:true, //边框线
                // textOverflow:false, //文字超出是否隐藏
                // checkboxDisable: (row) => { return true;}        // 判断行是否允许选择
            },
            // ——————————————————————————————————————————————————————————————————————————————
            // demo表格数据
            queryList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 表单参数
            form: {},
            // 表单校验
            rules: {
            }
        };
    },
    methods: {
        // 操作事件
        commandDropdownClick(key, row) {
            switch (key) {
                case "修改": this.handleUpdate(row); break;
                case "删除": this.handleDelete(row); break;
            }
        },
        // 选中列表返回事件
        selectionReturnChange(selection) {
            /* 不能重名；ids,single,multiple,selectedList */
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                name: null,
                code: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null,
                remark: null,
                gender: null,
                status: null,
                noticeType: null
            };
            this.resetForm("form");
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加demo";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids
            getDemo(id).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改demo";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        updateDemo(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addDemo(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id ? [row.id] : this.ids;
            let name = this.deleteName(ids);
            this.$modal.confirm('是否确认删除demo编号为"' + name + '"的数据项？').then(function () {
                return delDemo(ids);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => { });
        },
        //删除行的提示名
        deleteName(ids) {
            let resultMap = {};
            this.tableDataList.map((item, index) => {
                resultMap[item.id] = item;
            });
            return ids.map(id => resultMap[id].id).join(',')
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('order/demo/export', {
                ...this.queryParams
            }, `demo_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>
