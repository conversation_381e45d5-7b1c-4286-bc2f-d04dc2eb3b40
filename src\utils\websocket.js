/**
 * WebSocket工具类
 * 用于与服务端进行WebSocket通信
 */
class WebSocketClient {
  constructor(options = {}) {
    this.url = options.url || ''
    this.protocols = options.protocols || []
    this.reconnectInterval = options.reconnectInterval || 5000 // 重连间隔
    this.maxReconnectAttempts = options.maxReconnectAttempts || 5 // 最大重连次数
    this.heartbeatInterval = options.heartbeatInterval || 30000 // 心跳间隔
    this.heartbeatMessage = options.heartbeatMessage || JSON.stringify({ type: 'ping' }) // 心跳消息
    
    this.ws = null
    this.reconnectAttempts = 0
    this.reconnectTimer = null
    this.heartbeatTimer = null
    this.isManualClose = false // 是否手动关闭
    
    // 事件监听器
    this.listeners = {
      open: [],
      message: [],
      error: [],
      close: []
    }
  }
  
  /**
   * 连接WebSocket
   * @param {string} url WebSocket地址
   */
  connect(url) {
    if (url) {
      this.url = url
    }
    
    if (!this.url) {
      console.error('WebSocket URL不能为空')
      return
    }
    
    try {
      this.ws = new WebSocket(this.url, this.protocols)
      this.bindEvents()
      console.log('正在连接WebSocket:', this.url)
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      this.handleReconnect()
    }
  }
  
  /**
   * 绑定WebSocket事件
   */
  bindEvents() {
    if (!this.ws) return
    
    // 连接成功
    this.ws.onopen = (event) => {
      console.log('WebSocket连接成功')
      this.reconnectAttempts = 0
      this.isManualClose = false
      // this.startHeartbeat()
      this.emit('open', event)
    }
    
    // 接收消息
    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        
        // 处理心跳响应
        if (data.type === 'pong') {
          console.log('收到心跳响应')
          return
        }
        
        this.emit('message', data, event)
      } catch (error) {
        // 如果不是JSON格式，直接传递原始数据
        this.emit('message', event.data, event)
      }
    }
    
    // 连接错误
    this.ws.onerror = (event) => {
      console.error('WebSocket连接错误:', event)
      this.emit('error', event)
    }
    
    // 连接关闭
    this.ws.onclose = (event) => {
      console.log('WebSocket连接关闭:', event.code, event.reason)
      this.stopHeartbeat()
      this.emit('close', event)
      
      // 如果不是手动关闭，尝试重连
      if (!this.isManualClose) {
        this.handleReconnect()
      }
    }
  }
  
  /**
   * 发送消息
   * @param {string|object} message 要发送的消息
   */
  send(message) {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket未连接，无法发送消息')
      return false
    }
    
    try {
      const data = typeof message === 'string' ? message : JSON.stringify(message)
      this.ws.send(data)
      return true
    } catch (error) {
      console.error('发送WebSocket消息失败:', error)
      return false
    }
  }
  
  /**
   * 关闭连接
   * @param {number} code 关闭代码
   * @param {string} reason 关闭原因
   */
  close(code = 1000, reason = '正常关闭') {
    this.isManualClose = true
    this.stopHeartbeat()
    this.clearReconnectTimer()
    
    if (this.ws) {
      this.ws.close(code, reason)
      this.ws = null
    }
    
    console.log('WebSocket连接已关闭')
  }
  
  /**
   * 处理重连
   */
  handleReconnect() {
    if (this.isManualClose || this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('停止重连')
      return
    }
    
    this.reconnectAttempts++
    console.log(`第${this.reconnectAttempts}次重连，${this.reconnectInterval}ms后重试`)
    
    this.reconnectTimer = setTimeout(() => {
      this.connect()
    }, this.reconnectInterval)
  }
  
  /**
   * 清除重连定时器
   */
  clearReconnectTimer() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }
  
  /**
   * 开始心跳
   */
  startHeartbeat() {
    if (this.heartbeatInterval <= 0) return
    
    this.heartbeatTimer = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send(this.heartbeatMessage)
      }
    }, this.heartbeatInterval)
  }
  
  /**
   * 停止心跳
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }
  
  /**
   * 添加事件监听器
   * @param {string} event 事件名称
   * @param {function} callback 回调函数
   */
  on(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback)
    }
  }
  
  /**
   * 移除事件监听器
   * @param {string} event 事件名称
   * @param {function} callback 回调函数
   */
  off(event, callback) {
    if (this.listeners[event]) {
      const index = this.listeners[event].indexOf(callback)
      if (index > -1) {
        this.listeners[event].splice(index, 1)
      }
    }
  }
  
  /**
   * 触发事件
   * @param {string} event 事件名称
   * @param {...any} args 参数
   */
  emit(event, ...args) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => {
        try {
          callback(...args)
        } catch (error) {
          console.error(`事件监听器执行错误 [${event}]:`, error)
        }
      })
    }
  }
  
  /**
   * 获取连接状态
   */
  getReadyState() {
    if (!this.ws) return WebSocket.CLOSED
    return this.ws.readyState
  }
  
  /**
   * 获取连接状态描述
   */
  getReadyStateText() {
    const state = this.getReadyState()
    const stateMap = {
      [WebSocket.CONNECTING]: '正在连接',
      [WebSocket.OPEN]: '已连接',
      [WebSocket.CLOSING]: '正在关闭',
      [WebSocket.CLOSED]: '已关闭'
    }
    return stateMap[state] || '未知状态'
  }
  
  /**
   * 检查是否已连接
   */
  isConnected() {
    return this.ws && this.ws.readyState === WebSocket.OPEN
  }
}

// 创建单例实例
let wsInstance = null

/**
 * 获取WebSocket单例实例
 * @param {object} options 配置选项
 */
export function getWebSocketInstance(options = {}) {
  if (!wsInstance) {
    wsInstance = new WebSocketClient(options)
  }
  return wsInstance
}

/**
 * 销毁WebSocket单例实例
 */
export function destroyWebSocketInstance() {
  if (wsInstance) {
    wsInstance.close()
    wsInstance = null
  }
}

// 导出WebSocket类和常用方法
export { WebSocketClient }
export default WebSocketClient