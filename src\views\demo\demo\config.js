export let fieldList = [
    {
        id: 1,
        tableName: "yd_dict_type",
        tableAlias: "ydt",
        columnName: "dict_id",
        columnAlias: "dict_id",
        javaType: "Long",
        dbType: "bigint",
        property: "dictId",
        name: "字典主键",
        value: null,
        compType: 1,
        boClass: "YdDictType",
        voClass: "YdDictType",
        doClass: "YdDictType",
        operType: "SELECT",
        permission: null,
        sort: 1,
        dict: null,
        sysDict: null,
        isDefault: true,
        sortable: true,
        sortType: "desc",
        reflection: "aaaa",
        insertable: false,
        updatable: false,
        selectable: true,
        listable: true,
        selectType: 5,
        tableWidth: null
    },
    {
        id: 5,
        tableName: "yd_dict_type",
        tableAlias: "ydt",
        columnName: "dict_id",
        columnAlias: "dict_id",
        javaType: "Long",
        dbType: "bigint",
        property: "dictId2",
        name: "字典主键",
        value: null,
        compType: 1,
        boClass: "YdDictType",
        voClass: "YdDictType",
        doClass: "YdDictType",
        operType: "SELECT",
        permission: null,
        sort: 1,
        dict: null,
        sysDict: null,
        isDefault: false,
        sortable: true,
        sortType: "desc",
        reflection: null,
        insertable: false,
        updatable: false,
        selectable: false,
        listable: true,
        selectType: 4,
        tableWidth: null
    },
    {
        id: 2,
        tableName: "yd_dict_type",
        tableAlias: "ydt",
        columnName: "dict_name",
        columnAlias: "dict_name",
        javaType: "String",
        dbType: "varchar",
        property: "dictName",
        name: "字典名称",
        value: null,
        compType: 12,
        boClass: "YdDictType",
        voClass: "YdDictType",
        doClass: "YdDictType",
        operType: "SELECT",
        permission: null,
        sort: 1,
        dict: null,
        sysDict: null,
        isDefault: true,
        sortable: true,
        sortType: "desc",
        reflection: null,
        insertable: false,
        updatable: false,
        selectable: true,
        listable: true,
        selectType: 2,
        tableWidth: null
    },
    {
        id: 3,
        tableName: "yd_dict_type",
        tableAlias: "ydt",
        columnName: "dict_type",
        columnAlias: "dict_type",
        javaType: "String",
        dbType: "varchar",
        property: "dictType",
        name: "字典类型",
        value: null,
        compType: 12,
        boClass: "YdDictType",
        voClass: "YdDictType",
        doClass: "YdDictType",
        operType: "SELECT",
        permission: null,
        sort: 1,
        dict: null,
        sysDict: null,
        isDefault: true,
        sortable: true,
        sortType: "desc",
        reflection: null,
        insertable: false,
        updatable: false,
        selectable: true,
        listable: true,
        selectType: 1,
        tableWidth: null
    },
]