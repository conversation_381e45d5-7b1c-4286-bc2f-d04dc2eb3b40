from modbus_utils import ModbusTCPUtils
from pymodbus.constants import Endian
import time

if __name__ == "__main__":
    modbus = ModbusTCPUtils('192.168.2.103')
    modbus.connect()
    print(f"读取基础状态")
    res = modbus.read_holding_register_int16(3)
    base_status_str = bin(res)[2:].zfill(16)
    print(f"风机运行状态：{base_status_str[-1]}")
    print(f"水泵运行状态：{base_status_str[-2]}")
    modbus.write_holding_register_int16(40001, int("1", 2))
    modbus.close()
