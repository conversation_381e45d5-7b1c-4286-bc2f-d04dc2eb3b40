from itertools import count
from pymodbus.client import ModbusTcpClient
from pymodbus.exceptions import ModbusException
import struct


class WaterPumpController:
    def __init__(self, ip, port):
        self.client = ModbusTcpClient(host=ip, port=port)
        if not self.client.connect():
            raise ConnectionError(f"无法连接到Modbus服务器: {ip}:{port}")

    def __del__(self):
        self.client.close()

    def read_status(self):
        status = {}
        # 读取16位无符号二进制参数（对应文档中的输出频率、输出电流等）
        try:
            output_frequency_result = self.client.read_holding_registers(1, count=1)
            if not output_frequency_result.isError():
                status['输出频率'] = output_frequency_result.registers[0]
        except ModbusException as e:
            print(f"读取输出频率错误: {e}")

        try:
            output_current_result = self.client.read_holding_registers(2, count=1)
            if not output_current_result.isError():
                status['输出电流'] = output_current_result.registers[0]
        except ModbusException as e:
            print(f"读取输出电流错误: {e}")

        try:
            output_voltage_result = self.client.read_holding_registers(3, count=1)
            if not output_voltage_result.isError():
                status['输出电压'] = output_voltage_result.registers[0]
        except ModbusException as e:
            print(f"读取输出电压错误: {e}")

        try:
            output_power_result = self.client.read_holding_registers(4, count=1)
            if not output_power_result.isError():
                status['输出功率'] = output_power_result.registers[0]
        except ModbusException as e:
            print(f"读取输出功率错误: {e}")

        try:
            current_pressure_result = self.client.read_holding_registers(5, count=1)
            if not current_pressure_result.isError():
                status['当前压力'] = current_pressure_result.registers[0]
        except ModbusException as e:
            print(f"读取当前压力错误: {e}")

        try:
            set_pressure_result = self.client.read_holding_registers(6, count=1)
            if not set_pressure_result.isError():
                status['设定压力'] = set_pressure_result.registers[0]
        except ModbusException as e:
            print(f"读取设定压力错误: {e}")

        # 读取故障类型
        try:
            fault_type_result = self.client.read_holding_registers(int('1200H', 16), count=1)
            if not fault_type_result.isError():
                fault_code = fault_type_result.registers[0]
                fault_dict = {
                    0: "无故障",
                    1: "EEPORM读写故障",
                    2: "电流检测故障",
                    3: "电机对地短路故障",
                    4: "模块短路保护故障",
                    5: "硬件加速过电流故障",
                    6: "硬件减速过电流故障",
                    7: "硬件恒速过电流故障",
                    8: "软件加速过电流故障",
                    9: "软件减速过电流故障",
                    10: "软件恒速过电流故障",
                    12: "变频器过载故障",
                    13: "硬件加速过电压故障",
                    14: "硬件减速过电压故障",
                    15: "硬件恒速过电压故障",
                    16: "软件加速过电压故障",
                    17: "软件减速过电压故障",
                    18: "软件恒速过电压故障",
                    19: "低电压故障",
                    20: "模块过热故障",
                    24: "输出缺相故障",
                    28: "电机过载故障",
                    29: "温度断线故障",
                    30: "磁极位置检测失败"
                }
                status['故障类型'] = fault_dict.get(fault_code, "未知故障")
        except ModbusException as e:
            print(f"读取故障类型错误: {e}")

        return status

    def control_water_pump(self, operation):
        try:
            address = int('2000H', 16)
            if operation == '系统停止':
                self.client.write_register(address, 6)
            if operation == '系统运行':
                self.client.write_register(address, 1)
            elif operation == '故障复位':
                self.client.write_register(address, 7)
            else:
                raise ValueError(f"不支持的操作: {operation}")
        except ModbusException as e:
            print(f"写入操作错误: {e}")


# 示例使用
if __name__ == "__main__":
    ip = '*************'
    port = 502
    controller = WaterPumpController(ip, port)

    # 读取水泵状态
    status = controller.read_status()
    print("水泵当前状态:", status)

    # 控制水泵停止
    controller.control_water_pump('系统停止')
    print("已设置运行控制参数为1（假设1表示启动运行）")

    # 故障复位操作
    controller.control_water_pump('故障复位')
    print("已执行故障复位操作")

    status = controller.read_status()
    print("水泵当前状态:", status)