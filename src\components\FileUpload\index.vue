<template>
  <div class="upload-file">
    <el-upload multiple :auto-upload="false" :on-change="handleChange" :action="uploadFileUrl"
      :before-upload="handleBeforeUpload" :file-list="fileList" :on-success="handleUploadSuccess"
      :show-file-list="false" :headers="headers" :list-type="uploadType == 'image' ? 'picture-card' : ''"
      class="upload-file-uploader" ref="fileUpload" v-if="fileList.length < limit && !disabled">
      <i class="el-icon-plus" v-if="uploadType == 'image'"></i>
      <!-- 上传按钮 -->
      <el-button size="mini" type="primary" v-else>选取文件</el-button>
      <!-- 上传提示 -->
      <div class="el-upload__tip" slot="tip" v-if="showTip">
        请上传
        <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
        <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b> </template>
        的文件
      </div>
    </el-upload>
    <!-- 文件列表 -->
    <transition-group v-if="uploadType === 'file'" class="upload-file-list el-upload-list el-upload-list--text"
      name="el-fade-in-linear" tag="ul">
      <li class="el-upload-list__item ele-upload-list__item-content" v-for="(file, index) in fileList"
        :key="'file' + index">
        <el-link @click="openUrl(file.url)" :underline="false">
          <span class="el-icon-document upload-file_text" style="width: 250px;margin-top: 10px;height: 18px;">
            {{ decodeURI(file.name.split("/")[file.name.split("/").length - 1].split('.')) }}
          </span>
        </el-link>
        <div class="ele-upload-list__item-content-action">
          <el-link :underline="false" @click="handleDelete(index)" type="danger" v-if="!disabled">删除</el-link>
        </div>
      </li>
    </transition-group>
    <ul v-else-if="uploadType == 'image'" v-for="(item, index) in imageUrls" :key="'image' + index"
      class="el-upload-list el-upload-list--picture-card">
      <li class="el-upload-list__item is-ready" style="width: 150px;height: 150px;">
        <div>
          <img :src="item.url" class="el-upload-list__item-thumbnail">
          <span class="el-upload-list__item-actions">
            <span class="el-upload-list__item-delete" @click="openPreview(index)">
              <i class="el-icon-zoom-in" />
            </span>
            <span class="el-upload-list__item-delete" @click="handleDelete(index)" v-if="!disabled">
              <i class="el-icon-delete" />
            </span>
          </span>
        </div>
      </li>
    </ul>
    <ImagePreviewDow v-if="isPreview" :url-list="imageSrcList" :initial-index="initialIndex" @close="closeViewer"
      @switch="handleSwitch"></ImagePreviewDow>
  </div>
</template>

<script>
import request from '@/utils/request'
import { getToken } from "@/utils/auth";
import COS from 'cos-js-sdk-v5';
var cos = new COS({
  // getAuthorization 必选参数
  getAuthorization: function (options, callback) {
    // 异步获取临时密钥
    // 服务端 JS 和 PHP 例子：https://github.com/tencentyun/cos-js-sdk-v5/blob/master/server/
    // 服务端其他语言参考 COS STS SDK ：https://github.com/tencentyun/qcloud-cos-sts-sdk
    // STS 详细文档指引看：https://cloud.tencent.com/document/product/436/14048
    // 异步获取临时密钥
    request({
      url: '/system/oss/getTempCredential',
      method: 'post'
    }).then(res => {
      const { tmpSecretId, tmpSecretKey, sessionToken, startTime, expiredTime } = res.data
      callback({
        TmpSecretId: tmpSecretId,
        TmpSecretKey: tmpSecretKey,
        SecurityToken: sessionToken,
        // 建议返回服务器时间作为签名的开始时间，避免用户浏览器本地时间偏差过大导致签名错误
        StartTime: startTime, // 时间戳，单位秒，如：1580000000
        ExpiredTime: expiredTime // 时间戳，单位秒，如：1580000000
      })
    }).catch(err => {
      console.log(err)
    })
  }
})
export default {
  name: "FileUpload",
  props: {
  // 禁用
  disabled: {
      type: Boolean,
      default: false,
    },
    // 值
    value: [String, Object, Array],
    // 数量限制
    limit: {
      type: Number,
      default: 5,
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5,
    },
    //上传类型，图片（image）,文件（file）
    uploadType: {
      type: String,
      default: 'file',
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ["doc", "xls", "ppt", "txt", "pdf", 'docx', 'jpg', "png"],
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    path: {
      type: String,
      default: '/public/driver/licence/'
    },
    //返回的数据类型（‘String’，'Array'’）
    valueType: {
      type: String,
      default: 'String'
    }
  },
  data() {
    return {
      isPreview: false,
      initialIndex: 0,
      imageSrcList: [],
      number: 0,
      uploadList: [],
      baseUrl: '',
      uploadFileUrl: '', // 上传文件服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      imageUrls: [],
      deleteFileList: []
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          let temp = 1;
          // 首先将值转为数组
          const list = Array.isArray(val) ? val : this.value.split(',');
          // 然后将数组转为对象数组
          this.fileList = list.map(item => {
            if (typeof item === "string") {
              item = { name: item, url: item };
            }
            item.uid = item.uid || new Date().getTime() + temp++;
            return item;
          });
        } else {
          this.fileList = [];
          return [];
        }
        if (val && this.uploadType == 'image' && this.imageUrls.length < this.fileList.length) {
          this.imageUrls = []
          if (this.fileList.length > 0) {
            this.fileList.forEach((it, inx) => {
              this.gainCosJsSdk(decodeURI(it.url)).then(res => {
                this.imageUrls.push({
                  name: inx,
                  url: res
                })
              })
            })
          }
        }
      },
      // deep: true,
      immediate: true
    },
    fileList: {
      handler(val) {
        this.fileList = val
      },
      deep: true,
      immediate: true
    },
    uploadList: {
      handler(val) {
      },
      deep: true,
      immediate: true
    },
    imageUrls: {
      handler(val) {
        this.imageUrls = val
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize);
    },
  },
  methods: {
    //图片预览
    openPreview(index) {
      this.imageSrcList = this.imageUrls.map(item => {
        return item.url
      })
      this.initialIndex = index
      this.isPreview = true;
    },
    //关闭图片预览
    closeViewer() {
      this.isPreview = false
    },
    //切换图片
    handleSwitch(index) {
      // 在这里你可以处理图片切换的逻辑
      this.initialIndex = index;
    },
    openUrl(url) {
      this.gainCosJsSdk(decodeURI(url)).then(res => {
        window.open(res, "_blank");
      }).finally(() => {
      })
    },
    handleChange(file, fileList) {
      let flag = this.handleBeforeUpload(file);
      if (flag) {
        this.handleFileInUploading(file.raw);
        this.$modal.loading("正在上传文件，请稍候...");
        this.number++;
      }
    },
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.limit == this.fileList.length) {
        this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);
        return false
      }
      if (this.fileType) {
        const fileName = file.name.split(".");
        const fileExt = fileName[fileName.length - 1];
        const isTypeOk = this.fileType.indexOf(fileExt) >= 0;
        if (!isTypeOk) {
          this.$modal.msgError(
            `文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`
          );
          return false;
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`);
          return false;
        }
      }

      return true;
    },
    // 文件个数超出
    handleExceed() {
      console.log(this.fileList, this.uploadList, this.imageUrls, this.limit, 'handleExceed');
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);
    },
    // 上传失败
    // handleUploadError(err) {
    //   this.$modal.msgError("上传文件失败，请重试");
    //   this.$modal.closeLoading();
    // },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      if (res.code === 200) {
        this.uploadList.push({ name: res.fileName, url: res.fileName });
        this.uploadedSuccessfully();
      } else {
        this.number--;
        this.$modal.closeLoading();
        this.$modal.msgError(res.msg);
        this.$refs.fileUpload.handleRemove(file);
        this.uploadedSuccessfully();
      }
    },
    // 删除文件
    handleDelete(index) {
      this.deleteFileList.push(this.fileList[index].url)
      this.$emit('delete',this.deleteFileList)
      // this.deleteFile(this.fileList[index].url)
      this.fileList.splice(index, 1);
      if (this.uploadType === 'image') {
        this.imageUrls.splice(index, 1);
      }
      if (this.valueType == 'String') {
        this.$emit("input", this.listToString(this.fileList));
      } else {
        let arr = this.fileList.map(item => {
          return item.url
        })
        this.$emit("input", arr);
      }

    },
    // 上传结束处理
    uploadedSuccessfully() {
      console.log(this.uploadList, 'this.uploadList');
      if (this.number > 0 && this.uploadList.length === this.number) {
        this.fileList = this.fileList.concat(this.uploadList);
        this.uploadList = [];
        this.number = 0;
        if (this.valueType == 'String') {
          this.$emit("input", this.listToString(this.fileList));
        } else {
          let arr = this.fileList.map(item => {
            return item.url
          })
          this.$emit("input", arr);
        }
        this.$modal.closeLoading();
      }
    },
    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = "";
      separator = separator || ",";
      for (let i in list) {
        strs += list[i].url + separator;
      }
      return strs != "" ? strs.substr(0, strs.length - 1) : "";
    },
    handleFileInUploading(file) {
      let name = file.name.split(".")[0];
      var that = this;
      let type = file.type.split("/")[1];
      if (type == "jpeg") {
        type = "jpg";
      } else if (type == "plain") {
        type = "txt";
      } else if (type == "docx") {
        type = "docx";
      } else if (file.name.split(".")[1]) {
        type = file.name.split(".")[1];
      }
      cos.uploadFile(
        {
          Bucket: this.ossData.Bucket /* 填写自己的 bucket，必须字段 */,
          Region: this.ossData.Region /* 存储桶所在地域，必须字段 */,
          Key: that.path + name + '_' + new Date().getTime() + "." + type /* 存储在桶里的对象键（例如:1.jpg，a/b/test.txt，图片.jpg）支持中文，必须字段 */,
          Body: file, // 上传文件对象
          SliceSize:
            1024 *
            1024 *
            5 /* 触发分块上传的阈值，超过5MB使用分块上传，小于5MB使用简单上传。可自行设置，非必须 */,
          onProgress: function (progressData) {
          },
        },
        function (err, data) {
          if (err) {
            that.$modal.closeLoading();
            that.$modal.msgError("上传文件失败，请重试");
          } else {
            console.log("上传成功");
            that.uploadList.push({ name: file.name, url: data.Location });
            that.uploadedSuccessfully();
          }
        }
      );
    },
  },
};
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
}

.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  display: flex;
  flex-direction: row;
  align-items: center;
  // position: relative;
}

.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}

.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}

.upload-file_text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
