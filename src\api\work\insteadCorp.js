import request from '@/utils/request'
import { tansParams } from '@/utils/ruoyi.js'

// 查询代开发应用企业信息列表
export function listInsteadCorpPost(paging, data) {
  return request({
    url: '/work/insteadCorp/list?' + tansParams(paging),
    method: 'post',
    data: data
  })
}
export function listInsteadCorpGet(params) {
  return request({
    url: '/work/insteadCorp/list',
    method: 'get',
    params: params
  })
}

// 查询代开发应用企业信息详细
export function getInsteadCorp(id) {
  return request({
    url: '/work/insteadCorp/' + id,
    method: 'get'
  })
}

// 新增代开发应用企业信息
export function addInsteadCorp(data) {
  return request({
    url: '/work/insteadCorp',
    method: 'post',
    data: data
  })
}

// 修改代开发应用企业信息
export function updateInsteadCorp(data) {
  return request({
    url: '/work/insteadCorp',
    method: 'put',
    data: data
  })
}

// 删除代开发应用企业信息
export function delInsteadCorp(主键) {
  return request({
    url: '/work/insteadCorp/' + 主键,
    method: 'delete'
  })
}
