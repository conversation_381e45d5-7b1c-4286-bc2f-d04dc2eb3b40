 # 水泵设备 WebSocket 通信协议

 ## 1. 概述

本文档定义了客户端与服务端之间通过 WebSocket 对水泵设备进行状态查询和远程控制的通信协议。

- **WebSocket URL**: `ws://<your_server_address>/ws/device`

 ## 2. 通信格式

所有消息均为 JSON 格式，并遵循统一的 `WebSocketMessage` 结构。

```json
{
  "type": "MESSAGE_TYPE",
  "requestId": "UNIQUE_ID",
  "payload": { ... }
}  
```

- `type`: 消息类型 (string)
- `requestId`: 客户端生成的唯一请求ID (string)，用于异步匹配响应。
- `payload`: 消息载荷 (object)，具体结构取决于 `type`。

 ## 3. 消息类型

### 3.1. 客户端 -> 服务端

#### 3.1.1. 获取水泵状态 (GET_STATUS)

**说明**:
客户端发送此消息以获取指定IP地址的水泵的实时状态。

**Payload 结构**:
```json
{
  "ip": "*************",
  "deviceType": 3
}
```

- `ip` (string, required): 水泵设备的IP地址。
- `deviceType` (integer, required): 设备类型，对于水泵，此值固定为 `3`。

#### 3.1.2. 控制水泵 (CONTROL_DEVICE)

**说明**:
客户端发送此消息以控制水泵的特定功能，例如设置压力。

**Payload 结构**:
```json
{
  "ip": "*************",
  "deviceType": 3,
  "type": "set_pressure",
  "value": 5
}
```

- `ip` (string, required): 水泵设备的IP地址。
- `deviceType` (integer, required): 设备类型，固定为 `3`。
- `type` (string, required): 控制指令类型。目前支持 `set_pressure`。
- `value` (integer, required): 控制指令的值。对于 `set_pressure`，表示要设置的压力值。

### 3.2. 服务端 -> 客户端

#### 3.2.1. 水泵状态更新 (STATUS_UPDATE)

**说明**:
服务端在收到 `GET_STATUS` 请求后，返回水泵的当前状态。

**Payload 结构**:
```json
{
  "pressure": 10,
  "flow": 50
  // ... 其他状态字段
}
```

- `pressure` (integer): 当前压力值。
- `flow` (integer): 当前流量。

#### 3.2.2. 控制结果 (CONTROL_RESULT)

**说明**:
服务端在收到 `CONTROL_DEVICE` 请求后，返回操作的执行结果。

**Payload 结构**:
```json
{
  "success": true
}
```

- `success` (boolean): 指令是否执行成功。

#### 3.2.3. 错误响应 (ERROR)

**说明**:
当处理发生错误时，服务端会发送此消息。

**Payload 结构**:
```json
{
  "message": "错误信息描述"
}
```

- `message` (string): 错误的详细描述。
