from modbus_utils import ModbusTCPUtils
from pymodbus.constants import Endian
import time

class WaterCannonDebugger:
    def __init__(self, ip, byte_order=Endian.BIG, word_order=Endian.BIG):
        """
        初始化水炮调试器
        :param ip: 设备IP
        :param byte_order: 字节序（Endian.BIG/Endian.LITTLE，默认大端）
        :param word_order: 字序（Endian.BIG/Endian.LITTLE，默认大端）
        """
        # 初始化Modbus工具类，支持字节/字序配置（解决高低位反转问题）
        self.modbus = ModbusTCPUtils(
            ip=ip,
            byte_order=byte_order,
            word_order=word_order
        )
        # 状态信号地址映射
        self.status_signals = {
            "水泵运行状态": "V4000.0",
            "水平反转状态": "V4000.1",
            "水平正转状态": "V4000.2",
            "上下反转状态": "V4000.3",
            "上下正转状态": "V4000.4",
            "远射状态": "V4000.5",
            "花洒状态": "V4000.6",
            "远程运行状态": "V4000.7",
            "水泵联动状态": "V4001.0",
            "上下巡航联动": "V4001.1",
            "故障状态": "V4001.2",
            "当前水平角度": "VD4004",
            "当前上下角度": "VD4008",
            "水平巡航低限角度": "VD4012",
            "水平巡航高限角度": "VD4016",
            "上下巡航低限角度": "VD4020",
            "上下巡航高限角度": "VD4024"
        }
        # 控制信号地址映射
        self.control_signals = {
            "巡航启动停止": "V4050.0",
            "故障复位": "V4050.1",
            "巡航运行时间": "VD4054",
            "巡航间隔时间": "VD4056"
        }

    def connect(self):
        """连接设备"""
        return self.modbus.connect()

    def print_device_status(self):
        """打印设备当前状态"""
        print("\n===== 设备状态信息 =====")
        # 读取开关量状态
        for name, addr in self.status_signals.items():
            if addr.startswith("V") and "." in addr:  # 开关量
                value, err = self.modbus.read_coil(addr)
                if err:
                    print(f"{name}: 读取失败 - {err}")
                else:
                    status = "运行" if value[0] else "停止" if name == "水泵运行状态" else \
                             "反转" if value[0] else "无反转" if "反转" in name else \
                             "正转" if value[0] else "无正转" if "正转" in name else \
                             "开启" if value[0] else "关闭"
                    print(f"{name}: {status} (值: {value[0]})")
            elif addr.startswith("VD"):  # 浮点数
                value, err = self.modbus.read_holding_register_float(addr)
                if err:
                    print(f"{name}: 读取失败 - {err}")
                else:
                    print(f"{name}: {value:.2f}°")

    def reset_fault(self):
        """故障复位（发送0.5秒脉冲）"""
        print("\n===== 执行故障复位 =====")
        success, err = self.modbus.write_coil(self.control_signals["故障复位"], True)
        if not success:
            print(f"复位失败: {err}")
            return False
        time.sleep(0.5)  # 保持0.5秒高电平
        success, err = self.modbus.write_coil(self.control_signals["故障复位"], False)
        if success:
            print("复位成功")
            return True
        else:
            print(f"复位后关闭失败: {err}")
            return False

    def set_cruise_params(self, run_time, interval_time):
        """设置巡航参数"""
        print(f"\n===== 设置巡航参数 =====")
        # 设置运行时间
        success, err = self.modbus.write_holding_register_int16(
            self.control_signals["巡航运行时间"], run_time
        )
        if not success:
            print(f"运行时间设置失败: {err}")
            return False
        # 设置间隔时间
        success, err = self.modbus.write_holding_register_int16(
            self.control_signals["巡航间隔时间"], interval_time
        )
        if success:
            print(f"巡航运行时间: {run_time}分钟, 间隔时间: {interval_time}分钟 - 设置成功")
            return True
        else:
            print(f"间隔时间设置失败: {err}")
            return False

    def start_cruise(self):
        """启动巡航"""
        print("\n===== 启动巡航 =====")
        success, err = self.modbus.write_coil(self.control_signals["巡航启动停止"], True)
        if success:
            print("巡航已启动")
            return True
        else:
            print(f"启动失败: {err}")
            return False

    def stop_cruise(self):
        """停止巡航"""
        print("\n===== 停止巡航 =====")
        success, err = self.modbus.write_coil(self.control_signals["巡航启动停止"], False)
        if success:
            print("巡航已停止")
            return True
        else:
            print(f"停止失败: {err}")
            return False

    def close(self):
        """关闭连接"""
        self.modbus.close()

if __name__ == "__main__":
    # 设备配置
    DEVICE_IP = "*************"  # 控制箱IP（151/152/153）
    # 字节序/字序配置（根据设备实际情况切换）
    # 若出现数值异常（如角度为负数或极大值），尝试改为Endian.LITTLE
    BYTE_ORDER = Endian.BIG    # 字节高低位（Big=正常, Little=反转）
    WORD_ORDER = Endian.BIG    # 字高低位（Big=正常, Little=反转）
    
    debugger = WaterCannonDebugger(
        ip=DEVICE_IP,
        byte_order=BYTE_ORDER,
        word_order=WORD_ORDER
    )
    
    if not debugger.connect():
        print(f"无法连接到设备 {DEVICE_IP}")
        exit(1)
    
    try:
        # 调试流程示例
        debugger.print_device_status()  # 查看初始状态
        
        # 检查远程状态和故障状态
        remote_status, _ = debugger.modbus.read_coil(debugger.status_signals["远程运行状态"])
        fault_status, _ = debugger.modbus.read_coil(debugger.status_signals["故障状态"])
        
        if not remote_status[0]:
            print("\n警告：设备未处于远程模式，请在控制箱切换模式")
        else:
            if fault_status[0]:
                print("\n检测到故障，尝试复位...")
                debugger.reset_fault()
                time.sleep(1)
                debugger.print_device_status()  # 查看复位后状态
            
            # 设置巡航参数并启动（示例：运行5分钟，间隔2分钟）
            debugger.set_cruise_params(5, 2)
            debugger.start_cruise()
            
            # 运行10秒后停止（实际使用根据需求调整）
            time.sleep(10)
            debugger.stop_cruise()
            
            debugger.print_device_status()  # 查看最终状态
    finally:
        debugger.close()