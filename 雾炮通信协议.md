 # 雾炮设备 WebSocket 通信协议

 ## 1. 概述

本文档定义了客户端与服务端之间通过 WebSocket 对雾炮设备进行状态查询和远程控制的通信协议。

- **WebSocket URL**: `ws://<your_server_address>/ws/device`

 ## 2. 通信格式

所有消息均为 JSON 格式，并遵循统一的 `WebSocketMessage` 结构。

```json
{
  "type": "MESSAGE_TYPE",
  "requestId": "UNIQUE_ID",
  "payload": { ... }
}
```

- `type`: 消息类型 (string)
- `requestId`: 客户端生成的唯一请求ID (string)，用于异步匹配响应。
- `payload`: 消息载荷 (object)，具体结构取决于 `type`。

 ## 3. 消息类型

### 3.1. 客户端 -> 服务端

#### 3.1.1. 获取雾炮状态 (GET_STATUS)

**说明**:
客户端发送此消息以获取指定IP地址的雾炮的实时状态。

**Payload 结构**:
```json
{
  "ip": "************",
  "deviceType": 2
}
```

- `ip` (string, required): 雾炮设备的IP地址。
- `deviceType` (integer, required): 设备类型，对于雾炮，此值固定为 `2`。

#### 3.1.2. 控制雾炮 (CONTROL_DEVICE)

**说明**:
客户端发送此消息以控制雾炮的特定功能，例如喷雾、转向等。

**Payload 结构**:
```json
{
  "ip": "************",
  "deviceType": 2,
  "type": "spray_control",
  "value": 1
}
```

- `ip` (string, required): 雾炮设备的IP地址。
- `deviceType` (integer, required): 设备类型，固定为 `2`。
- `type` (string, required): 控制指令类型。例如 `spray_control`, `turn_left`, `turn_right` 等。
- `value` (integer, required): 控制指令的值。例如，对于 `spray_control`，`1` 表示开启，`0` 表示关闭。

### 3.2. 服务端 -> 客户端

#### 3.2.1. 雾炮状态更新 (STATUS_UPDATE)

**说明**:
服务端在收到 `GET_STATUS` 请求后，返回雾炮的当前状态。

**Payload 结构**:
```json
{
  "spray_on": 1,
  "angle": 90
  // ... 其他状态字段
}
```

- `spray_on` (integer): 喷雾状态 (1: 开启, 0: 关闭)。
- `angle` (integer): 当前水平角度。

#### 3.2.2. 控制结果 (CONTROL_RESULT)

**说明**:
服务端在收到 `CONTROL_DEVICE` 请求后，返回操作的执行结果。

**Payload 结构**:
```json
{
  "success": true
}
```

- `success` (boolean): 指令是否执行成功。

#### 3.2.3. 错误响应 (ERROR)

**说明**:
当处理发生错误时，服务端会发送此消息。

**Payload 结构**:
```json
{
  "message": "错误信息描述"
}
```

- `message` (string): 错误的详细描述。
