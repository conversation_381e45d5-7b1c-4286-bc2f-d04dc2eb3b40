<template>
    <!-- 调度日志 -->
    <div class="dispatchingLog_task">
        <div class="dispatchingLog_task_head">调度日志</div>
        <div class="dispatchingLog_task_search">
            <selectInput v-model="queryParams.jobGroup" @change="jobGroupChange($event)" lable="执行器" class="mr10">
                <el-option v-for="item in actuatorOptions" :key="item.id" :label="item.title" :value="item.id" />
            </selectInput>
            <selectInput v-model="queryParams.jobId" lable="任务" class="mr10">
                <el-option v-for="item, index in jobIdOptions" :key="index" :label="item.jobDesc" :value="item.id" />
            </selectInput>
            <selectInput v-model="queryParams.logStatus" lable="状态" class="mr10">
                <el-option v-for="item in logStatusOptions" :key="item.value" :label="item.name" :value="item.value" />
            </selectInput>
            <timeInput lable="调度时间" v-model="queryParams.filterTimeList"></timeInput>
        </div>
        <div class="">
            <div class="dispatchingLog_task_tableHead">
                <div>
                    <el-button size="mini" @click="clearEvent">清理</el-button>
                    <el-button size="mini" type="primary" @click="getList">搜索</el-button>
                </div>
                <div>
                    <el-pagination v-if="recordsTotal > 0" @current-change="getList"
                        :current-page.sync="queryParams.pageNum" :page-size.sync="queryParams.pageSize"
                        layout="total, prev, pager, next,sizes" :page-sizes="[10, 25, 50, 100]"
                        :total="recordsTotal"></el-pagination>
                </div>
            </div>
            <el-table :data="tableData" v-loading="loading" border>
                <el-table-column prop="jobId" label="任务ID" width="120"></el-table-column>
                <el-table-column prop="triggerTime" label="调度时间" min-width="200"></el-table-column>
                <el-table-column prop="triggerCode" label="调度结果" min-width="120">
                    <template slot-scope="{row}">
                        <el-tag :type="({ 0: 'info', 200: 'success', 500: 'danger' })[row.triggerCode]">{{ ({
                            0: '无',
                            200: '成功', 500: '失败'
                        })[row.triggerCode] }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="triggerMsg" label="调度备注" min-width="120">
                    <template slot-scope="{row}">
                        <el-button v-if="row.triggerMsg" @click="viewClick(row, 1)" type="text">查看</el-button>
                        <span v-else>无</span>
                    </template>
                </el-table-column>
                <el-table-column prop="handleTime" label="执行时间" min-width="200"></el-table-column>
                <el-table-column prop="handleCode" label="执行结果" min-width="120">
                    <template slot-scope="{row}">
                        <el-tag
                            :type="({ 0: 'info', 200: 'success', 500: 'danger', 502: 'warning' })[row.handleCode]">{{ ({
                                0: '无', 200: '成功', 500: '失败', 502: '失败(超时)'
                            })[row.handleCode] }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="handleMsg" label="执行备注" min-width="300">
                    <template slot-scope="{row}">
                        <el-button v-if="row.handleMsg" @click="viewClick(row, 2)" type="text">查看</el-button>
                        <span v-else>无</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                    <template slot-scope="{row}">
                        <el-dropdown @command="dropdownEvent($event, row)" trigger="click" size="mini" split-button>
                            操作
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item :divided="item.divided" :disabled="item.disabled(row)"
                                    v-for="item, index in dropdownItem" :key="index" :command="item.id">{{ item.name
                                    }}</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <playFrameTemplate :title="viewDivTitle" width="600px" :show.sync="viewShow">
            <span v-html="viewDiv" style="white-space: pre-wrap"></span>
        </playFrameTemplate>

        <playFrameTemplate width="500px" title="日志清理" @change="clearChange" :show.sync="clearShow">
            <el-form ref="form" :model="formClear" label-width="120px">
                <el-form-item label="执行器" prop="jobGroup">
                    <selectInput disabled v-model="formClear.jobGroup">
                        <el-option v-for="item in actuatorOptions" :key="item.id" :label="item.title"
                            :value="item.id" />
                    </selectInput>
                </el-form-item>

                <el-form-item label="任务" prop="jobId">
                    <selectInput disabled v-model="formClear.jobId">
                        <el-option v-for="item, index in jobIdOptions" :key="index" :label="item.jobDesc"
                            :value="item.id" />
                    </selectInput>
                </el-form-item>

                <el-form-item label="清理方式" prop="type">
                    <selectInput v-model="formClear.type">
                        <el-option v-for="item in typeOptions" :key="item.value" :label="item.name"
                            :value="item.value" />
                    </selectInput>
                </el-form-item>
            </el-form>
        </playFrameTemplate>
    </div>
</template>

<script>
import router from '@/router'
import { dispatchingLogInfoApi, dispatchingLogPageListApi, dispatchingClearLogtApi, dispatchingGetJobsByGroupApi } from "../api/dispatchingLog.js"
import selectInput from "../components/lableInput/selectInput.vue"
import baseInput from "../components/lableInput/input.vue"
import timeInput from "../components/lableInput/timeInput.vue"
import playFrameTemplate from "../components/playFrame/index.vue"
import cronPlayFrame from "../components/cron/index.vue";
export default {
    name: "DispatchingLog",
    components: {
        selectInput, playFrameTemplate, baseInput, cronPlayFrame, timeInput
    },
    data() {
        return {
            loading: false,
            viewDivTitle: "",
            formClear: {
                jobGroup: null,
                jobId: 0,
                type: 1,
            },
            typeOptions: [
                { name: "清理一个月之前日志数据", value: 1 },
                { name: "清理三个月之前日志数据", value: 2 },
                { name: "清理六个月之前日志数据", value: 3 },
                { name: "清理一年之前日志数据", value: 4 },
                { name: "清理一千条以前日志数据", value: 5 },
                { name: "清理一万条以前日志数据", value: 6 },
                { name: "清理三万条以前日志数据", value: 7 },
                { name: "清理十万条以前日志数据", value: 8 },
                { name: "清理所有日志数据", value: 9 },
            ],
            clearShow: false,
            viewDiv: '',
            viewShow: false,
            jobIdOptions: [],
            logStatusOptions: [
                { name: "全部", value: -1 },
                { name: "成功", value: 1 },
                { name: "失败", value: 2 },
                { name: "进行中", value: 3 },
            ],
            queryParams: {
                jobGroup: null,
                pageSize: 10,
                jobId: 0,
                logStatus: -1,
                filterTimeList: [],
                filterTime: '',
                pageNum: 1
            },
            pageNum: 1,
            recordsTotal: 0,
            tableData: [],
            actuatorOptions: [],
            dropdownItem: [
                { name: "执行日志", id: 1, disabled: (row) => { let judge = false; row.triggerCode != 200 && (judge = true); return judge } },
            ],
            queryRoute: {}
        }
    },
    watch: {
        queryParams: {
            handler(val) {
                if (!val.filterTimeList) {
                    this.queryParams.filterTime = null
                } else {
                    this.queryParams.filterTime = this.parseTime(val.filterTimeList[0]) + ' ~ ' + this.parseTime(val.filterTimeList[1])
                }
            },
            deep: true,
            immediate: true
        },
        '$route': {
            handler(newParams, oldParams) {
                if (newParams.path != '/taskscheduling/dispatchingLog') return;
                if (newParams.query.jobDesc) {
                    if (Object.keys(this.queryRoute).length != 0 && this.isEqual(this.queryRoute, newParams.query)) {
                        return
                    }
                    this.queryRoute = newParams.query
                    this.queryParams.jobGroup = Number(this.$route.query?.jobGroup) || null;
                    this.queryParams.jobId = Number(this.$route.query?.jobDesc) || 0;
                    this.oneEvent();
                    this.routerEvent();
                }
            },
            // immediate: true,
            deep: true // 深度监听，适用于对象或数组
        }
    },

    created() {
        this.queryParams.jobGroup = Number(this.$route.query?.jobGroup) || null;
        this.queryParams.jobId = Number(this.$route.query?.jobDesc) || 0;
        this.oneEvent();
        this.routerEvent();
    },

    methods: {
        isEqual(obj1, obj2) {
            const keys1 = Object.keys(obj1);
            const keys2 = Object.keys(obj2);

            if (keys1.length !== keys2.length) {
                return false;
            }

            for (const key of keys1) {
                if (obj1[key] !== obj2[key]) {
                    return false;
                }
            }

            return true;
        },
        routerEvent() {
            router.beforeEach((to, from, next) => {
                let judge = sessionStorage.getItem("router/logDetailPage") || false
                if (this.$route.name == "Task" && !judge) {
                    let routes = [
                        {
                            path: '/logDetailPage',
                            meta: { title: '执行日志' },
                            name: 'logDetailPage',
                            component: () => import('@/views/taskscheduling/dispatchingLog/logDetailPage.vue'),
                            hidden: true,
                        }
                    ];
                    router.addRoutes(routes)
                    next({ ...to, replace: true })
                    sessionStorage.setItem("router/logDetailPage", true)
                } else {
                    next()
                }
            })
        },
        clearEvent() {
            let { jobGroup, jobId } = this.queryParams;
            this.formClear = { ...this.formClear, jobGroup, jobId }
            this.clearShow = true;
        },
        jobGroupChange(e) {
            dispatchingGetJobsByGroupApi({ jobGroup: e }).then(res => {
                let list = [{ jobDesc: "全部", id: 0 }]
                this.jobIdOptions = [...list, ...res.content];
            })
        },
        // 执行一次数据,获取基础信息
        oneEvent() {
            dispatchingLogInfoApi().then(res => {
                let { JobGroupList } = res.data;
                this.actuatorOptions = JobGroupList;
                this.actuatorOptions.length > 0 && (this.queryParams.jobGroup = this.actuatorOptions[0].id);
                this.jobGroupChange(this.queryParams.jobGroup);
                this.getList();
            });
            const date = new Date();
            let a = this.currentAllTime(new Date(date.setTime(date.getTime() - 1 * 60 * 60 * 1000)));
            let b = this.currentAllTime();
            this.queryParams.filterTime = `${a} ~ ${b}`;
            // this.queryParams.filterTimeList = [a, b];
        },
        currentAllTime(date = new Date()) {
            let sign2 = ":";
            let year = date.getFullYear() // 年
            let month = date.getMonth() + 1; // 月
            let day = date.getDate(); // 日
            let hour = date.getHours(); // 时
            let minutes = date.getMinutes(); // 分
            let seconds = date.getSeconds() //秒
            let weekArr = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期天'];
            let week = weekArr[date.getDay()];
            // 给一位数的数据前面加 “0”
            month >= 1 && month <= 9 && (month = "0" + month);
            day >= 0 && day <= 9 && (day = "0" + day);
            hour >= 0 && hour <= 9 && (hour = "0" + hour);
            minutes >= 0 && minutes <= 9 && (minutes = "0" + minutes);
            seconds >= 0 && seconds <= 9 && (seconds >= 0 && seconds <= 9);
            return year + "-" + month + "-" + day + " " + hour + sign2 + minutes + sign2 + seconds;
        },
        clearChange(judge) {
            if (judge) {
                dispatchingClearLogtApi(this.formClear).then(res => {
                    this.$message({ message: `清理成功!`, type: 'success' });
                    this.getList();
                })
            }
        },
        getList() {
            // 获取列表信息
            this.loading = true;
            dispatchingLogPageListApi(this.queryParams).then(res => {
                this.tableData = res.data;
                this.recordsTotal = res.recordsTotal;
                this.loading = false;
            })
        },
        viewClick(row, judge) {
            this.viewShow = true;
            if (judge == 1) {
                this.viewDivTitle = "调度结果"
                this.viewDiv = row.triggerMsg;
            } else {
                this.viewDivTitle = "执行备注"
                this.viewDiv = row.handleMsg;
            }
        },
        // 操作下拉选择事件
        dropdownEvent(id, obj = {}) {
            switch (id) {
                case 1:
                    let routeUrl = this.$router.resolve({ path: "/logDetailPage", query: { id: obj.id } });
                    window.open(routeUrl.href, '_blank')
                        ; break;
            }
        },
    },
}
</script>

<style lang="scss">
/* ::-webkit-scrollbar {
    width: 12px;
    height: 12px;
    } */
::-webkit-scrollbar-track {
    background: rgb(239, 239, 239);
}

::-webkit-scrollbar-thumb {
    background: #bfbfbf;
}

.marginLeft10 {
    margin-left: 10px;
}

.marginBottom10 {
    margin-bottom: 10px;
}

.dispatchingLog_task {
    padding: 10px 15px;
    box-sizing: border-box;
    background-color: rgb(255 255 255 / 95%);
    width: 98%;
    min-height: 80vh;
    margin-left: 1%;
    margin-top: 10px;
    border-radius: 10px;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);

    &_head {
        padding-bottom: 10px;
        font-size: 25px;
        font-weight: bold;
    }

    &_search {
        display: flex;
        flex-wrap: wrap;

        .el-input__inner {
            border-radius: 0px !important;
            min-width: 200px;
        }

        .el-input {
            margin-bottom: 15px;
        }
    }

    &_tableHead {
        display: flex;
        justify-content: space-between;
        padding: 2px 0px 2px 5px;
        align-items: center;
        border-top: 1px solid #dfe6ec;
        border-left: 1px solid #dfe6ec;
        border-right: 1px solid #dfe6ec;
    }
}

.editTitle {
    padding-bottom: 3px;
    margin-bottom: 10px;
    color: #858585;
    border-bottom: 1px solid #e5e5e5;
}
</style>