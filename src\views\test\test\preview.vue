<template>
  <el-dialog :visible.sync="visible" append-to-body :close-on-click-modal="false" :close-on-press-escape="false" title="打印预览" :width="width + 'mm'">
    <div style="padding-bottom: 20px;border-bottom: 1px solid #ccc;">
      <el-button :loading="waitShowPrinter" type="primary" icon="el-icon-printer" @click.stop="print">打印</el-button>
      <el-button :loading="waitShowPrinter" type="primary" icon="el-icon-tickets" @click.stop="toPdf">pdf</el-button>

    </div>
    <div v-if="spinning" style="min-height: 100px">
      <div id="preview_content_design"></div>
    </div>
    <div slot="footer" class="dialog-footer" align="right" style="padding-top: 20px;border-top: 1px solid #ccc;">
      <el-button @click="hideModal">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "printPreview",
  props: {},
  data() {
    return {
      visible: false,
      spinning: true,
      waitShowPrinter: false,
      // 纸张宽 mm
      width: 0,
      // 模板
      hiprintTemplate: {},
      // 数据
      printData: {}
    }
  },
  computed: {},
  watch: {},
  created() {
  },
  mounted() {
  },
  methods: {
    hideModal() {
      this.visible = false
    },
    show(hiprintTemplate, printData, width = '410') {
      this.visible = true
      this.spinning = true
      this.width = hiprintTemplate.editingPanel ? hiprintTemplate.editingPanel.width : width;
      // this.width += 10;
      this.hiprintTemplate = hiprintTemplate
      this.printData = printData
      setTimeout(() => {
        // eslint-disable-next-line no-undef
        $('#preview_content_design').html(hiprintTemplate.getHtml(printData))
        // this.spinning = false
      }, 500)
    },
    print() {
      this.waitShowPrinter = true
      this.hiprintTemplate.print(this.printData, {}, {
        callback: () => {
          console.log('callback')
          this.waitShowPrinter = false
        }
      })
    },
    toPdf() {
      this.hiprintTemplate.toPdf(this.printData, '打印预览', {isDownload: false, type: 'pdfobjectnewwindow'});
    },
  }
}

</script>
<style lang="scss" scoped>
::v-deep .ant-modal-body {
  padding: 0px;
}

::v-deep .ant-modal-content {
  margin-bottom: 24px;
}
</style>
