import request from '@/utils/request'

// 3.调度日志 基础数据 
export function dispatchingLogInfoApi(query) {
    return request({
        url: '/xxl-job/joblog/indexJson',
        method: 'get',
        params: query
    })
}

// 3.调度日志 列表查询
export function dispatchingLogPageListApi(query) {
    return request({
        url: '/xxl-job/joblog/pageList',
        method: 'get',
        params: query
    })
}
// 3.调度日志 列表查询
export function dispatchingClearLogtApi(query) {
    return request({
        url: '/xxl-job/joblog/clearLog',
        method: 'get',
        params: query
    })
}
// 3.调度日志 任务下拉选择
export function dispatchingGetJobsByGroupApi(query) {
    return request({
        url: '/xxl-job/joblog/getJobsByGroup',
        method: 'get',
        params: query
    })
}
// 3.调度日志 执行日志详细
export function dispatchingLogDetailCatApi(query) {
    return request({
        url: '/xxl-job/joblog/logDetailCat',
        method: 'get',
        params: query
    })
}

// 3.调度日志 执行日志详细
export function dispatchingLogDetailPageApi(query) {
    return request({
        url: '/xxl-job/joblog/logDetailPage',
        method: 'get',
        params: query
    })
}