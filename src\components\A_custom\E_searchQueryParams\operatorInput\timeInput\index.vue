<template>
    <div>
        <el-date-picker style="width: 178px;" v-if="config.data.compType != 11" :disabled="config.disabled" size="mini"
            class="schemeQueryInput" value-format="yyyy-MM-dd" :value="value" @input="inputValue" type="date"
            placeholder="选择日期" />
        <div v-else>
            <el-date-picker width="178px" :picker-options="rangeFrontPickerOptions" key="rangeFront"
                :disabled="config.disabled" size="mini" class="schemeQueryInput" value-format="yyyy-MM-dd"
                v-model="rangeFront" @input="inputRangeEvent" type="date" placeholder="开始日期" :value="rangeFront" />
            <span style="margin-left:5px">~</span>
            <el-date-picker key="rangeAfter" :picker-options="rangeAfterPickerOptions" :disabled="config.disabled"
                v-model="rangeAfter" size="mini" class="schemeQueryInput" value-format="yyyy-MM-dd" :value="rangeAfter"
                @input="inputRangeEvent" type="date" placeholder="结束日期" />
        </div>
    </div>
</template>

<script>
import { inputMixin } from "../mixins/index.js"
export default {
    mixins: [inputMixin],
    data() {
        return {
            rangeFront: undefined,
            rangeAfter: undefined,
            rangeAfterPickerOptions: {
                shortcuts: [{
                    text: '今天',
                    onClick: (picker) => {
                        // 根据当前日期，计算出今天的开始时间和结束时间
                        const now = new Date()
                        const startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0)
                        const endTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
                        this.$nextTick(() => {
                            this.timeChange(startTime, endTime, 'today1')
                        })
                        picker.$emit('pick', [startTime, endTime]);
                    }
                }, {
                    text: '本周',
                    onClick: (picker) => {
                        // 根据当前时间计算出本周的开始时间和结束时间
                        const now = new Date()
                        const startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay() + 1)
                        const endTime = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay() + 7, 23, 59, 59)
                        this.$nextTick(() => {
                            this.timeChange(startTime, endTime)
                        })
                        picker.$emit('pick', [startTime, endTime]);

                    }
                }, {
                    text: '本月',
                    onClick: (picker) => {
                        const now = new Date()
                        const startTime = new Date(now.getFullYear(), now.getMonth(), 1)
                        const endTime = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59)
                        this.$nextTick(() => {
                            this.timeChange(startTime, endTime)
                        })
                        picker.$emit('pick', [startTime, endTime]);
                    }
                }, {
                    text: '本季度',
                    onClick: (picker) => {
                        const now = new Date()
                        const startTime = new Date(now.getFullYear(), now.getMonth() - now.getMonth() % 3, 1)
                        const endTime = new Date(now.getFullYear(), now.getMonth() - now.getMonth() % 3 + 3, 0, 23, 59, 59)
                        this.$nextTick(() => {
                            this.timeChange(startTime, endTime)
                        })
                        picker.$emit('pick', [startTime, endTime]);
                    }
                }, {
                    text: '本年',
                    onClick: (picker) => {
                        const now = new Date()
                        const startTime = new Date(now.getFullYear(), 0, 1)
                        const endTime = new Date(now.getFullYear(), 11, 31, 23, 59, 59)
                        this.$nextTick(() => {
                            this.timeChange(startTime, endTime)
                        })
                        picker.$emit('pick', [startTime, endTime]);
                    }
                }],
                disabledDate: (time) => {
                    if (this.rangeFront) {
                        if (this.parseTime(time, '{y}-{m}-{d}') == this.rangeFront) {
                            return false
                        } else {
                            return Number(new Date(this.rangeFront)) > time.getTime()
                        }

                    }
                },
            },
            rangeFrontPickerOptions: {
                shortcuts: [{
                    text: '今天',
                    onClick: (picker) => {
                        // 根据当前日期，计算出今天的开始时间和结束时间
                        const now = new Date()
                        const startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0)
                        const endTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
                        this.$nextTick(() => {
                            this.timeChange(startTime, endTime, 'today2')
                        })
                        picker.$emit('pick', [startTime, endTime]);
                    }
                }, {
                    text: '本周',
                    onClick: (picker) => {
                        // 根据当前时间计算出本周的开始时间和结束时间
                        const now = new Date()
                        const startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay() + 1)
                        const endTime = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay() + 7, 23, 59, 59)
                        this.$nextTick(() => {
                            this.timeChange(startTime, endTime)
                        })
                        picker.$emit('pick', [startTime, endTime]);

                    }
                }, {
                    text: '本月',
                    onClick: (picker) => {
                        const now = new Date()
                        const startTime = new Date(now.getFullYear(), now.getMonth(), 1)
                        const endTime = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59)
                        this.$nextTick(() => {
                            this.timeChange(startTime, endTime)
                        })
                        picker.$emit('pick', [startTime, endTime]);
                    }
                }, {
                    text: '本季度',
                    onClick: (picker) => {
                        const now = new Date()
                        const startTime = new Date(now.getFullYear(), now.getMonth() - now.getMonth() % 3, 1)
                        const endTime = new Date(now.getFullYear(), now.getMonth() - now.getMonth() % 3 + 3, 0, 23, 59, 59)
                        this.$nextTick(() => {
                            this.timeChange(startTime, endTime)
                        })
                        picker.$emit('pick', [startTime, endTime]);
                    }
                }, {
                    text: '本年',
                    onClick: (picker) => {
                        const now = new Date()
                        const startTime = new Date(now.getFullYear(), 0, 1)
                        const endTime = new Date(now.getFullYear(), 11, 31, 23, 59, 59)
                        this.$nextTick(() => {
                            this.timeChange(startTime, endTime)
                        })
                        picker.$emit('pick', [startTime, endTime]);
                    }
                }],
                disabledDate: (time) => {
                    if (this.rangeAfter) {
                        return time.getTime() > Number(new Date(this.rangeAfter))
                    }
                },
            },
        }
    },
    watch: {
        value: {
            handler(val) {
                if (val) {
                    if (val.includes('00:00:00') || val.includes('23:59:59')) {
                        this.rangeFront = val.split(",")[0].replace(/( 00:00:00)+$/, " 00:00:00")
                        this.rangeAfter = val.split(",")[1].replace(/( 23:59:59)+$/, " 23:59:59")
                        this.$emit('input', this.rangeFront + "," + this.rangeAfter);
                    } else {
                        this.$emit('input', val)
                    }
                } else {
                    this.rangeFront = ''
                    this.rangeAfter = ''
                    this.$emit('input', '')
                }
            },
            deep: true
        },
    },
    created() {
    },
    methods: {
        timeChange(startTime, endTime, type) {
            if (type == 'today1') {
                this.rangeFront = this.parseTime(startTime)
                if (!this.rangeAfter) {
                    this.rangeAfter = this.parseTime(endTime)
                }
            } else if (type == 'today2') {
                this.rangeAfter = this.parseTime(endTime)
                if (!this.rangeFront) {
                    this.rangeFront = this.parseTime(startTime)
                }
            } else {
                this.rangeFront = this.parseTime(startTime)
                this.rangeAfter = this.parseTime(endTime)
            }
            this.$emit('input', this.rangeFront + "," + this.rangeAfter);
        },
        inputValue(val) {
            this.$emit('input', val);
        },
        inputRangeEvent(val) {
            // 清空时所处理
            if (!val) {
                if (!this.rangeFront && !this.rangeAfter) {
                    return this.$emit('input', '')
                }
                if (!this.rangeFront) {
                    return this.$emit('input', "," + this.rangeAfter + " 23:59:59")
                }
                if (!this.rangeAfter) {
                    return this.$emit('input', this.rangeFront + " 00:00:00" + ",")
                }
            }
            let isAdd = (this.rangeFront && this.rangeFront?.includes("00:00:00")) && (this.rangeAfter && this.rangeAfter?.includes("23:59:59"));
            if (this.rangeFront && this.rangeAfter && !isAdd) {
                this.$emit('input', this.rangeFront + " 00:00:00" + "," + this.rangeAfter + " 23:59:59");
            } else if (!this.rangeFront && this.rangeAfter && !this.rangeAfter?.includes("23:59:59")) {
                this.$emit('input', "," + this.rangeAfter + " 23:59:59");
            } else if (!this.rangeFront?.includes("00:00:00") && this.rangeFront && !this.rangeAfter) {
                this.$emit('input', this.rangeFront + " 00:00:00" + ",");
            } else {
                this.$emit('input', '');
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.schemeQueryInput_rangeTime {
    width: 360px;
}

.schemeQueryInput {
    width: 178px;
}
</style>