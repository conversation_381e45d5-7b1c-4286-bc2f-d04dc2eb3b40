# device_control_debugger.py (pymodbus v3, with <PERSON><PERSON> Cannon)
# -*- coding: utf-8 -*-

"""
设备控制调试脚本
功能: 通过 Modbus TCP 协议连接并控制水炮、雾炮和水泵设备。
依赖: pymodbus (请先通过 pip install pymodbus 安装)
"""

import time
import struct
import subprocess
import sys
import os
from pymodbus.client import ModbusTcpClient
from pymodbus.payload import BinaryPayloadDecoder
from pymodbus.constants import Endian
from pymodbus.exceptions import ModbusException

# --- 地址映射说明 ---
# V-Bit (e.g., V4000.0) -> Coil Address (V-Word * 8 + V-Bit)
# VD (e.g., VD4004) -> Holding Register Address (VD Address)
# Water Pump Registers -> Holding Register Address (Address - 1 for 0-based)

# --- 水炮地址常量 ---
WC_COILS_BASE = {
    "pump_running": 4000 * 8 + 0, "h_reverse": 4000 * 8 + 1, "h_forward": 4000 * 8 + 2,
    "v_reverse": 4000 * 8 + 3, "v_forward": 4000 * 8 + 4, "far_shot": 4000 * 8 + 5,
    "sprinkler": 4000 * 8 + 6, "remote_mode": 4000 * 8 + 7, "pump_linkage": 4001 * 8 + 0,
    "v_cruise_linkage": 4001 * 8 + 1, "fault": 4001 * 8 + 2,
    "cruise_spray": 4050 * 8 + 0, "fault_reset": 4050 * 8 + 1,
}
WC_REGS_BASE = {
    "h_angle": 4004, "v_angle": 4008, "h_cruise_low": 4012, "h_cruise_high": 4016,
    "v_cruise_low": 4020, "v_cruise_high": 4024, "cruise_run_time": 4054,
    "cruise_interval_time": 4056,
}

# --- 雾炮地址常量 (使用4x寄存器映射模式，地址从40001开始) ---
MC_REGS_BASE = {
    # 控制寄存器 (开关量)
    "spray_control": 40001,        # 喷雾控制 (1启动, 0停止)
    "spray_mode_select": 40002,    # 喷射方式 (1摇摆, 0定点)
    "fan_running": 40003,          # 风机运行状态
    "pump_running": 40004,         # 水泵运行状态
    "rising": 40005,               # 上升状态
    "falling": 40006,              # 下降状态
    "rotating_left": 40007,        # 左转状态
    "rotating_right": 40008,       # 右转状态
    "manual_mode": 40009,          # 手动模式
    "remote_controller_mode": 40010, # 遥控器模式
    "remote_mode": 40011,          # 远程模式
    "fault": 40012,                # 故障状态
    "rc_running": 40013,           # 遥控运行状态
    "water_shortage_fault": 40014, # 缺水故障
    "set_zero_position": 40015,    # 设置零位
    "fault_reset": 40016,          # 故障复位
    
    # 角度寄存器
    "current_rotation_pos": 40020,  # 当前旋转位置
    "current_pitch_pos": 40021,     # 当前俯仰位置
    "fixed_rotation_angle": 40022,  # 定点旋转角度
    "fixed_pitch_angle": 40023,     # 定点俯仰角度
    "swing_start_angle": 40017,     # 摇摆起始角度
    "swing_end_angle": 40019,       # 摇摆结束角度
    "left_limit_set": 40026,        # 左限位设置
    "right_limit_set": 40027,       # 右限位设置
    "pulses_per_revolution": 40028, # 每转脉冲数
}

# --- 水泵地址常量 (0-based) ---
WP_REGS_BASE = {
    "output_freq": 0, "output_current": 1, "output_voltage": 2, "output_power": 3,
    "current_pressure": 4, "set_pressure": 5, "run_control": 6, "fault_type": 7,
}

class DeviceController:
    """Modbus设备控制器基类"""
    def __init__(self, host, port=502, unit_id=1):
        self.host = host
        self.port = port
        self.unit_id = unit_id
        self.client = ModbusTcpClient(host, port=port)

    def read_float(self, address):
        try:
            rr = self.client.read_holding_registers(address=address, count=2, slave=self.unit_id)
            if rr.isError(): return None
            decoder = BinaryPayloadDecoder.fromRegisters(rr.registers, byteorder=Endian.BIG, wordorder=Endian.BIG)
            return decoder.decode_32bit_float()
        except ModbusException: return None

    def write_float(self, address, value):
        try:
            packed_bytes = struct.pack('>f', value)
            payload = struct.unpack('>HH', packed_bytes)
            wr = self.client.write_registers(address=address, values=list(payload), slave=self.unit_id)
            return not wr.isError()
        except ModbusException: return False

    def read_int32(self, address):
        try:
            rr = self.client.read_holding_registers(address=address, count=2, slave=self.unit_id)
            if rr.isError(): return None
            decoder = BinaryPayloadDecoder.fromRegisters(rr.registers, byteorder=Endian.BIG, wordorder=Endian.BIG)
            return decoder.decode_32bit_int()
        except ModbusException: return None
    
    def write_int32(self, address, value):
        try:
            packed_bytes = struct.pack('>i', value)
            payload = struct.unpack('>HH', packed_bytes)
            wr = self.client.write_registers(address=address, values=list(payload), slave=self.unit_id)
            return not wr.isError()
        except ModbusException: return False
    
    # 雾炮机专用的寄存器读写方法 (4x寄存器映射)
    def to_signed_int16(self, unsigned_value):
        """将无符号16位整数转换为有符号16位整数"""
        if unsigned_value > 32767:
            return unsigned_value - 65536
        return unsigned_value
    
    def from_signed_int16(self, signed_value):
        """将有符号16位整数转换为无符号16位整数"""
        if signed_value < 0:
            return signed_value + 65536
        return signed_value
    
    def get_modbus_address(self, register_addr):
        """将4x寄存器地址转换为Modbus地址"""
        return register_addr - 40001
    
    def read_register(self, register_addr):
        """读取单个寄存器"""
        modbus_addr = self.get_modbus_address(register_addr)
        
        if modbus_addr < 0:
            print(f"✗ 寄存器地址{register_addr}无效 (Modbus地址: {modbus_addr} < 0)")
            return None
        
        try:
            rr = self.client.read_holding_registers(address=modbus_addr, count=1, slave=self.unit_id)
            
            if rr.isError():
                print(f"✗ 读取寄存器{register_addr}失败")
                return None
            else:
                value = rr.registers[0]
                return value
                
        except Exception as e:
            print(f"✗ 读取寄存器{register_addr}异常: {e}")
            return None
    
    def write_register(self, register_addr, value, signed=False):
        """写入寄存器值"""
        # 处理有符号值
        if signed and value < 0:
            write_value = self.from_signed_int16(value)
        else:
            write_value = value
        
        modbus_addr = self.get_modbus_address(register_addr)
        
        if modbus_addr < 0:
            print(f"✗ 寄存器地址{register_addr}无效 (Modbus地址: {modbus_addr} < 0)")
            return False
        
        try:
            # 写入寄存器
            wr = self.client.write_register(address=modbus_addr, value=write_value, slave=self.unit_id)
            
            if wr.isError():
                print(f"✗ 写入失败: {wr}")
                return False
            else:
                return True
                    
        except Exception as e:
            print(f"✗ 写入异常: {e}")
            return False

class WaterCannon(DeviceController):
    """水炮控制器"""
    # ... (Implementation from previous version, no changes needed)
    def __init__(self, host, port=502):
        super().__init__(host, port)
        print(f"--- 已初始化水炮控制器: {host} ---")

    def get_status(self):
        print("\n--- 读取水炮状态 ---")
        coil_addrs = list(WC_COILS_BASE.values())
        min_addr, max_addr = min(coil_addrs), max(coil_addrs)
        count = max_addr - min_addr + 1
        try:
            rr = self.client.read_coils(address=min_addr, count=count, slave=self.unit_id)
            if not rr.isError():
                for name, addr in WC_COILS_BASE.items():
                    value = rr.bits[addr - min_addr]
                    print(f"{name}: {value}")
            for name, addr in WC_REGS_BASE.items():
                val = self.read_int32(addr) if "time" in name else self.read_float(addr)
                if val is not None: print(f"{name}: {val}")
        except ModbusException as e: print(f"读取出错: {e}")
        print("--- 读取完成 ---\n")

    def set_cruise_spray(self, start=True):
        print(f"--- 设置巡航喷洒: {'启动' if start else '停止'} ---")
        self.client.write_coil(address=WC_COILS_BASE["cruise_spray"], value=start, slave=self.unit_id)

    def set_fault_reset(self):
        print("--- 故障复位 ---")
        self.client.write_coil(address=WC_COILS_BASE["fault_reset"], value=True, slave=self.unit_id)
        time.sleep(0.5)
        self.client.write_coil(address=WC_COILS_BASE["fault_reset"], value=False, slave=self.unit_id)

    def set_cruise_time(self, run_time, interval_time):
        print(f"--- 设置巡航时间: 运行 {run_time}min, 间隔 {interval_time}min ---")
        self.write_int32(WC_REGS_BASE["cruise_run_time"], run_time)
        self.write_int32(WC_REGS_BASE["cruise_interval_time"], interval_time)

class MistCannon(DeviceController):
    """雾炮控制器"""
    def __init__(self, host, port=502):
        super().__init__(host, port)
        print(f"--- 已初始化雾炮控制器: {host} ---")

    def get_status(self):
        print("\n--- 读取雾炮状态 ---")
        try:
            # 读取所有寄存器状态
            for name, addr in MC_REGS_BASE.items():
                val = self.read_register(addr)
                if val is not None: 
                    signed_val = self.to_signed_int16(val)
                    print(f"{name}: {val} (无符号) / {signed_val} (有符号) / 0x{val:04X}")
        except ModbusException as e: 
            print(f"读取出错: {e}")
        print("--- 读取完成 ---\n")

    def set_spray(self, start=True):
        print(f"--- 设置喷雾: {'启动' if start else '停止'} ---")
        success = self.write_register(MC_REGS_BASE["spray_control"], 1536 if start else 0)
        if success:
            print("✓ 喷雾控制设置成功")
        else:
            print("✗ 喷雾控制设置失败")

    def set_spray_mode(self, swing=True):
        print(f"--- 设置喷射方式: {'摇摆' if swing else '定点'} ---")
        success = self.write_register(MC_REGS_BASE["spray_mode_select"], 256 if swing else 0)
        if success:
            print("✓ 喷射方式设置成功")
        else:
            print("✗ 喷射方式设置失败")

    def set_fixed_angles(self, rotation, pitch):
        print(f"--- 设置定点角度: 旋转 {rotation}, 俯仰 {pitch} ---")
        success1 = self.write_register(MC_REGS_BASE["fixed_rotation_angle"], rotation, signed=True)
        success2 = self.write_register(MC_REGS_BASE["fixed_pitch_angle"], pitch, signed=True)
        if success1 and success2:
            print("✓ 定点角度设置成功")
        else:
            print("✗ 定点角度设置失败")

    def set_swing_angles(self, start, end):
        print(f"--- 设置摇摆范围: 起始 {start}, 终止 {end} ---")
        success1 = self.write_register(MC_REGS_BASE["swing_start_angle"], start, signed=True)
        success2 = self.write_register(MC_REGS_BASE["swing_end_angle"], end, signed=True)
        if success1 and success2:
            print("✓ 摇摆范围设置成功")
        else:
            print("✗ 摇摆范围设置失败")

    def fault_reset(self):
        print("--- 故障复位 ---")
        success1 = self.write_register(MC_REGS_BASE["fault_reset"], 1)
        if success1:
            time.sleep(0.5)
            success2 = self.write_register(MC_REGS_BASE["fault_reset"], 0)
            if success2:
                print("✓ 故障复位成功")
            else:
                print("✗ 故障复位失败(复位信号清除失败)")
        else:
            print("✗ 故障复位失败(复位信号设置失败)")
    
    def set_zero_position(self):
        print("--- 设置零位 ---")
        success = self.write_register(MC_REGS_BASE["set_zero_position"], 1)
        if success:
            time.sleep(0.5)
            self.write_register(MC_REGS_BASE["set_zero_position"], 0)
            print("✓ 零位设置成功")
        else:
            print("✗ 零位设置失败")
    
    def read_current_position(self):
        print("--- 读取当前位置 ---")
        rotation = self.read_register(MC_REGS_BASE["current_rotation_pos"])
        pitch = self.read_register(MC_REGS_BASE["current_pitch_pos"])
        if rotation is not None and pitch is not None:
            rotation_signed = self.to_signed_int16(rotation)
            pitch_signed = self.to_signed_int16(pitch)
            print(f"当前旋转位置: {rotation_signed}°")
            print(f"当前俯仰位置: {pitch_signed}°")
        else:
            print("✗ 读取当前位置失败")

class WaterPump(DeviceController):
    """水泵控制器"""
    # ... (Implementation from previous version, no changes needed)
    def __init__(self, host, port=502):
        super().__init__(host, port)
        print(f"--- 已初始化水泵控制器: {host} ---")

    def get_status(self):
        print("\n--- 读取水泵状态 ---")
        try:
            rr = self.client.read_holding_registers(address=0, count=len(WP_REGS_BASE), slave=self.unit_id)
            if not rr.isError():
                for name, addr in WP_REGS_BASE.items():
                    print(f"{name}: {rr.registers[addr]}")
        except ModbusException as e: print(f"读取出错: {e}")
        print("--- 读取完成 ---\n")

    def set_run_control(self, start=True):
        print(f"--- 设置水泵运行: {'启动' if start else '停止'} ---")
        self.client.write_register(address=WP_REGS_BASE["run_control"], value=1 if start else 0, slave=self.unit_id)

    def set_pressure(self, pressure):
        print(f"--- 设置水泵压力: {pressure} ---")
        self.client.write_register(address=WP_REGS_BASE["set_pressure"], value=int(pressure), slave=self.unit_id)

def get_ip(device_name):
    return input(f"请输入 {device_name} 的IP地址: ")

def create_menu(device_class, menu_map):
    ip = get_ip(device_class.__name__)
    device = device_class(ip)
    if not device.client.connect():
        print(f"无法连接到 {ip}。请检查IP地址和网络连接。")
        return

    while True:
        print(f"\n--- {device_class.__name__} 控制菜单 ---")
        for key, (text, _) in menu_map.items(): print(f"{key}. {text}")
        print("0. 返回主菜单")
        choice = input("请选择操作: ")
        if choice == '0': break
        if choice in menu_map:
            _, func = menu_map[choice]
            func(device)
    device.client.close()

def start_mist_cannon_simulator():
    """启动雾炮从机模拟器"""
    simulator_path = os.path.join(os.path.dirname(__file__), 'mist_cannon_simulator.py')
    if not os.path.exists(simulator_path):
        print("✗ 雾炮从机模拟器脚本不存在，请先创建 mist_cannon_simulator.py")
        return

    print("--- 启动雾炮从机模拟器 ---")
    try:
        # 在新进程中启动模拟器
        process = subprocess.Popen([sys.executable, simulator_path])
        print("✓ 雾炮从机模拟器已启动")
        print("模拟8个雾炮从机，IP地址: ************* - *************")
        input("按回车键返回主菜单...")
    except Exception as e:
        print(f"✗ 启动雾炮从机模拟器失败: {e}")

def main_menu():
    wc_menu_map = {
        '1': ("读取状态", lambda dev: dev.get_status()),
        '2': ("启动巡航", lambda dev: dev.set_cruise_spray(True)),
        '3': ("停止巡航", lambda dev: dev.set_cruise_spray(False)),
        '4': ("故障复位", lambda dev: dev.set_fault_reset()),
        '5': ("设置巡航时间", lambda dev: dev.set_cruise_time(int(input("运行时间(min): ")), int(input("间隔时间(min): ")))),
    }
    mc_menu_map = {
        '1': ("读取状态", lambda dev: dev.get_status()),
        '2': ("启动喷雾", lambda dev: dev.set_spray(True)),
        '3': ("停止喷雾", lambda dev: dev.set_spray(False)),
        '4': ("设为摇摆模式", lambda dev: dev.set_spray_mode(True)),
        '5': ("设为定点模式", lambda dev: dev.set_spray_mode(False)),
        '6': ("设置定点角度", lambda dev: dev.set_fixed_angles(int(input("旋转角度: ")), int(input("俯仰角度: ")))),
        '7': ("设置摇摆范围", lambda dev: dev.set_swing_angles(int(input("起始角度: ")), int(input("终止角度: ")))),
        '8': ("故障复位", lambda dev: dev.fault_reset()),
        '9': ("设置零位", lambda dev: dev.set_zero_position()),
        '10': ("读取当前位置", lambda dev: dev.read_current_position()),
    }
    wp_menu_map = {
        '1': ("读取状态", lambda dev: dev.get_status()),
        '2': ("启动水泵", lambda dev: dev.set_run_control(True)),
        '3.': ("停止水泵", lambda dev: dev.set_run_control(False)),
        '4': ("设定压力", lambda dev: dev.set_pressure(int(input("目标压力: ")))),
    }

    main_menu_map = {
        '1': ("控制水炮", lambda: create_menu(WaterCannon, wc_menu_map)),
        '2': ("控制雾炮", lambda: create_menu(MistCannon, mc_menu_map)),
        '3': ("控制水泵", lambda: create_menu(WaterPump, wp_menu_map)),
        '4': ("启动雾炮从机模拟器", start_mist_cannon_simulator),
    }

    print("\n==============================================")
    print("  欢迎使用设备控制调试脚本 (pymodbus v3)")
    print("==============================================")
    while True:
        print("\n--- 主菜单 ---")
        for key, (text, _) in main_menu_map.items(): print(f"{key}. {text}")
        print("q. 退出程序")
        choice = input("请选择设备类型: ")
        if choice.lower() == 'q': break
        if choice in main_menu_map:
            main_menu_map[choice][1]()

if __name__ == "__main__":
    main_menu()
