<template>
  <div class="app-container">
    <searchForm  @change="searchFormChange" :searchList.sync="queryList" :keys="url" ></searchForm>
    <defineTable v-loading="loading" :tableKeysList="queryList" :tableDataList.sync="tableDataList" :config="tableConfig" @change="tableChange" >
      <!-- 操作 -->
      <template #operate="{row}" >
        <el-dropdown split-button trigger="click" @command="commandDropdownClick" >
          <span class="el-dropdown-link">展开</span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="修改" >修改</el-dropdown-item>
            <el-dropdown-item command="详情" >详情</el-dropdown-item>
            <el-dropdown-item command="审批" >审批</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <!-- 右击选项窗口 -->
      <template #rightOption="{data}">
        <el-link size="mini">导出</el-link>
        <el-link size="mini" @click="copySelectedUnits(data)">复制选中的单元</el-link>
        <el-link size="mini" @click="uncheck(data)">取消行</el-link>
      </template>
    </defineTable>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script>
import pageMixins from "@/components/A_custom/mixins/page.js"
import rightOptionMixins from "@/components/A_custom/mixins/rightOption.js"
import { tableListApi } from "@/api/demo/demo.js"
export default {
  name: "Demo",
  mixins:[pageMixins,rightOptionMixins],
  data() {
    return {
      url:"demo/demo",
      tableListApi:tableListApi,
      tableConfig:{
        // rowKey:"id",
        // checkbox:true, //选择多行数据
        // selection:true,     //是否需要选择
        // amountToJudge:false, //需要合计
        // align:"center", //文字位置
        // border:true, //边框线
        // textOverflow:false, //文字超出是否隐藏
      },
    }
  },
  computed:{

  },
  mounted(){
    
  },
  methods: {
    // 操作返回事件
    commandDropdownClick(e){
      console.log(e,"commandDropdownClick")
    },
    //列表弹框返回事件
    tableChange(emitData){
      let {data,name,type} = emitData;
      // 操作返回事件
      if(type==1){

      }
    },
  }
};
</script>


<style lang="scss" >

</style>
