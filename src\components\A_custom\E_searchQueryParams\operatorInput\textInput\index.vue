<template>
    <el-input :disabled="config.disabled" class="schemeQueryInput" size="mini" :placeholder="placeholder" @input="valueInput" :value="value" @keyup.enter.native="handleQuery"></el-input>
</template>

<script>
import {inputMixin} from "../mixins/index.js"
    export default {
        mixins:[inputMixin],
        methods:{
            valueInput(e){
                this.$emit('input', e);
            },
            handleQuery(){
                this.$emit('handleQuery');
            },
        },
    }
</script>

<style lang="scss" scoped>
// .schemeQueryInput{
//     width:150px;
//     margin-left:5px;
// }
</style>