<template>
    <div class="" v-loading="loading">
        <dropDownDisplay title="模板详情信息">
            <!-- <el-row :gutter="10" class="mb8" >
                <el-col :span="1.5">
                    <el-button  type="primary" plain size="mini" class="mr10"
                        @click="formUpdate">修改</el-button>
                    <el-button type="success" plain size="mini" class="mr10"
                        @click="submitForm">保存</el-button>
                </el-col>
            </el-row> -->
            <el-form ref="form" :model="form" label-width="120px" :inline="true">
                <showInput label="需求人员" v-model="form.contactsName" width="220px"></showInput>
                <showInput label="发货计划类型" v-model="form.noticeType" :options="dict.type.lx_demo_notice_type" width="220px"></showInput>
            </el-form>
        </dropDownDisplay>
    </div>
</template>

<script>
import dropDownDisplay from "@/components/A_custom/D_public/dropDownDisplay/index"
import { listDemo, getDemo, delDemo, addDemo, updateDemo, listBySelector } from "@/api/system/demo";
export default {
    name: "demoDetail",
    dicts:['lx_demo_notice_type'],
    components: {
        dropDownDisplay
    },
    data() {
        return {
            loading: false,
            form:{}


        };
    },
    created() {
        this.loading = true;
        if (this.$route.query.id) {
            this.getDetail(this.$route.query.id)
        }
    },
    watch: {
        
    },
    methods: {
        getDetail(id) {
            getDemo(id).then(res => {
                this.form = res.data;
                this.loading = false;
            });
        },
    }
};
</script>