<template>
  <div class="lookImage">
    <div v-if="fileDialog">
      <el-dialog title="图片查看" v-if="dialog" :visible.sync="fileDialog" width="865px" append-to-body :before-close="noEvent"
        :close-on-click-modal="false">
        <div v-loading="loading">
          <div v-if="imageList.length > 0">
            <div class="mb10 bold">图片：</div>
            <el-image class="mr5 ml5 mb5" v-for="item, index in imageList" :key="index"
              style="width: 150px; height: 150px;border: #1890FF 1px solid;" :src="item" :preview-src-list="imageList"></el-image>
          </div>
          <div v-if="textList.length > 0">
            <div class="mb10 mt20 bold">其他：</div>
            <div v-for="item, index in textList" :key="index" class="row alignCenter mb5 ml10">
              <div class="mr10 lookImage_text">{{ decodeURI(item).split("/")[item.split("/").length - 1]}}</div>
              <el-button type="success" size="mini" @click="textDownload(item)">下载</el-button>
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="$emit('update:fileDialog', false)">取 消</el-button>
        </div>
      </el-dialog>

      <div v-else>
        <div v-if="imageList.length > 0">
          <div class="mb10 bold" style="color: rgb(125, 125, 125);">图片：</div>
          <el-image class="mr5 ml5 mb5" v-for="item, index in imageList" :key="index" style="width: 90px; height: 90px;border: #f56c6c 1px solid;"
            :src="item" :preview-src-list="imageList"></el-image>
        </div>
        <div v-if="textList.length > 0">
          <div class="mb10 mt20 bold" style="color: rgb(125, 125, 125);">其他：</div>
          <div v-for="item, index in textList" :key="index" class="row alignCenter mb5 ml10">
            <div class="mr10 lookImage_text">{{ decodeURI(item).split("/")[item.split("/").length - 1] }}</div>
            <el-button type="success" size="mini" @click="textDownload(item)">下载</el-button>
          </div>
        </div>
      </div>
    </div>

    <ImagePreviewDow v-else-if="!fileDialog && showPreview" :url-list="imageList" :initial-index="initialIndex" @close="closeViewer"
      @switch="handleSwitch"></ImagePreviewDow>
  </div>
</template>

<script>
export default {
  props: {
    fileDialog: {
      type: Boolean,
      default: () => false
    },
    show: {
      type: Boolean,
      default: () => false
    },
    value: {
      type: [String, Array],
      default: () => ""
    },
    dialog: {
      type: [Boolean],
      default: () => true
    },
  },
  data() {
    return {
      imageList: [],
      textList: [],
      // 存储桶是否有权限 true 是 ,false 否
      judge: true,
      loading: false,
      initialIndex: 0,
      showPreview:false,
    };
  },
  watch: {
    show() {
      this.imageList = [];
      this.textList = [];
      if (this.show) {
        this.oneEvent();
        this.showPreview = true
      } 
    },
    fileDialog() {
      this.imageList = [];
      this.textList = [];
      if (this.fileDialog) {
        this.oneEvent();
      } 
    },
  },
  created() {
    if (!this.dialog) {
      this.oneEvent();
    }
  },
  methods: {
    // 两种模式查看图片 , 有权限 ,没权限
    oneEvent() {
      if (!this.value) return
      let list = this.value.split(",");
      list.forEach(x => {
        let str = x.split(".")[x.split(".").length - 1];
        if (!this.judge) {
          if (str == "jpg" || str == "png" || str == 'jpeg') {
            // this.imageList.push(window.location.origin + process.env.VUE_APP_BASE_API + "/"  + x)
            this.imageList.push("http://" + x); //没有限制的
          } else {
            this.textList.push(x)
          }
        } else {
          this.loading = true;
          if (str == "jpg" || str == "png" || str == 'jpeg') {
            this.gainCosJsSdk(decodeURI(x)).then(res => {
              this.imageList.push(res)
            }).finally(() => {
              this.loading = false;
            })
          } else {
            this.textList.push(x)
            // this.gainCosJsSdk(decodeURI(x)).then(res => {
            //   this.textList.push(res)
            // }).finally(() => {
            //   this.loading = false;
            // })
          }
        }
      });
    },
    noEvent() {
      this.$emit('update:fileDialog', false)
    },
    textDownload(url) {
      console.log(url,'url');
      this.gainCosJsSdk(decodeURI(url)).then(res => {
        window.open(res, "_blank");
      }).finally(() => {
      })
      // this.download2(decodeURI(decodeURI(src)), {}, `${new Date().getTime()}.${strType}`)
    },
    //关闭图片预览
    closeViewer() {
      this.showPreview = false
      this.$emit('update:show', false)
    },
    //切换图片
    handleSwitch(index) {
      // 在这里你可以处理图片切换的逻辑
      this.initialIndex = index;
    },
  }
};
</script>
<style scoped lang="scss">
.lookImage_text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
