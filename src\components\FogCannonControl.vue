<script setup>
import { ref, onMounted, inject, onBeforeUnmount, computed, watch } from 'vue';
import { UpOutlined, DownOutlined, LeftOutlined, RightOutlined, PoweroffOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { getWebSocketInstance } from '../utils/websocket.js';
import { createRequestId, parseRequestId, sendWebSocketMessage } from './device.js';
// 注入GSAP
const gsap = inject('gsap');

// 接收父组件传递的设备数据
const props = defineProps({
  devices: {
    type: Array,
    default: () => []
  }
});

// WebSocket实例
const ws = ref(null);
// 设备状态存储
const deviceStatus = ref({});
// 设备空payload跟踪数据
const deviceEmptyPayloadTracker = ref({});

// 网络状态
const isOnline = ref(navigator.onLine);
const checkNetworkStatus = () => {
  isOnline.value = navigator.onLine;
};

// 监听网络状态变化
onMounted(() => {
  window.addEventListener('online', checkNetworkStatus);
  window.addEventListener('offline', checkNetworkStatus);
});

onBeforeUnmount(() => {
  window.removeEventListener('online', checkNetworkStatus);
  window.removeEventListener('offline', checkNetworkStatus);
  
  // 停止定时查询
  stopStatusPolling();
  
  // 关闭WebSocket连接
  if (ws.value) {
    ws.value.close();
  }
});

// 雾炮设备列表（基于props.devices）
const fogCannons = computed(() => {
  return props.devices.filter(device => device.type == 2).map(device => {
    const status = getDeviceStatus(device);
    return {
      id: device.id,
      ip: device.ip,
      name: device.name || `雾炮${device.id}`,
      ...status
    };
  });
});

// 获取设备状态
const getDeviceStatus = (device) => {
  const status = deviceStatus.value[device.ip];
  if (!status) {
    return getDefaultStatus();
  }
  
  // 协议字段映射到组件字段
  return {
    running: Boolean(status.spray_on),
    totalFlow: status.total_flow || 0,
    instantFlow: status.instant_flow || 0,
    pressure: status.pressure || 0,
    fanStatus: Boolean(status.fan_control),
    pumpStatus: status.fan_control == 256,
    swingStatus: Boolean(status.spray_mode_select),
    swingAngle: `${status.swing_start_angle} ~ ${status.swing_end_angle}`,
    swingStartAngle: status.swing_start_angle,
    swingEndAngle: status.swing_end_angle,
    turnUd: status.turn_ud,
    pitchAngle: 0,
    udAngle: status.ud_angle,
    fanPower: status.fan_power,
    swingPower: status.swing_power || 30,
    faults: {
      fanFault: Boolean(status.fan_fault),
      pumpFault: Boolean(status.pump_fault),
      oilPumpFault: Boolean(status.oil_pump_fault),
      limitProtection: Boolean(status.limit_protection),
      servoFault: Boolean(status.servo_fault),
      emergencyStop: Boolean(status.emergency_stop),
      drainageRunning: Boolean(status.drainage_running),
      waterShortage: Boolean(status.water_shortage)
    }
  };
};



// 判断设备是否在线
const isDeviceOnline = (device) => {
  const deviceIp = typeof device === 'string' ? device : device.ip;
  const status = deviceStatus.value[deviceIp];
  const tracker = deviceEmptyPayloadTracker.value[deviceIp];


  // 如果没有状态记录，认为离线
  if (!status) return false;
  
  // 如果设备状态中明确标记为在线，返回true
  if (status.isOnline === true) return true;
  
  // 如果没有空payload跟踪记录，使用原有的30秒逻辑作为兜底
  if (!tracker) {
    return status.lastUpdate && (Date.now() - status.lastUpdate) < 30000;
  }
  
  // 检查是否连续60秒收到空payload
  if (tracker.emptyPayloadStartTime) {
    const emptyDuration = Date.now() - tracker.emptyPayloadStartTime;
    // 如果连续60秒收到空payload，认为离线
    if (emptyDuration >= 60000) {
      return false;
    }
  }
  
  // 其他情况认为在线
  return true;
};

// 获取默认状态
const getDefaultStatus = () => ({
  direction: 0,
  fan: false,
  pump: false,
  swing: false,
  isOnline: true
});

// 查询所有雾炮设备状态
const queryAllStatus = () => {
  const fogCannonDevices = props.devices.filter(device => device.type == 2);
  fogCannonDevices.forEach(device => {
    queryStatus({ip:device.ip, type: 2});
  });
};

// 查询单个设备状态
const queryStatus = (device) => {
  sendWebSocketMessage(ws.value, 'GET_STATUS', device);
};

const sendControlCommand = (device, type, value) => {
  console.log('device :>> ', device);
  try {
    sendWebSocketMessage(ws.value, 'CONTROL_DEVICE', {ip:device.ip, type: 2}, type, value);
    
    // 延迟查询状态更新
    setTimeout(() => {
      queryStatus({ip:device.ip, type: 2});
    }, 1000);
    
  } catch (error) {
    console.warn('控制雾炮失败:', error.message);
  }
};

// 控制功能
const togglePower = async (cannon) => {
  if (!isDeviceOnline(cannon)) {
    message.warning('设备离线，无法控制');
    return;
  }
  const newValue = cannon.running ? 0 : 256;
  await sendControlCommand(cannon, 'spray_control', newValue);
  
  // 状态变化动画
  const targetCard = document.querySelector(`.cannon-card-${cannon.id}`);
  if (targetCard) {
    gsap.to(targetCard, {
      backgroundColor: '#0D275A',
      duration: 0.5,
      ease: "power2.inOut",
      clearProps: "transform",
      onComplete: () => {
        targetCard.style.transform = "none";
      }
    });

    // 状态标签动画
    const statusTag = targetCard.querySelector('.status-tag');
    if (statusTag) {
      gsap.fromTo(statusTag,
        { scale: 0.8, opacity: 0.7 },
        {
          scale: 1,
          opacity: 1,
          duration: 0.3,
          ease: "back.out(1.7)",
          clearProps: "all" // 清除所有动画属性
        }
      );
    }
  }
};

const toggleFan = async (cannon) => {
  if (!isDeviceOnline(cannon)) {
    message.warning('设备离线，无法控制');
    return;
  }
  
  const newValue = cannon.fanStatus ? 0 : 2<<8;
  await sendControlCommand(cannon, 'fan_control', newValue);
};

const togglePump = async (cannon) => {
  if (!isDeviceOnline(cannon)) {
    message.warning('设备离线，无法控制');
    return;
  }
  
  const newValue = cannon.pumpStatus ? 2<<8 : 2<<7;
  await sendControlCommand(cannon, 'pump_control', newValue);
};

const toggleSwing = async (cannon) => {
  if (!isDeviceOnline(cannon)) {
    message.warning('设备离线，无法控制');
    return;
  }
  
  const newValue = cannon.swingStatus ? 0 : 2<<7;
  await sendControlCommand(cannon, 'spray_mode_select', newValue);
};

const togglePitch = async (cannon) => {
  if (!isDeviceOnline(cannon)) {
    message.warning('设备离线，无法控制');
    return;
  }
  
  const newValue = cannon.pitchStatus ? 0 : 1;
  await sendControlCommand(cannon, 'pitch_control', newValue);
};
const controlChangeFanPower = async (cannon,value ) => {
  // 改变风机频率
  if (!isDeviceOnline(cannon.ip)) {
    message.warning('设备离线，无法控制');
    return;
  }
  const status = deviceStatus.value[cannon.ip];
  await sendControlCommand(cannon, 'fan_power', value);
}

const controlChangeSwingPower = async (cannon, value) => {
  // 改变摇摆频率
  if (!isDeviceOnline(cannon.ip)) {
    message.warning('设备离线，无法控制');
    return;
  }
  const status = deviceStatus.value[cannon.ip];
  await sendControlCommand(cannon, 'swing_power', value);
}

const controlChangeSwingStartAngle = async (cannon, value) => {
  // 改变摇摆角度起始
  if (!isDeviceOnline(cannon.ip)) {
    message.warning('设备离线，无法控制');
    return;
  }
  const status = deviceStatus.value[cannon.ip];
  await sendControlCommand(cannon, 'swing_start_angle', value);
}

const controlChangeSwingEndAngle = async (cannon, value) => {
  // 改变摇摆角度起始
  if (!isDeviceOnline(cannon.ip)) {
    message.warning('设备离线，无法控制');
    return;
  }
  const status = deviceStatus.value[cannon.ip];
  await sendControlCommand(cannon, 'swing_end_angle', value);
}

const controlDirection = async (cannon, direction) => {
  if (!isDeviceOnline(cannon)) {
    message.warning('设备离线，无法控制');
    return;
  }
  const status = deviceStatus.value[cannon.ip];
  let controlType;
  let angle = 0
  switch (direction) {
    case 'up':
      controlType = 'turn_ud';
      angle = status.turn_ud + 1;
      break;
    case 'down':
      angle = status.turn_ud - 1;
      controlType = 'turn_ud';
      break;
    case 'left':
      angle = status.swing_start_angle - 1;
      controlType = 'swing_start_angle';
      break;
    case 'right':
      angle = status.swing_end_angle + 1;
      controlType = 'swing_end_angle';
      break;
    default:
      return;
  }

  await sendControlCommand(cannon, controlType, angle);
  
  // 添加点击动画反馈
  animateDirectionChange(direction);
};

// 方向控制按钮动画
const animateDirectionChange = (direction) => {
  const dirButton = document.querySelector(`.swing-control > .${direction}`);
  if (dirButton) {
    gsap.to(dirButton, {
      scale: 1.2,
      duration: 0.1,
      ease: "power1.out",
      onComplete: () => {
        gsap.to(dirButton, {
          scale: 1,
          duration: 0.2,
          ease: "elastic.out(1, 0.3)"
        });
      }
    });
  }
};

// 初始化WebSocket连接
const initWebSocket = () => {
  ws.value = getWebSocketInstance({
    reconnectInterval: 5000,
    maxReconnectAttempts: 10,
    heartbeatInterval: 30000
  });

  // 监听WebSocket事件
  ws.value.on('open', () => {
    // 连接成功后立即查询所有雾炮状态
    queryAllStatus();
  });

  ws.value.on('message', handleWebSocketMessage);
  
  ws.value.on('error', (error) => {
    console.error('雾炮WebSocket错误:', error);
  });

  ws.value.on('close', () => {
  });

  // 连接到WebSocket服务器
  ws.value.connect('ws://localhost:8080/ws/device');
};

// 处理WebSocket消息
const handleWebSocketMessage = (data) => {
  if (data.type === 'STATUS_UPDATE') {
    // 从requestId中提取设备IP
    const deviceIp = parseRequestId(data.requestId);
    
    if (deviceIp) {
      // 初始化设备跟踪器
      if (!deviceEmptyPayloadTracker.value[deviceIp]) {
        deviceEmptyPayloadTracker.value[deviceIp] = {
          emptyPayloadStartTime: null,
          consecutiveEmptyCount: 0
        };
      }
      
      // 检查payload是否为空或无效
      const isEmptyPayload = !data.payload || Object.keys(data.payload).length === 0;
      if (isEmptyPayload) {
        // 收到空payload
        if (!deviceEmptyPayloadTracker.value[deviceIp].emptyPayloadStartTime) {
          // 开始记录空payload时间
          deviceEmptyPayloadTracker.value[deviceIp].emptyPayloadStartTime = Date.now();
          deviceEmptyPayloadTracker.value[deviceIp].consecutiveEmptyCount = 1;
        } else {
          // 增加连续空payload计数
          deviceEmptyPayloadTracker.value[deviceIp].consecutiveEmptyCount++;
        }
      } else {
        // 收到有效payload，重置空payload跟踪
        deviceEmptyPayloadTracker.value[deviceIp].emptyPayloadStartTime = null;
        deviceEmptyPayloadTracker.value[deviceIp].consecutiveEmptyCount = 0;
        
        // 更新设备状态
        deviceStatus.value[deviceIp] = {
          ...deviceStatus.value[deviceIp],
          ...data.payload,
          lastUpdate: Date.now(),
          isOnline: true
        };
      }
    }
  } else if (data.type === 'CONTROL_RESULT') {
    if (data.payload?.success) {
      // 控制成功后立即查询状态
      setTimeout(() => {
        queryAllStatus();
      }, 10);
    }
  } else if (data.type === 'ERROR') {
    console.error('雾炮操作错误:', data.payload?.message);
  }
};

// 定时查询状态
let statusQueryInterval = null;

// 开始定时查询状态（每10秒）
const startStatusQuery = () => {
  if (statusQueryInterval) {
    clearInterval(statusQueryInterval);
  }
  
  statusQueryInterval = setInterval(() => {
    queryAllStatus();
    checkDeviceOnlineStatus(); // 检查设备在线状态
  }, 10000);
};

// 检查设备在线状态
const checkDeviceOnlineStatus = () => {
  Object.keys(deviceEmptyPayloadTracker.value).forEach(deviceIp => {
    const tracker = deviceEmptyPayloadTracker.value[deviceIp];
    const status = deviceStatus.value[deviceIp];
    
    if (tracker && tracker.emptyPayloadStartTime && status) {
      const emptyDuration = Date.now() - tracker.emptyPayloadStartTime;
      
      // 如果连续60秒收到空payload，标记设备为离线
      if (emptyDuration >= 60000) {
        if (status.isOnline !== false) {
          deviceStatus.value[deviceIp] = {
            ...status,
            isOnline: false
          };
        }
      }
    }
  });
};

// 停止定时查询
const stopStatusPolling = () => {
  if (statusQueryInterval) {
    clearInterval(statusQueryInterval);
    statusQueryInterval = null;
  }
};

// 监听设备数据变化
watch(() => props.devices, (newDevices) => {
  if (newDevices && newDevices.length > 0) {
    // 初始化设备状态
    newDevices.filter(device => device.type == 2).forEach(device => {
      if (!deviceStatus.value[device.ip]) {
        deviceStatus.value[device.ip] = {
          ...getDefaultStatus(),
          lastUpdate: null,
          isOnline: true
        };
      }
      // 初始化空payload跟踪器
      if (!deviceEmptyPayloadTracker.value[device.ip]) {
        deviceEmptyPayloadTracker.value[device.ip] = {
          emptyPayloadStartTime: null,
          consecutiveEmptyCount: 0
        };
      }
    });
    
    // 如果WebSocket已连接，查询状态
    if (ws.value && ws.value.isConnected()) {
      queryAllStatus();
    }
  }
}, { immediate: true });

onMounted(() => {
  // 初始化WebSocket连接
  initWebSocket();
  
  // 开始定时查询状态
  startStatusQuery();
  
  // 卡片入场动画
  const cards = document.querySelectorAll('.cannon-card');
  gsap.from(cards, {
    y: 30,
    opacity: 0,
    duration: 0.6,
    stagger: 0.1,
    ease: "power2.out",
    clearProps: "transform,opacity" // 确保动画结束后清除所有属性
  });

  // 控制按钮入场动画
  const buttons = document.querySelectorAll('.swing-control button');
  gsap.from(buttons, {
    scale: 0,
    opacity: 0,
    duration: 0.5,
    stagger: 0.05,
    delay: 0.3,
    ease: "back.out(1.7)",
    clearProps: "all" // 确保动画结束后清除所有属性
  });
});
</script>

<template>
  <div class="fog-cannon-container">
    <a-row :gutter="[16, 24]">
      <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8" v-for="cannon in fogCannons" :key="cannon.id">
        <a-card
          :title="cannon.name"
          :bordered="false"
          :class="['cannon-card', `cannon-card-${cannon.id}`, {'running': cannon.running}]"
          :headStyle="{color: '#FFFFFF', backgroundColor: '#0D275A', borderBottom: 'none'}"
        >
          <template #extra>
            <span class="network-status" :class="{ 'online': isDeviceOnline(cannon.ip), 'offline': !isDeviceOnline(cannon.ip) }">
              <span class="status-dot"></span>
              {{ isDeviceOnline(cannon.ip) ? '在线' : '离线' }}
            </span>
          </template>
          <div class="device-row">
            <div class="device-info">
              <div style="font-size:13px;line-height:1.8;">
                <div class="info-item">
                  <span class="info-label">设备IP:</span>
                  <span class="info-value">{{ cannon.ip }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">瞬时流量:</span>
                  <span class="info-value">{{ cannon.instantFlow }}m³/h</span>
                </div>
                <div class="info-item">
                  <span class="info-label">累计流量:</span>
                  <span class="info-value">{{ cannon.totalFlow }}m³</span>
                </div>
                <div class="slider-item">
                  <span class="slider-label">摇摆角度起始({{getDeviceStatus(cannon).swingStartAngle}})：</span>
                  <div class="slider-container">
                    <a-slider :value="getDeviceStatus(cannon).swingStartAngle" :min="-160" :max="0" @change="(value) => controlChangeSwingStartAngle(cannon, value)"/>
                  </div>
                </div>
                <div class="slider-item">
                  <span class="slider-label">摇摆角度终止({{getDeviceStatus(cannon).swingEndAngle}})：</span>
                  <div class="slider-container">
                    <a-slider :value="getDeviceStatus(cannon).swingEndAngle" :min="0" :max="160" @change="(value) => controlChangeSwingEndAngle(cannon, value)"/>
                  </div>
                </div>
                <div class="slider-item">
                  <span class="slider-label">风机频率({{getDeviceStatus(cannon).fanPower}})：</span>
                  <div class="slider-container">
                    <a-slider :value="getDeviceStatus(cannon).fanPower" :min="30" :max="50" @change="(value) => controlChangeFanPower(cannon, value)"/>
                  </div>
                </div>
                <div class="slider-item">
                  <span class="slider-label">摇摆频率({{getDeviceStatus(cannon).swingPower}})：</span>
                  <div class="slider-container">
                    <a-slider :value="getDeviceStatus(cannon).swingPower" :min="30" :max="50" @change="(value) => controlChangeSwingPower(cannon, value)"/>
                  </div>
                </div>
              </div>
            </div>
            <div class="swing-control-row">
              <div class="swing-control">
                <a-button
                  class="up control-btn direction-btn"
                  size="large"
                  shape="circle"
                  @click="controlDirection(cannon, 'up')"
                  :disabled="!isDeviceOnline(cannon.ip)"
                >
                  <UpOutlined />
                </a-button>
                <div class="angle-display">
                  <span class="angle-value">{{ cannon.turnUd }}°</span>
                </div>
                <a-button
                  class="down control-btn direction-btn"
                  size="large"
                  shape="circle"
                  @click="controlDirection(cannon, 'down')"
                  :disabled="!isDeviceOnline(cannon.ip)"
                >
                  <DownOutlined />
                </a-button>
              </div>
            </div>
          </div>
          <div class="switch-row">
            <span style="color: #FFFFFF;">风机:</span>
            <a-switch :checked="cannon.fanStatus" @change="toggleFan(cannon)" :disabled="!isDeviceOnline(cannon.ip)"
                     active-color="#2ECC71" inactive-color="#6C5B7B" />
            <span style="margin-left:8px; color: #FFFFFF;">水泵:</span>
            <a-switch :checked="cannon.pumpStatus" @change="togglePump(cannon)" :disabled="!isDeviceOnline(cannon.ip)"
                     active-color="#2ECC71" inactive-color="#6C5B7B" />
            <span style="margin-left:8px; color: #FFFFFF;">摇动:</span>
            <a-switch :checked="cannon.swingStatus" @change="toggleSwing(cannon)" :disabled="!isDeviceOnline(cannon.ip)"
                     active-color="#2ECC71" inactive-color="#6C5B7B" />
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<style scoped>
/* 网络状态样式 */
.network-status {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  margin-right: 8px;
  transition: all 0.3s;
}

.network-status .status-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 4px;
}

.network-status.online {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.network-status.online .status-dot {
  background-color: #2ecc71;
  box-shadow: 0 0 6px #2ecc71;
}

.network-status.offline {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.network-status.offline .status-dot {
  background-color: #e74c3c;
  box-shadow: 0 0 6px #e74c3c;
}

/* 卡片悬停效果 */
.cannon-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.5s ease;
}

.cannon-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 20px rgba(0,0,0,0.4);
}

.cannon-card.running {
  background-color: #0D275A;
}

/* 方向控制按钮 */
.direction-btn {
  background-color: #FFFFFF !important;
  border-color: #D9D9D9 !important;
  color: #000000 !important;
  transition: all 0.2s ease;
}

.direction-btn:not([disabled]):hover {
  background-color: #3498DB !important;
  border-color: #3498DB !important;
  color: #FFFFFF !important;
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0,0,0,0.3);
}

/* 禁用状态下的方向按钮 */
.direction-btn[disabled] {
  background-color: #F5F5F5 !important;
  border-color: #D9D9D9 !important;
  color: #BFBFBF !important;
  cursor: not-allowed;
  box-shadow: none;
}

/* 按钮悬停效果 */
.control-btn {
  transition: transform 0.2s ease, box-shadow 0.2s ease, background-color 0.2s ease;
}

.control-btn:not([disabled]):hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0,0,0,0.3);
  background-color: #2980B9 !important;
}

.control-btn:not([disabled]):active {
  transform: translateY(1px);
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* 状态标签样式 */
.status-tag {
  transition: all 0.3s ease;
  margin: 0;
  border: none;
  color: #FFFFFF !important;
}

/* 运行中状态标签 */
.running-tag {
  background-color: #2ECC71 !important;
}

/* 已停止状态标签 */
.stopped-tag {
  background-color: #E74C3C !important;
}

/* 移除点击后的状态变化 */
.status-tag:active,
.status-tag:focus,
.status-tag:hover {
  opacity: 0.9;
  transform: scale(1.05);
}

/* 开关动画 */
.ant-switch {
  transition: all 0.4s ease !important;
}
.ant-switch-checked {
  transform: scale(1.05);
}

/* 开关按钮禁用状态 */
.ant-switch-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #F5F5F5 !important;
}

/* 电源按钮效果 */
.power-btn {
  transition: all 0.3s ease;
}
.power-btn:hover {
  background-color: #2980B9 !important;
}

/* 禁用状态下的按钮 */
.ant-btn[disabled], 
.ant-btn[disabled]:hover, 
.ant-btn[disabled]:focus, 
.ant-btn[disabled]:active {
  background-color: #F5F5F5 !important;
  border-color: #D9D9D9 !important;
  color: #BFBFBF !important;
  cursor: not-allowed;
  text-shadow: none;
  box-shadow: none;
}

.fog-cannon-container {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

.section-title {
  margin-bottom: 20px;
  color: #FFFFFF;
  font-weight: bold;
}

.cannon-card {
  background: #0D275A;
  box-shadow: 0 8px 16px rgba(0,0,0,0.3);
  border-radius: 12px;
  height: auto;
  min-height: 280px; /* 增加最小高度 */
  margin: 0;
  color: #FFFFFF;
  border: none;
  display: flex;
  flex-direction: column;
  transform: none !important; /* 防止transform属性影响布局 */
  will-change: auto; /* 优化动画性能 */
}

.status-container, .fault-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.status-tag, .fault-tag {
  margin-bottom: 8px;
}

.control-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .control-buttons {
    flex-direction: column;
  }

  .control-buttons button {
    margin-bottom: 8px;
  }
  .cannon-card {
    margin-bottom: 4px;
    padding: 4px 0;
  }
  .section-title {
    margin-bottom: 8px;
    font-size: 16px;
  }
  .a-row {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  .a-col {
    padding-left: 2px !important;
    padding-right: 2px !important;
  }
  .info-control-row {
    gap: 4px;
  }
  .device-info {
    min-width: 0;
  }
  .switch-row {
    width: 100%;
    margin-top: 12px;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
  }
  .device-info, .swing-control-row {
    width: 50%;
    min-width: 0;
  }
  .info-control-row {
    flex-direction: row;
    align-items: flex-start;
  }
  .device-row {
    gap: 4px;
  }
  .device-info, .swing-control-row {
    width: 50%;
    min-width: 0;
  }
}

.device-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  flex: 1; /* 占用剩余空间 */
}
.device-info {
  flex: 1;
}
.switch-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.swing-control-row {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 70px;
}
.swing-control {
  align-items: center;
  justify-items: center;
}
.swing-control > .up {
  grid-column: 2;
  grid-row: 1;
}
.swing-control > .left {
  grid-column: 1;
  grid-row: 2;
}
.swing-control > .right {
  grid-column: 3;
  grid-row: 2;
}
.swing-control > .down {
  grid-column: 2;
  grid-row: 3;
}

/* 信息项对齐样式 */
.info-item {
  display: flex;
  align-items: center;
}

.info-label {
  display: inline-block;
  width: 110px;
  text-align: left;
  color: #FFFFFF;
  font-size: 13px;
  flex-shrink: 0;
}

.info-value {
  color: #FFFFFF;
  font-size: 13px;
  margin-left: 8px;
}

/* 滑块项对齐样式 */
.slider-item {
  display: flex;
  align-items: center;
}

.slider-label {
  display: inline-block;
  width: 110px;
  text-align: left;
  color: #FFFFFF;
  font-size: 13px;
  flex-shrink: 0;
  white-space: nowrap;
}

.slider-container {
  flex: 1;
  min-width: 0;
}

/* 角度显示样式 */
.angle-display {
  margin: 10px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #FFFFFF;
  font-size: 12px;
  min-width: 60px;
}

.angle-label {
  font-size: 11px;
  color: #B0C4DE;
  margin-bottom: 2px;
  white-space: nowrap;
}

.angle-value {
  font-size: 14px;
  font-weight: bold;
  color: #FFFFFF;
}
</style>
