<template>
    <div class="searchQueryParams">
        <el-row>
            <el-col :span="21">
                <el-form :inline="true" label-width="auto" ref="queryForm">
                    <el-form-item :label="item.name" v-for="item, index in searchListComputed" :key="item.id">
                        <!-- 值 -->
                        <schemeQueryInput :dicts='dicts' :val.sync="item" v-model="item.select"
                            @keyReturnEvent="keyReturnEvent" @handleQuery="searchForClick" :operatorOptions="compType">
                        </schemeQueryInput>
                    </el-form-item>
                    <!--  按钮    -->
                    <el-form-item>
                        <el-button v-if="searchListLength >= 4 && searchListLength > formItemNum"
                            :icon="searchStatus ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" size="mini"
                            @click="searchHide" plain>{{ searchStatus ? '收起' : '展开' }}</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
            <el-col :span="3">
                <div class="searchBtn">
                    <el-button icon="el-icon-search" size="mini" type="primary" @click="searchForClick">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resettingClick">重置</el-button>
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import searchMixin from "@/components/A_custom/mixins/queryParams.js"
export default {
    mixins: [searchMixin],
    provide() { return { deep: () => { return this.deep } } },
    props: {
        show: {
            type: Boolean,
            default: () => { return true },
        },
        searchList: {
            type: Array,
            default: () => { return [] },
        },
        keys: {
            type: String,
            default: () => { return String(Number(new Date())) }
        },
        showBut: {
            type: Boolean,
            default: () => { return true }
        },

        //后端未加
        formItemNum: {
            type: Number,
            default: () => { return 2 },
        },
        dicts: {
            type: Object,
            default: () => { return {} },
        },
        defaultQueryKeys: {
            type: Object,
            default: () => { return {} }
        },
        //比较符类型
        compType: {
            type: Array,
            default: () => { return [] }
        }
    },
    data() {
        return {
            checkedCitiesList: [],
            schemeName: "",//方案名称
            deep: {
                resetting: true, //重置事件执行
            },
            setUpJudge: false, //自定义方案切换
            showJudge: false,
            oneJudge: true,
            butTitle: "显示筛选",
            plankeys: "全部条件",
            planList: [],//方案的数组
            //查询条件
            queryParams: {},
            defaultSchemeObj: {},////初始查询对象，里面包含数组
            searchStatus: false,
            searchListLength: 0,
            newsearchList: []
        }
    },
    created() {
        this.showJudge = this.show;
        //默认是否是展开
        this.oneEvent()
    },
    computed: {
        // 方案查询回显，控制默认值
        searchListComputed() {
            this.newsearchList = this.searchList
            if (this.newsearchList && this.searchList.length != 0) {
                const isDefaultlocalStorage = JSON.parse(localStorage.getItem('搜索方案' + this.keys)).filter(item => item.isDefault == true)[0]
                // let list = this.searchList.filter(x => { return (x.isDefault && x.selectable && isDefaultlocalStorage.ids.includes(x.id)) })
                let list = this.searchList.filter(x => { return (x.selectable && isDefaultlocalStorage.ids.includes(x.id)) })
                this.searchListLength = isDefaultlocalStorage.ids.length;
                if (!this.searchStatus) {
                    return list.sort((Old, New) => { return Old.searchSort - New.searchSort }).slice(0, this.formItemNum)
                }
                let arr = list.sort((Old, New) => { return Old.searchSort - New.searchSort })
                return arr;
            }
        }
    },
    methods: {
        // 更新searchListComputed数据
        upDataSearchListComputed() {
            this.newsearchList = this.searchList.map(item => {
                return item;
            });
        },
        searchHide() {
            this.searchStatus = !this.searchStatus
        },
        // 刷新
        refresh() {
            this.$emit('getList')
        },
        oneEvent() {
            const searchSchemeObj = localStorage.getItem('搜索方案' + this.keys);
            let searchSchemeParseObj = JSON.parse(searchSchemeObj) || [];
            // 按 searchSort 进行排序，并在 searchSort 相同的情况下按原数组位置排序
            let ids = this.searchList
                .map((x, index) => ({ ...x, originalIndex: index }))
                .filter(x => x.selectable)
                .sort((a, b) => {
                    if (a.searchSort !== b.searchSort) {
                        return a.searchSort - b.searchSort;
                    }
                    return a.originalIndex - b.originalIndex;
                })
                .map(x => x.id);

            // 按 searchSort 进行排序，并在 searchSort 相同的情况下按原数组位置排序
            let isDefaultIds = this.searchList
                .map((x, index) => ({ ...x, originalIndex: index }))
                .filter(x => x.isDefault)
                .sort((a, b) => {
                    if (a.searchSort !== b.searchSort) {
                        return a.searchSort - b.searchSort;
                    }
                    return a.originalIndex - b.originalIndex;
                })
                .map(x => x.id);
            try {
                if (searchSchemeParseObj.length == 0) {
                    searchSchemeParseObj = [{ name: '全部条件', ids: ids, isDefault: false }, { name: '方案一', ids: isDefaultIds, isDefault: true }]
                    // 存储到 localStorage
                    localStorage.setItem('搜索方案' + this.keys, JSON.stringify(searchSchemeParseObj));
                } else {
                    // let oldData = JSON.stringify(searchSchemeParseObj.filter(x => x.name == '方案一')[0].ids)
                    // let newData = JSON.stringify(isDefaultIds)
                    // if (oldData !== newData) {
                    //     searchSchemeParseObj = [{ name: '全部条件', ids: ids, isDefault: false }, { name: '方案一', ids: isDefaultIds, isDefault: true }]
                    //     // 存储到 localStorage
                    //     localStorage.setItem('搜索方案' + this.keys, JSON.stringify(searchSchemeParseObj));
                    // }

                    // 获取本地存储中当前 isDefault 为 true 的方案
                    let localDefaultScheme = searchSchemeParseObj.find(x => x.isDefault === true);

                    // 获取当前存储的 "全部条件" 和 "方案一"
                    let oldAllConditions = JSON.stringify(searchSchemeParseObj.find(x => x.name === '全部条件')?.ids);
                    let oldSchemeOne = JSON.stringify(searchSchemeParseObj.find(x => x.name === '方案一')?.ids);

                    // 新的全部条件和方案一
                    let newAllConditions = JSON.stringify(ids);
                    let newSchemeOne = JSON.stringify(isDefaultIds);

                    // 比较本地存储的 "全部条件" 和新的 "全部条件"
                    if (oldAllConditions !== newAllConditions || oldSchemeOne !== newSchemeOne) {
                        searchSchemeParseObj = searchSchemeParseObj.map(scheme => {
                            if (scheme.name === '全部条件') {
                                // 更新 "全部条件" 并设为非默认
                                return { ...scheme, ids: ids, isDefault: false };
                            } else if (scheme.name === '方案一') {
                                // 更新 "方案一"，设为非默认
                                return { ...scheme, ids: isDefaultIds, isDefault: false };
                            } else {
                                // 其他方案，检查 ids 是否都在新的 "全部条件" 中
                                let filteredIds = scheme.ids.filter(id => ids.includes(id));
                                return { ...scheme, ids: filteredIds, isDefault: false };
                            }
                        });

                        // 确保 "全部条件" 和 "方案一" 存在，如果不存在则添加
                        if (!searchSchemeParseObj.find(scheme => scheme.name === '全部条件')) {
                            searchSchemeParseObj.unshift({ name: '全部条件', ids: ids, isDefault: false });
                        }
                        if (!searchSchemeParseObj.find(scheme => scheme.name === '方案一')) {
                            searchSchemeParseObj.push({ name: '方案一', ids: isDefaultIds, isDefault: false });
                        }
                        // 设置 isDefault 为本地存储中默认方案的 isDefault
                        searchSchemeParseObj = searchSchemeParseObj.map(scheme => {
                            if (scheme.name === localDefaultScheme.name) {
                                return { ...scheme, isDefault: true }; // 保持本地默认方案为 true
                            }
                            return scheme;
                        });
                        // 存储到 localStorage
                        localStorage.setItem('搜索方案' + this.keys, JSON.stringify(searchSchemeParseObj));
                    }
                }
                this.getCacheScheme();
            } catch (error) {
                console.log(error)
                localStorage.removeItem('搜索方案' + this.keys);
                searchSchemeParseObj = [{ name: '全部条件', ids: ids, isDefault: false }, { name: '方案一', ids: isDefaultIds, isDefault: true }]
                // 存储到 localStorage
                localStorage.setItem('搜索方案' + this.keys, JSON.stringify(searchSchemeParseObj));
            }

        },
        getCacheScheme() {
            // 页面进来判断是否 有搜索方案，如没有，先处理方案，后存储上去
            const searchSchemeObj = localStorage.getItem('搜索方案' + this.keys);
            this.defaultSchemeObj = JSON.parse(searchSchemeObj);
            this.planList = Object.keys(this.defaultSchemeObj).reverse().map((x, i) => { return { name: x, id: i } })
        },
        // 重置
        resettingClick() {
            this.$router.replace({ query: {} });
            this.deep.resetting = !this.deep.resetting
            this.$emit('change', { type: 2, name: "重置" });
        },
        // 处理数组形式
        processingArrays(list) {
            list.forEach(x => {
                if (Array.isArray(x.value)) {
                    x.value = x.value.join(",");
                }
            });
            return list
        },
        // 判断范围是否都填了
        isValidTimeRange(str) {
            const parts = str.split(",");
            if (parts.length !== 2) {
                return false;
            }
            const beforeComma = parts[0].trim();
            const afterComma = parts[1].trim();
            return beforeComma.endsWith("00:00:00") && afterComma.endsWith("23:59:59");
        },
        // 搜索
        searchForClick() {
            let newList = this.searchList.filter(x => {
                if (x.value) {
                    return x.select = true
                }
            })
            this.$emit('change', { type: 1, name: "搜索", data: this.processingArrays(JSON.parse(JSON.stringify(newList))) });
        },
    },
}
</script>

<style lang="scss">
.searchQueryParams {
    padding: 0 10px;


    .el-form-item {
        margin-bottom: 5px;
    }

    .searchBtn {
        padding: 5px 0;
        display: flex;
        justify-content: flex-end;
    }

    .el-form-item__label {
        color: #4C4C4C;
        font-size: 12px;
    }
}
</style>