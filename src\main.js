import Vue from 'vue'
import * as echarts from 'echarts';
import Cookies from 'js-cookie'

import Element from 'element-ui'
import './assets/styles/element-variables.scss'

import './assets/iconfont/iconfont.css' //自定义图标
import '@/assets/styles/index.scss' // global css
import '@/assets/styles/ruoyi.scss' // ruoyi css
import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive
import plugins from './plugins' // plugins
import { download } from '@/utils/request'


import './assets/icons' // icon
import './permission' // permission control

import { getDicts } from "@/api/system/dict/data";
import { getConfigKey } from "@/api/system/config";
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree } from "@/utils/ruoyi";
// 分页组件
import Pagination from "@/components/Pagination";
// 自定义表格工具组件
import RightToolbar from "@/components/RightToolbar"
// 富文本组件
import Editor from "@/components/Editor"
// 文件上传组件
import FileUpload from "@/components/FileUpload"
// 图片上传组件
import ImageUpload from "@/components/ImageUpload"
// 图片预览组件
import ImagePreview from "@/components/ImagePreview"
// 字典标签组件
import DictTag from '@/components/DictTag'
//表单右键
import rightClick from '@/components/A_custom/C_rightOption'
//自定义图片预览下载
import ImagePreviewDow from "@/components/imagePreviewDow"
// 头部标签组件
import VueMeta from 'vue-meta'
// 字典数据组件
import DictData from '@/components/DictData'
import searchForm from "@/components/A_custom/A_searchForm/index"
import searchForm2 from "@/components/A_custom/E_searchQueryParams/index"
import defineTable from "@/components/A_custom/B_defineTable/index"
import rightOption from "@/components/A_custom/C_rightOption/index"
import showInput from "@/components/showInput/index"
import dropDownDisplay from "@/components/A_custom/D_public/dropDownDisplay/index"
import frameInput from "@/components/A_custom/A_searchForm/operatorInput/frameInput/index"
import baseFrame from "@/components/A_custom/A_searchForm/operatorInput/frameInput/baseFrame/index"
import searchInput from "@/components/searchInput/index"

Vue.component('searchInput', searchInput);
Vue.component('baseFrame', baseFrame);
Vue.component('frameInput', frameInput);
Vue.component('dropDownDisplay', dropDownDisplay);
Vue.component('showInput', showInput);
Vue.component('searchForm', searchForm);
Vue.component('searchForm2', searchForm2);
Vue.component('defineTable', defineTable);
Vue.component('rightOption', rightOption);
// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree
Vue.prototype.echarts = echarts
// 全局组件挂载
Vue.component('DictTag', DictTag)
Vue.component('Pagination', Pagination)
Vue.component('RightToolbar', RightToolbar)
Vue.component('Editor', Editor)
Vue.component('FileUpload', FileUpload)
Vue.component('ImageUpload', ImageUpload)
Vue.component('ImagePreview', ImagePreview)
Vue.component('rightClick', rightClick)
Vue.component('ImagePreviewDow', ImagePreviewDow)

import complete from '@/mixins/complete.js'

Vue.mixin(complete)

Vue.use(directive)
Vue.use(plugins)
Vue.use(echarts)
Vue.use(VueMeta)
DictData.install()

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})

//oss存储桶数据
Vue.prototype.ossData = {
  Bucket:'chw-public-1307727660',
  Region:'ap-nanjing'
} 