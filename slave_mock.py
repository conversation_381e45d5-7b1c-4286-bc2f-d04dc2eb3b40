from pymodbus.server import StartTcpServer
from pymodbus.datastore import ModbusSequentialDataBlock
from pymodbus.datastore import ModbusSlaveContext, ModbusServerContext
from pymodbus.device import ModbusDeviceIdentification
import threading
import time

class PLCSimulator:
    def __init__(self, host='localhost', port=502):
        self.host = host
        self.port = port
        self.server = None
        self.thread = None
        
        # 初始化数据存储
        # 线圈 (Coils, 0x0000-0xFFFF)
        coils = ModbusSequentialDataBlock(4006, [0]*100)  # 初始化为0
        
        # 保持寄存器 (Holding Registers, 0x40060-0x4FFFF)
        registers = ModbusSequentialDataBlock(4006, [0]*100)
        
        # 设置浮点数示例 (VD4012 = 3.14)
        import struct
        float_value = 3.14
        float_bytes = struct.pack('>f', float_value)  # 大端序
        reg_high = (float_bytes[0] << 8) | float_bytes[1]
        reg_low = (float_bytes[2] << 8) | float_bytes[3]
        registers.setValues(4012, [reg_high, reg_low])
        
        # 创建从站上下文
        store = ModbusSlaveContext(
            di=None,          # 离散输入 (可选)
            co=coils,         # 线圈
            hr=registers,     # 保持寄存器
            ir=None           # 输入寄存器 (可选)
        )
        
        # 创建服务器上下文
        self.context = ModbusServerContext(slaves=store, single=True)
        
        # 设置服务器标识
        self.identity = ModbusDeviceIdentification()
        self.identity.VendorName = 'PyModbus'
        self.identity.ProductCode = 'PM'
        self.identity.VendorUrl = 'https://github.com/riptideio/pymodbus/'
        self.identity.ProductName = 'Modbus Server'
        self.identity.ModelName = 'pymodbus Server'
        self.identity.MajorMinorRevision = '3.9.2'
    
    def start(self):
        """启动服务器线程"""
        self.thread = threading.Thread(target=self._run_server)
        self.thread.daemon = True
        self.thread.start()
        print(f"Modbus TCP服务器已启动: {self.host}:{self.port}")
    
    def _run_server(self):
        """运行服务器的内部方法"""
        self.server = StartTcpServer(
            context=self.context,
            identity=self.identity,
            address=(self.host, self.port)
        )
    
    def stop(self):
        """停止服务器"""
        if self.server and self.server.is_running:
            self.server.shutdown()
            self.thread.join(timeout=2.0)
            print("Modbus TCP服务器已停止")
    
    def set_coil(self, address, value):
        """设置线圈值"""
        self.context[0].setValues(1, address, [value])  # 1表示线圈类型
    
    def set_register(self, address, value):
        """设置寄存器值"""
        self.context[0].setValues(3, address, [value])  # 3表示保持寄存器类型

# 示例使用
if __name__ == "__main__":
    simulator = PLCSimulator()
    simulator.start()
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        simulator.stop()