{"name": "figma", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview --host 0.0.0.0", "serve": "vite preview --host 0.0.0.0 --port 8080"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@vueuse/core": "^13.1.0", "animate.css": "^4.1.1", "ant-design-vue": "^3.2.20", "axios": "^1.11.0", "echarts": "^5.6.0", "gsap": "^3.13.0", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "vite": "^6.3.5"}}