# mist_cannon_simulator.py
# -*- coding: utf-8 -*-

"""
雾炮机 Modbus 从机模拟器
功能: 模拟8个雾炮机设备，IP地址从*************到*************
依赖: pymodbus (请先通过 pip install pymodbus 安装)
"""

import time
import threading
import random
from pymodbus.server import StartTcpServer
from pymodbus.device import ModbusDeviceIdentification
from pymodbus.datastore import ModbusSequentialDataBlock, ModbusSlaveContext, ModbusServerContext
from pymodbus.constants import Endian
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 雾炮机寄存器地址映射 (4x寄存器映射模式，地址从40001开始)
MC_REGS_BASE = {
    # 控制寄存器 (开关量)
    "spray_control": 40001,        # 喷雾控制 (1启动, 0停止)
    "spray_mode_select": 40002,    # 喷射方式 (1摇摆, 0定点)
    "fan_running": 40003,          # 风机运行状态
    "pump_running": 40004,         # 水泵运行状态
    "rising": 40005,               # 上升状态
    "falling": 40006,              # 下降状态
    "rotating_left": 40007,        # 左转状态
    "rotating_right": 40008,       # 右转状态
    "manual_mode": 40009,          # 手动模式
    "remote_controller_mode": 40010, # 遥控器模式
    "remote_mode": 40011,          # 远程模式
    "fault": 40012,                # 故障状态
    "rc_running": 40013,           # 遥控运行状态
    "water_shortage_fault": 40014, # 缺水故障
    "set_zero_position": 40015,    # 设置零位
    "fault_reset": 40016,          # 故障复位
    
    # 角度寄存器
    "swing_start_angle": 40017,     # 摇摆起始角度
    "swing_end_angle": 40019,       # 摇摆结束角度
    "current_rotation_pos": 40020,  # 当前旋转位置
    "current_pitch_pos": 40021,     # 当前俯仰位置
    "fixed_rotation_angle": 40022,  # 定点旋转角度
    "fixed_pitch_angle": 40023,     # 定点俯仰角度
    "left_limit_set": 40026,        # 左限位设置
    "right_limit_set": 40027,       # 右限位设置
    "pulses_per_revolution": 40028, # 每转脉冲数
}

class MistCannonSimulator:
    """雾炮机模拟器"""
    
    def __init__(self, device_id):
        self.device_id = device_id
        self.ip = f"192.168.2.{100 + device_id}"
        
        # 初始化寄存器数据
        self.registers = {}
        self.init_registers()
        
        # 运行状态
        self.running = False
        self.spray_active = False
        self.swing_mode = False
        self.current_rotation = 0
        self.current_pitch = 0
        self.swing_direction = 1  # 1为正向，-1为反向
        
        # 创建数据存储
        self.create_datastore()
        
    def init_registers(self):
        """初始化寄存器默认值"""
        # 控制寄存器初始值
        self.registers[0] = 0    # spray_control
        self.registers[1] = 0    # spray_mode_select
        self.registers[2] = 0    # fan_running
        self.registers[3] = 0    # pump_running
        self.registers[4] = 0    # rising
        self.registers[5] = 0    # falling
        self.registers[6] = 0    # rotating_left
        self.registers[7] = 0    # rotating_right
        self.registers[8] = 1    # manual_mode (默认手动模式)
        self.registers[9] = 0    # remote_controller_mode
        self.registers[10] = 0   # remote_mode
        self.registers[11] = 0   # fault
        self.registers[12] = 0   # rc_running
        self.registers[13] = 0   # water_shortage_fault
        self.registers[14] = 0   # set_zero_position
        self.registers[15] = 0   # fault_reset
        
        # 角度寄存器初始值
        self.registers[16] = self.to_unsigned(-90)  # swing_start_angle
        self.registers[18] = self.to_unsigned(90)   # swing_end_angle
        self.registers[19] = 0   # current_rotation_pos
        self.registers[20] = 0   # current_pitch_pos
        self.registers[21] = 0   # fixed_rotation_angle
        self.registers[22] = 0   # fixed_pitch_angle
        self.registers[25] = self.to_unsigned(-180) # left_limit_set
        self.registers[26] = self.to_unsigned(180)  # right_limit_set
        self.registers[27] = 3600 # pulses_per_revolution
        
    def to_unsigned(self, signed_value):
        """将有符号16位整数转换为无符号16位整数"""
        if signed_value < 0:
            return signed_value + 65536
        return signed_value
        
    def to_signed(self, unsigned_value):
        """将无符号16位整数转换为有符号16位整数"""
        if unsigned_value > 32767:
            return unsigned_value - 65536
        return unsigned_value
        
    def create_datastore(self):
        """创建Modbus数据存储"""
        # 创建保持寄存器数据块 (最大100个寄存器)
        hr_block = ModbusSequentialDataBlock(0, [0] * 100)
        
        # 初始化寄存器值
        for addr, value in self.registers.items():
            hr_block.setValues(addr, [value])
            
        # 创建从机上下文
        self.slave_context = ModbusSlaveContext(
            di=ModbusSequentialDataBlock(0, [0] * 100),  # 离散输入
            co=ModbusSequentialDataBlock(0, [0] * 100),  # 线圈
            hr=hr_block,  # 保持寄存器
            ir=ModbusSequentialDataBlock(0, [0] * 100)   # 输入寄存器
        )
        
    def update_simulation(self):
        """更新模拟状态"""
        while self.running:
            try:
                # 检查喷雾控制状态
                spray_control = self.slave_context.getValues(3, 0, 1)[0]
                if spray_control == 1536:  # 启动喷雾
                    if not self.spray_active:
                        self.spray_active = True
                        logger.info(f"雾炮机 {self.device_id} ({self.ip}): 启动喷雾")
                        # 启动风机和水泵
                        self.slave_context.setValues(3, 2, [1])  # fan_running
                        self.slave_context.setValues(3, 3, [1])  # pump_running
                elif spray_control == 0:  # 停止喷雾
                    if self.spray_active:
                        self.spray_active = False
                        logger.info(f"雾炮机 {self.device_id} ({self.ip}): 停止喷雾")
                        # 停止风机和水泵
                        self.slave_context.setValues(3, 2, [0])  # fan_running
                        self.slave_context.setValues(3, 3, [0])  # pump_running
                
                # 检查喷射模式
                spray_mode = self.slave_context.getValues(3, 1, 1)[0]
                swing_mode = (spray_mode == 256)  # 摇摆模式
                
                if self.spray_active:
                    if swing_mode:
                        # 摇摆模式 - 模拟摇摆运动
                        self.simulate_swing_motion()
                    else:
                        # 定点模式 - 移动到固定角度
                        self.simulate_fixed_position()
                
                # 检查故障复位
                fault_reset = self.slave_context.getValues(3, 15, 1)[0]
                if fault_reset == 1:
                    logger.info(f"雾炮机 {self.device_id} ({self.ip}): 故障复位")
                    self.slave_context.setValues(3, 11, [0])  # 清除故障状态
                    self.slave_context.setValues(3, 13, [0])  # 清除缺水故障
                    self.slave_context.setValues(3, 15, [0])  # 清除复位信号
                
                # 检查设置零位
                set_zero = self.slave_context.getValues(3, 14, 1)[0]
                if set_zero == 1:
                    logger.info(f"雾炮机 {self.device_id} ({self.ip}): 设置零位")
                    self.current_rotation = 0
                    self.current_pitch = 0
                    self.slave_context.setValues(3, 19, [0])  # current_rotation_pos
                    self.slave_context.setValues(3, 20, [0])  # current_pitch_pos
                    self.slave_context.setValues(3, 14, [0])  # 清除零位设置信号
                
                # 随机模拟一些故障（低概率）
                if random.random() < 0.001:  # 0.1% 概率
                    if random.choice([True, False]):
                        self.slave_context.setValues(3, 13, [1])  # 缺水故障
                        logger.warning(f"雾炮机 {self.device_id} ({self.ip}): 缺水故障")
                
                time.sleep(0.1)  # 100ms 更新周期
                
            except Exception as e:
                logger.error(f"雾炮机 {self.device_id} 模拟更新错误: {e}")
                time.sleep(1)
    
    def simulate_swing_motion(self):
        """模拟摇摆运动"""
        # 获取摇摆范围
        start_angle = self.to_signed(self.slave_context.getValues(3, 16, 1)[0])
        end_angle = self.to_signed(self.slave_context.getValues(3, 18, 1)[0])
        
        # 模拟摇摆运动
        if self.swing_direction > 0:
            self.current_rotation += 2  # 每次增加2度
            if self.current_rotation >= end_angle:
                self.swing_direction = -1
                self.slave_context.setValues(3, 7, [0])  # 停止右转
                self.slave_context.setValues(3, 6, [1])  # 开始左转
        else:
            self.current_rotation -= 2  # 每次减少2度
            if self.current_rotation <= start_angle:
                self.swing_direction = 1
                self.slave_context.setValues(3, 6, [0])  # 停止左转
                self.slave_context.setValues(3, 7, [1])  # 开始右转
        
        # 更新当前位置
        self.slave_context.setValues(3, 19, [self.to_unsigned(self.current_rotation)])
    
    def simulate_fixed_position(self):
        """模拟定点位置"""
        # 获取目标角度
        target_rotation = self.to_signed(self.slave_context.getValues(3, 21, 1)[0])
        target_pitch = self.to_signed(self.slave_context.getValues(3, 22, 1)[0])
        
        # 模拟移动到目标位置
        if abs(self.current_rotation - target_rotation) > 1:
            if self.current_rotation < target_rotation:
                self.current_rotation += 1
                self.slave_context.setValues(3, 7, [1])  # 右转
                self.slave_context.setValues(3, 6, [0])  # 停止左转
            else:
                self.current_rotation -= 1
                self.slave_context.setValues(3, 6, [1])  # 左转
                self.slave_context.setValues(3, 7, [0])  # 停止右转
        else:
            self.slave_context.setValues(3, 6, [0])  # 停止左转
            self.slave_context.setValues(3, 7, [0])  # 停止右转
        
        if abs(self.current_pitch - target_pitch) > 1:
            if self.current_pitch < target_pitch:
                self.current_pitch += 1
                self.slave_context.setValues(3, 4, [1])  # 上升
                self.slave_context.setValues(3, 5, [0])  # 停止下降
            else:
                self.current_pitch -= 1
                self.slave_context.setValues(3, 5, [1])  # 下降
                self.slave_context.setValues(3, 4, [0])  # 停止上升
        else:
            self.slave_context.setValues(3, 4, [0])  # 停止上升
            self.slave_context.setValues(3, 5, [0])  # 停止下降
        
        # 更新当前位置
        self.slave_context.setValues(3, 19, [self.to_unsigned(self.current_rotation)])
        self.slave_context.setValues(3, 20, [self.to_unsigned(self.current_pitch)])
    
    def start(self):
        """启动模拟器"""
        self.running = True

        # 启动模拟更新线程
        self.simulation_thread = threading.Thread(target=self.update_simulation)
        self.simulation_thread.daemon = True
        self.simulation_thread.start()

        # 创建服务器上下文
        context = ModbusServerContext(slaves={1: self.slave_context}, single=True)

        # 设备标识
        identity = ModbusDeviceIdentification()
        identity.VendorName = 'HNTY'
        identity.ProductCode = 'MC'
        identity.VendorUrl = 'http://github.com/riptideio/pymodbus/'
        identity.ProductName = f'Mist Cannon Simulator {self.device_id}'
        identity.ModelName = f'MC-{self.device_id:03d}'
        identity.MajorMinorRevision = '1.0'

        logger.info(f"启动雾炮机模拟器 {self.device_id} - IP: {self.ip}")

        try:
            # 启动TCP服务器 (阻塞调用)
            StartTcpServer(context=context, identity=identity, address=(self.ip, 502))
        except Exception as e:
            logger.error(f"雾炮机模拟器 {self.device_id} 启动失败: {e}")
            self.running = False
    
    def stop(self):
        """停止模拟器"""
        self.running = False
        logger.info(f"停止雾炮机模拟器 {self.device_id} - IP: {self.ip}")

def main():
    """主函数 - 启动8个雾炮机模拟器"""
    print("==============================================")
    print("      雾炮机 Modbus 从机模拟器")
    print("==============================================")
    print("模拟8个雾炮机设备:")
    print("IP地址范围: ************* - *************")
    print("端口: 502")
    print("从机ID: 1")
    print("==============================================")

    simulators = []
    threads = []

    try:
        # 创建并启动8个模拟器
        for i in range(1, 9):
            simulator = MistCannonSimulator(i)
            simulators.append(simulator)

            # 在单独的线程中启动每个模拟器
            thread = threading.Thread(target=simulator.start, name=f"MistCannon-{i}")
            thread.daemon = True
            thread.start()
            threads.append(thread)

            print(f"✓ 雾炮机模拟器 {i} (192.168.2.{100+i}) 启动中...")
            time.sleep(1)  # 给每个服务器足够的启动时间

        print("\n✓ 所有雾炮机模拟器已启动")
        print("模拟器功能:")
        print("- 支持喷雾控制 (启动/停止)")
        print("- 支持喷射模式切换 (摇摆/定点)")
        print("- 支持角度设置和位置反馈")
        print("- 支持故障模拟和复位")
        print("- 支持零位设置")
        print("\n按 Ctrl+C 停止所有模拟器...")

        # 保持主线程运行
        while True:
            time.sleep(1)
            # 检查线程状态
            active_threads = [t for t in threads if t.is_alive()]
            if len(active_threads) < len(threads):
                logger.warning(f"检测到 {len(threads) - len(active_threads)} 个模拟器线程已停止")

    except KeyboardInterrupt:
        print("\n正在停止所有模拟器...")
        for simulator in simulators:
            simulator.stop()

        # 等待所有线程结束
        for thread in threads:
            if thread.is_alive():
                thread.join(timeout=2)

        print("✓ 所有模拟器已停止")
    except Exception as e:
        logger.error(f"模拟器运行错误: {e}")
        for simulator in simulators:
            simulator.stop()

if __name__ == "__main__":
    main()
