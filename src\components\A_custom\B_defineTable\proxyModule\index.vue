<template>
    <!-- 代理接收组件参数 -->
    <div>
        <components v-if="item.tempalte.name" class="proxyModule" v-bind="item.tempalte.attr" v-on="item.tempalte.event" :is="item.tempalte.name" :style="styleConfig[item.tempalte.name]||{}" >
            <div v-if="item.tempalte.name == 'el-image'" slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
            </div>
        </components>
        <div v-else  v-html="row[item.property]"></div>
    </div>
</template>
    
<script>
    export default {
        inheritAttrs:false,
        props:{
            // 行数据
            row:{
                type:Object,
                default:()=>{
                    return {}
                }
            },
            // 竖配置
            item:{
                type:Object,
                default:()=>{
                    return {
                        /*
                            slotName:"是否插槽展示"
                            name:"el-element组件名",
                            attr:"el-element原参数",
                            event:"el-element原事件",
                            keys:"需要拿的key处理",
                        */ 
                    }
                }
            },
        },
        created(){
            this.handleProcess();
        },
        data(){
            return{
                styleConfig:{
                    "el-image":{
                        width:"50px",
                        height:"50px",
                    },
                },
            }
        },
        methods:{
            handleProcess(){
                if(this.item.tempalte.name == "el-image"){
                    if(Array.isArray(this.row[this.item.property]) && this.row[this.item.property].length > 0 ){
                        let urls = this.row[this.item.property].map(x=>{return x[this.item.tempalte.keys]})
                        this.item.tempalte.attr = {...this.item.tempalte.attr,src:this.row[this.item.property][0][this.item.tempalte.keys||"url"],previewSrcList:urls}
                    }else if(!Array.isArray(this.row[this.item.property])&& this.row[this.item.property]){
                        let urls = [this.row[this.item.property]]
                        this.item.tempalte.attr = {...this.item.tempalte.attr,src:this.row[this.item.property],previewSrcList:urls}
                    }else{
                        
                    }
                }
                if(this.item.tempalte.name == "el-button"){
                    
                }
            }
        },
    }
</script>
<style>
.proxyModule .image-slot .el-icon-picture-outline{
    font-size: 50px;
    line-height: 60px;
}
</style>