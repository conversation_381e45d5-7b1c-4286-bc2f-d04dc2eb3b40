/**
 * px 转 rem 工具函数
 * 基础设计尺寸为750px
 * 1rem = 100px
 */

import rem from './rem';

/**
 * 将 px 转换为 rem
 * @param {number} px - 像素值
 * @returns {string} - 转换后的 rem 值
 */
export function pxToRem(px) {
  return (px / rem.BASE_FONT_SIZE) + 'rem';
}

/**
 * 将对象中的 px 值转换为 rem
 * @param {Object} styles - 样式对象
 * @returns {Object} - 转换后的样式对象
 */
export function convertStyles(styles) {
  const result = {};
  
  for (const key in styles) {
    const value = styles[key];
    
    // 如果值是数字或者以 px 结尾的字符串，则转换为 rem
    if (typeof value === 'number') {
      result[key] = pxToRem(value);
    } else if (typeof value === 'string' && value.endsWith('px')) {
      const pxValue = parseFloat(value);
      if (!isNaN(pxValue)) {
        result[key] = pxToRem(pxValue);
      } else {
        result[key] = value;
      }
    } else {
      result[key] = value;
    }
  }
  
  return result;
}

export default {
  pxToRem,
  convertStyles
};
