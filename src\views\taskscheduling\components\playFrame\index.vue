<template>
    <el-dialog :title="title" v-bind="$attrs" :width="width" :visible.sync="show" :before-close="handleClose" :close-on-click-modal="false">
        <slot></slot>
        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="saveEvent">确 定</el-button>
            <el-button @click="cancelEvent">取 消</el-button>
        </span>
    </el-dialog>
</template>

<script>
    export default {
        props:{
            title:{
                type:String,
            },
            show:{
                type:Boolean,
                default:()=>{return false}
            },
            width:{
                type:String,
                default:()=>{return "900px"}
            },
        },
        data(){
            return{

            }
        },
        methods:{
            handleClose(){
                this.cancelEvent();
            },
            cancelEvent(){
                 this.$emit("update:show",false);
                 this.$emit("change",false);
            },
            saveEvent(e){
                this.$emit("change",true);
                this.$emit("update:show",false);
            }
        },
    }
</script>

<style lang="scss" scoped>

</style>