<template>
<!-- 任务管理 -->
    <div class="taskscheduling_task">
        <div class="taskscheduling_task_head" >任务管理</div>
        <div class="taskscheduling_task_search" >
            <selectInput  v-model="queryParams.jobGroup" lable="执行器" >
                <el-option v-for="item in actuatorOptions" :key="item.id" :label="item.title" :value="item.id" />
            </selectInput>
            <selectInput  style="margin-left:10px" v-model="queryParams.triggerStatus"  >
                <el-option v-for="item in statusOptions" :key="item.value" :label="item.name" :value="item.value" />
            </selectInput>
            <el-input style="width:200px;margin-left:10px;" clearable v-model="queryParams.jobDesc" placeholder="请输入任务描述"></el-input>
            <el-input style="width:200px;margin-left:10px;" clearable v-model="queryParams.executorHandler" placeholder="请输入jobHandler"></el-input>
            <el-input style="width:200px;margin-left:10px;" clearable v-model="queryParams.author" placeholder="请输入负责人"></el-input>
        </div>
        
        <div class="">
            <div class="taskscheduling_task_tableHead" >
                <div>
                    <el-button size="mini" @click="openEvent">新增</el-button>
                    <el-button size="mini" type="primary" @click="getList" >搜索</el-button>
                    <el-button size="mini" type="success" @click="resetting">重置</el-button>
                </div>
                <div>
                    <el-pagination v-if="recordsTotal>0"  @current-change="getList" :current-page.sync="queryParams.pageNum"
                        :page-size.sync="queryParams.pageSize" layout="total, prev, pager, next,sizes" :page-sizes="[10, 25, 50, 100]" :total="recordsTotal"></el-pagination>
                </div>
            </div>
            <el-table :data="tableData" border v-loading="loading" >
                <el-table-column prop="id" label="任务ID" width="100"></el-table-column>
                <el-table-column prop="jobDesc" label="任务描述" min-width="300" ></el-table-column>
                <el-table-column prop="scheduleConf" label="调度类型" width="250" ></el-table-column>
                <el-table-column prop="executorHandler" label="运行模式" width="300" >
                    <template slot-scope="{row}" > 
                        <span>{{row.glueType+ ": " + row.executorHandler? row.executorHandler:""}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="author" label="负责人" width="150" ></el-table-column>
                <el-table-column prop="triggerStatus" label="状态" width="150" >
                    <template slot-scope="{row}" >
                        <el-tag :type="triggerStatusTag[row.triggerStatus].tag">{{triggerStatusTag[row.triggerStatus].name}}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="150" >
                    <template slot-scope="{row}" > 
                        <el-dropdown  @command="dropdownEvent($event,row)" trigger="click" size="mini" split-button >
                                操作
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item :divided="item.divided" :disabled="item.disabled(row)" v-for="item,index in dropdownItem" :key="index" :command="item.id" >{{item.name}}</el-dropdown-item>
                                <el-dropdown-item :command="8" >{{({1:"停用",0:"启用"})[row.triggerStatus]}}</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </template> 
                </el-table-column>
            </el-table>
        </div>
        
        <playFrameTemplate title="执行一次" :show.sync="playFrameTemplateShow" @change="playFrameTemplateChange" >
            <el-form ref="form" :model="form" label-width="100px">
                <el-form-item label="任务参数">
                    <el-input type="textarea" v-model="form.executorParam" placeholder="请输入任务参数"></el-input>
                </el-form-item>
                <el-form-item label="机器地址">
                    <el-input type="textarea" v-model="form.addressList" placeholder="请输入本次执行的机器地址,为空则从执行器获取"></el-input>
                </el-form-item>
            </el-form>
        </playFrameTemplate>

        <el-dialog title="更新任务" :visible.sync="editShow" width="900px" append-to-body :close-on-click-modal="false">
             <el-form ref="form" :rules="editRules" :model="formEdit" label-width="120px">
                <div class="editTitle" >基础配置</div>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="执行器" prop="jobGroup">
                            <selectInput  v-model="formEdit.jobGroup" >
                                <el-option v-for="item in actuatorOptions" :key="item.id" :label="item.title" :value="item.id" />
                            </selectInput>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="任务描述" prop="jobDesc">
                            <baseInput v-model="formEdit.jobDesc" placeholder="请输入任务描述" ></baseInput>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="负责人" prop="author">
                            <baseInput v-model="formEdit.author" placeholder="请输入负责人" ></baseInput>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="报警邮件" prop="alarmEmail">
                            <baseInput v-model="formEdit.alarmEmail" placeholder="请输入报警邮件,多个邮件地址则逗号分隔" ></baseInput>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div class="editTitle" >调度配置</div>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="调度类型" prop="scheduleType">
                            <selectInput  v-model="formEdit.scheduleType">
                                <el-option v-for="item in scheduleTypeOptions" :key="item.key" :label="item.value" :value="item.key" />
                            </selectInput>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="Cron" prop="scheduleConf" v-if="formEdit.scheduleType=='CRON'" >
                            <baseInput v-model="formEdit.scheduleConf" placeholder="请输入表达式" >
                                <el-button slot="append" icon="el-icon-search" @click="cronEvent" ></el-button>
                            </baseInput>
                        </el-form-item>
                        <el-form-item label="固定速度" prop="scheduleConf" v-if="formEdit.scheduleType=='FIX_RATE'" >
                            <baseInput type="number" v-model="formEdit.scheduleConf" placeholder="请输入( Second )" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <div class="editTitle" >任务配置</div>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="运行模式" prop="glueType">
                            <selectInput :disabled="!!tableObj.id"  v-model="formEdit.glueType" @change="glueTypeChange" >
                                <el-option v-for="item in glueTypeOptions" :key="item.key" :label="item.value" :value="item.key" />
                            </selectInput>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="'BEAN'== formEdit.glueType">
                        <el-form-item label="JobHandler" prop="executorHandler">
                             <baseInput v-model="formEdit.executorHandler" placeholder="请输入JobHandler" ></baseInput>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24"> 
                        <el-form-item label="任务参数" prop="executorParam">
                            <baseInput width="685px" v-model="formEdit.executorParam" placeholder="请输入任务参数" ></baseInput>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div class="editTitle" >高级配置</div>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="路由策略" prop="executorRouteStrategy">
                            <selectInput  v-model="formEdit.executorRouteStrategy" >
                                <el-option v-for="item in executorRouteStrategyOptions" :key="item.key" :label="item.value" :value="item.key" />
                            </selectInput>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="子任务ID" prop="childJobId">
                            <baseInput v-model="formEdit.childJobId" placeholder="请输入子任务ID" ></baseInput>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="调度过期策略" prop="misfireStrategy">
                            <selectInput  v-model="formEdit.misfireStrategy" >
                                <el-option v-for="item in misfireStrategyOptions" :key="item.key" :label="item.value" :value="item.key" />
                            </selectInput>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="阻塞处理策略" prop="executorBlockStrategy">
                            <selectInput  v-model="formEdit.executorBlockStrategy" >
                                <el-option v-for="item in executorBlockStrategyOptions" :key="item.key" :label="item.value" :value="item.key" />
                            </selectInput>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="任务超时时间" prop="executorTimeout">
                            <baseInput v-model="formEdit.executorTimeout" placeholder="请输入子任务超时时间" ></baseInput>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="失败重试次数" prop="executorFailRetryCount">
                            <baseInput v-model="formEdit.executorFailRetryCount" placeholder="请输入子失败重试次数" ></baseInput>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="editSaveEvent">确 定</el-button>
                <el-button @click="editCancelEvent">取 消</el-button>
            </span>
        </el-dialog>
        <cronPlayFrame :show.sync="cronPlayFrameShow" v-model="formEdit.scheduleConf" ></cronPlayFrame>
    </div>
</template>

<script>
import {jobinfoJsonTaskApi,jobinfoTableTaskApi,jobinfoTriggerTaskApi,jobinfoRegisterNodesTaskApi,jobinfoNextExecutionTimeTaskApi,jobinfoNextStartTaskApi,jobinfoNextStopTaskApi,jobinfoAddTaskApi,jobinfoUpdateTaskApi,jobinfoRemoveApi} from "../api/task.js"
    import selectInput from "../components/lableInput/selectInput.vue"
    import baseInput from "../components/lableInput/input.vue"
    import playFrameTemplate from "../components/playFrame/index.vue"
    import cronPlayFrame from "../components/cron/index.vue"
    export default {
        name : "Task",
        components:{
            selectInput,playFrameTemplate,baseInput,cronPlayFrame
        },
        data() {
            return {
                loading:false,
                cronPlayFrameShow:false,
                scheduleTypeOptions:[],
                misfireStrategyOptions:[],
                glueTypeOptions:[],
                executorBlockStrategyOptions:[],
                executorRouteStrategyOptions:[],
                judge:null,
                playFrameTemplateShow:false,
                editShow:false,
                form:{},
                formEdit:{

                },
                queryParams:{
                    jobGroup:1,
                    actuator:null,
                    triggerStatus:-1,
                    jobDesc:"",
                    executorHandler:"",
                    author:"",
                    pageSize:10,
                    pageNum:1
                },
                recordsTotal:0,
                tableData:[],
                currentPage1:10,
                actuatorOptions:[],
                statusOptions:[{value:-1,name:"全部",},{value:1,name:"启动"},{value:0,name:"停用"}],
                triggerStatusTag:{
                    0:{tag:"info",name:"STOP"},
                    1:{tag:"success",name:"RUNNING"},
                },
                dropdownItem:[
                    {name:"执行一次",id:1,disabled:()=>{return false}},
                    {name:"查询日志",id:2,disabled:()=>{return false}},
                    {name:"注册节点",id:3,disabled:(row)=>{ return false }},
                    {name:"下次执行时间",id:4,disabled:()=>{return false}},
                    {name:"GLUE IDE",id:9,disabled:(row)=>{return row.glueType =='BEAN'}},
                    {name:"编辑",id:5,divided:true,disabled:()=>{return false}},
                    {name:"删除",id:6,disabled:()=>{return false}},
                    {name:"复制",id:7,disabled:()=>{return false}},
                ],
                tableObj:{},
                editRules:{
                    jobGroup:[
                        { required: true, message: '执行器不能为空!', trigger: 'blur' },
                    ],
                    jobDesc:[
                        { required: true, message: '任务描述不能为空!', trigger: 'blur' },
                    ],
                    author:[
                        { required: true, message: '负责人不能为空!', trigger: 'blur' },
                    ],
                    scheduleType:[
                        { required: true, message: '调度类型不能为空!', trigger: 'blur' },
                    ],
                    scheduleConf:[
                        { required: true, message: '不能为空!', trigger: 'blur' },
                    ],
                    glueType:[
                        { required: true, message: '运行模式不能为空!', trigger: 'blur' },
                    ],
                    executorHandler:[
                        { required: true, message: 'JobHandler不能为空!', trigger: 'blur' },
                    ],
                },
                cronStr:"",
            }
        },
        created() {
            this.oneEvent();
        },
        methods: {
            glueTypeChange(e){
                if(e.indexOf("GLUE")!=-1){
                    this.formEdit.glueRemark = "GLUE代码初始化";
                }else{
                    this.formEdit.glueRemark = "";
                }
                console.log(this.formEdit.glueRemark)
            },
            // 执行一次数据,获取基础信息
            oneEvent(){
                jobinfoJsonTaskApi().then(res=>{
                    let {JobGroupList,ExecutorBlockStrategyEnum,ExecutorRouteStrategyEnum,GlueTypeEnum,MisfireStrategyEnum,ScheduleTypeEnum} = res.data;
                    this.actuatorOptions = JobGroupList;
                    this.executorBlockStrategyOptions = ExecutorBlockStrategyEnum; //阻塞处理策略
                    this.executorRouteStrategyOptions = ExecutorRouteStrategyEnum;//路由策略
                    this.glueTypeOptions = GlueTypeEnum;//运行模式
                    this.misfireStrategyOptions = MisfireStrategyEnum;//调度过期策略
                    this.scheduleTypeOptions = ScheduleTypeEnum;//调度类型
                    this.getList();
                })
                
            },
            getList(){
                // 获取列表信息
                this.loading = true;
                jobinfoTableTaskApi(this.queryParams).then(res=>{
                    let {recordsTotal,data} = res;
                    this.tableData = data;
                    this.recordsTotal = recordsTotal;
                    this.loading = false;
                })
            },
            openEvent(){
                this.editShow = true;
                this.tableObj = {};
                this.formEdit = {
                    jobGroup:1,
                    scheduleType:"CRON",
                    glueType:"BEAN",
                    executorRouteStrategy:"FIRST",
                    misfireStrategy:"DO_NOTHING",
                    executorBlockStrategy:"SERIAL_EXECUTION",
                    executorTimeout:0,
                    executorFailRetryCount:0,
                    glueRemark:"GLUE代码初始化",
                };
            },
            // 新增编辑保存事件
            editSaveEvent(){
                this.$refs.form.validate((valid) => {
                    if(valid){
                        [this.formEdit.cronGen_display,this.formEdit.schedule_conf_CRON ] = [this.formEdit.scheduleConf,this.formEdit.scheduleConf]
                        if(!!this.formEdit.id){
                            jobinfoUpdateTaskApi(this.formEdit).then(res=>{
                                this.$message({message:`编辑成功!`,type: 'success'});
                                this.editShow = false;
                                this.getList();
                            })
                        }else{
                            jobinfoAddTaskApi(this.formEdit).then(res=>{
                                this.$message({message:`新增成功!`,type: 'success'});
                                this.editShow = false;
                                this.getList();
                            })
                        }
                    }
                })
            },
            cronEvent(){
                this.cronPlayFrameShow = true;
            },
            //  新增编辑取消事件
            editCancelEvent(){
                this.editShow = false;
            },
            // 重置
            resetting(){
                this.queryParams = {
                    jobGroup:1,
                    actuator:null,
                    triggerStatus:-1,
                    jobDesc:"",
                    executorHandler:"",
                    author:"",
                    pageSize:10,
                    pageNum:0,
                }
                this.getList();
            },
            // 操作下拉选择事件
            dropdownEvent(id,obj={}){
                this.tableObj = JSON.parse(JSON.stringify(obj));
                switch (id) {
                    case 1: (this.playFrameTemplateShow = true) ;break;
                    case 2: (this.$router.push({path:"/taskscheduling/dispatchingLog",query:{jobGroup:this.tableObj.jobGroup,jobDesc:this.tableObj.id}})) ;break;
                    case 3: this.registerNodesEvent();break;
                    case 4: this.nextExecutionTimeEvent();break;
                    case 5: (this.editShow = true,this.formEdit = this.tableObj);break;
                    case 6: this.removeEvent(this.tableObj);break;
                    case 7: (this.formEdit = {...this.tableObj,id:null,addTime:null,glueUpdatetime:null,updateTime:null},this.editShow = true);break;
                    case 8: this.stateChange(this.tableObj);break;
                    case 9: this.GLUEIDEEvent(obj);break;
                }
            },
            GLUEIDEEvent(row){
                var codeUrl = process.env.VUE_APP_BASE_API +'/xxl-job/jobcode?jobId='+ row.id;
                window.open(codeUrl)
            },
            removeEvent({id}){
                this.$confirm(`确定删除?`, '系统提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(() => {
                    jobinfoRemoveApi({id}).then(res=>{
                        this.$message({message:`删除成功!`,type: 'success'});
                        this.getList();
                    })
                })
            },
            // 启动停用事件
            stateChange(obj){
                let {triggerStatus,id} = obj;
                let valueObj = {
                    0:{name:"启用",fn:jobinfoNextStartTaskApi},
                    1:{name:"停用",fn:jobinfoNextStopTaskApi}
                }
                this.$confirm(`确定${valueObj[triggerStatus].name}?`, '系统提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                    }).then(() => {
                        valueObj[triggerStatus].fn({id}).then(res=>{
                            this.$message({message:`${valueObj[triggerStatus].name}成功!`,type: 'success'});
                            this.getList();
                        })
                    }).catch(() => {
         
                });
            },
            // 下次执行时间事件
            nextExecutionTimeEvent(){
                let {scheduleType,scheduleConf} = this.tableObj;
                jobinfoNextExecutionTimeTaskApi({scheduleType,scheduleConf}).then(res=>{
                    let list = res.content;
                    let str = "";
                    if(list&&list.length > 0){
                        list.forEach((x,i)=>{
                            str+=`<p>${i+1 +'.'+ x}</p>`
                        })
                    }else{
                        str="暂无数据"
                    }
                    this.$alert(str, '下次执行时间', {dangerouslyUseHTMLString: true});
                })
            },
            // 注册节点事件
            registerNodesEvent(){
                let {jobGroup} = this.tableObj;
                jobinfoRegisterNodesTaskApi({id:jobGroup}).then(res=>{
                    let {registryList}= res.content;
                    let str = "";
                    if(registryList&&registryList.length > 0){
                        registryList.forEach((x,i)=>{
                            str+=`<p>${i+1 +'.'+ x}</p>`
                        })
                    }else{
                        str="暂无数据"
                    }
                    this.$alert(str, '注册节点', {dangerouslyUseHTMLString: true});
                })
            },
            // 执行一次事件
            playFrameTemplateChange(judge){
                if(judge){
                    let {id} = this.tableObj;
                    jobinfoTriggerTaskApi({...this.form,id}).then(res=>{
                        this.$message({message: '保存成功',type: 'success'});
                        this.getList();
                    }).catch((err)=>{
                        this.$message.error("保存失败!");
                    })
                }else{
                    this.tableObj = {};
                }
            },
        },
    }
</script>

<style lang="scss">

    ::-webkit-scrollbar {
    width: 12px;
    height: 12px;
    }
    ::-webkit-scrollbar-track {
    background: rgb(239, 239, 239);
    }
    ::-webkit-scrollbar-thumb {
    background: #bfbfbf;
    }
    .marginLeft10{
        margin-left:10px;
    }
    .marginBottom10{
        margin-bottom:10px;
    }
    .taskscheduling_task{
        padding:10px 15px;
        box-sizing:border-box;
        background-color: rgb(255 255 255 / 95%);
        width: 98%;
        min-height: 80vh;
        margin-left: 1%;
        margin-top: 10px;
        border-radius: 10px;
        box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);
        &_head{
            padding-bottom:10px;
            font-size:25px;
            font-weight:bold;
        }
        &_search{
            display:flex;
            flex-wrap: wrap;
            .el-input__inner{
                border-radius:0px !important;
                min-width:200px;
            }
            .el-input{
                margin-bottom:15px;
            }
        }
        &_tableHead{
            display:flex;
            justify-content: space-between;
            padding:2px 0px 2px 5px;
            align-items:center;
            border-top: 1px solid #dfe6ec;
            border-left: 1px solid #dfe6ec;
            border-right: 1px solid #dfe6ec;
        }
    }
    .editTitle{
        padding-bottom:3px;
        margin-bottom:10px;
        color:#858585;
        border-bottom:1px solid #e5e5e5;
    }
    .lableInput_selectInput_lable{
        margin-left: 0px;
    }
</style>