<template>
  <div>
    <el-dialog
      title="营业额"
      :close-on-click-modal="false"
      :visible.sync="rateTurnoverJudge"
      width="1500px"
      :before-close="handleClose"
    >
      <div class="row-between">
        <el-radio-group @change="getlist()" v-model="queryParams.onCredit">
          <el-radio-button
            v-for="item,index in dataList"
            :label="item.value"
            :key="index"
          >{{item.name}}</el-radio-button>
        </el-radio-group>
        <el-radio-group @change="getlist()" v-model="queryParams.dateType">
          <el-radio-button
            v-for="city,index in dateList"
            :label="city.value"
            :key="index"
          >{{city.name}}</el-radio-button>
        </el-radio-group>
      </div>
      <el-row>
        <el-col :span="24" class="mt10">
          <el-descriptions
            :labelStyle="{color:'#18212b',fontWeight:'550',textAlign:'center',fontSize:'18px'}"
            :contentStyle="{fontSize:'16px',fontWeight:'550',textAlign:'center',width:'200px',color:'#74304d'}"
            :column="5"
            border
          >
            <el-descriptions-item>
              <template slot="label">总额度</template>
              {{turnoverObj.allSum}}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">已收款</template>
              {{turnoverObj.proceedsSum}}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">挂账</template>
              {{turnoverObj.unProceedsSum}}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">已收挂账</template>
              {{turnoverObj.paidOnCredit}}
            </el-descriptions-item>
            <el-descriptions-item style="width:250px">
              <template slot="label">日期范围</template>
              {{turnoverObj.dateRange}}
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
      <el-row>
        <!-- 总收益 -->
        <el-col :span="24">
          <el-table border :data="profitTableList" class="mt10">
            <el-table-column label="订单编号" align="center" width="200" prop="orderNumber" />
            <el-table-column label="房间" align="center" prop="roomName" />
            <el-table-column label="负责人" align="center" prop="followerNickName" />
            <el-table-column label="会员名称" align="center" prop="memberName">
              <template slot-scope="{row}">
                <span>{{row.memberName}}</span>
              </template>
            </el-table-column>
            <el-table-column label="会员手机" align="center" prop="mobile" />
            <el-table-column label="总金额" align="center" prop="totalPrice">
              <template slot-scope="{row}">
                <div>
                  <span>¥</span>
                  <strong>{{" "+row.totalPrice}}</strong>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="实付金额" align="center" prop="paymentMoney">
              <template slot-scope="{row}">
                <div>
                  <span>¥</span>
                  <strong>{{" "+row.paymentMoney}}</strong>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="支付方式" align="center" prop="paymentMethod">
              <template slot-scope="scope">
                <dict-tag
                  :options="dict.type.ca_order_payment_method"
                  :value="scope.row.paymentMethod"
                />
              </template>
            </el-table-column>
            <el-table-column label="付款金额" align="center" prop="paymentPrice">
              <template slot-scope="{row}">
                <div>
                  <span>¥</span>
                  <strong>{{" "+row.paymentPrice}}</strong>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="结账时间" align="center" width="200" prop="settleTime" />

            <!-- <el-table-column label="实收金额" align="center" prop="proceedsSum">
              <template slot-scope="{row}">
                <div class="money">
                  <span>¥</span>
                  <strong>{{" "+row.proceedsSum}}</strong>
                </div>
              </template>
            </el-table-column>-->
            <!-- <el-table-column label="未收金额" align="center" prop="unProceedsSum">
              <template slot-scope="{row}">
                <div class="money">
                  <span>¥</span>
                  <strong>{{" "+row.unProceedsSum}}</strong>
                </div>
              </template>
            </el-table-column>-->
            <!-- <el-table-column label="优惠金额" align="center" prop="discount">
              <template slot-scope="{row}">
                <span>{{(row.totalPrice - row.paymentPrice).toFixed(2)}}</span>
              </template>
            </el-table-column>-->
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getlist"
          />
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>-->
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import { listOrder, turnoverList } from "@/api/catering/order/order";
// import { getRevenue } from "@/api/index/index";
export default {
  dicts: ["ca_order_payment_method"],
  props: {
    rateTurnoverJudge: Boolean,
    termIndex: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      turnoverObj: {
        dateRange: ""
      },
      dataList: [
        { value: null, name: "全部" },
        { value: 0, name: "已收" },
        { value: 1, name: "未收" }
      ],
      dateList: [
        { value: 1, name: "今日" },
        { value: 2, name: "本周" },
        { value: 3, name: "本月" },
        { value: 4, name: "本季度" },
        { value: 5, name: "本年" }
      ],
      // 总收益 值
      total: 0,
      profitTableList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        onCredit: null, //是否赊账 0 :否 , 1:是
        status: 2,
        dateType: 1 //日期
      }
    };
  },
  watch: {
    rateTurnoverJudge() {
      if (this.rateTurnoverJudge) {
        this.queryParams.dateType = this.termIndex;
        this.getlist();
      }
    }
  },
  methods: {
    getlist() {
      // listOrder(this.queryParams).then(res => {
      //   this.profitTableList = res.rows;
      //   this.total = res.total;
      // })
      // turnoverList(this.queryParams).then(res => {
      //   this.profitTableList = res.rows;
      //   this.total = res.total;
      // });
      // getRevenue({ type: this.queryParams.dateType }).then(res => {
      //   let {
      //     allSum,
      //     proceedsSum,
      //     unProceedsSum,
      //     paidOnCredit
      //   } = res.data.dashboardTurnoverVO;
      //   console.log(allSum, proceedsSum, unProceedsSum);
      //   // dashboardTurnoverVO 营业额 dashboardOperatingCostsVOList 运营成本 netProfit  净利润
      //   this.turnoverObj = {
      //     ...this.turnoverObj,
      //     allSum: allSum.toFixed(2),
      //     proceedsSum: proceedsSum.toFixed(2),
      //     unProceedsSum: unProceedsSum.toFixed(2),
      //     paidOnCredit: paidOnCredit.toFixed(2)
      //   };
      //   // 自然日计算
      //   this.naturalDay(this.queryParams.dateType);
      // });
    },
    naturalDay(type) {
      switch (type) {
        case 1:
          this.turnoverObj.dateRange = this.dateofAcquisition();
          break;
        case 2:
          this.turnoverObj.dateRange =
            this.weeklyInterval() + " ~ " + this.dateofAcquisition();
          break;
        case 3:
          this.turnoverObj.dateRange =
            this.monthlyInterval() + " ~ " + this.dateofAcquisition();
          break;
        case 4:
          this.turnoverObj.dateRange =
            this.quarterlyInterval() + " ~ " + this.dateofAcquisition();
          break;
        case 5:
          this.turnoverObj.dateRange =
            this.IntervalOfThisyear() + " ~ " + this.dateofAcquisition();
          break;
      }
    },
    // 本年区间计算
    IntervalOfThisyear() {
      let date = new Date();
      date.setDate(1);
      date.setMonth(0);
      return this.dateofAcquisition(date);
    },
    // 季度区间计算
    quarterlyInterval() {
      let date = new Date();
      let month = date.getMonth();
      switch (month) {
        case month < 3:
          date.setMonth(0);
          break;
        case 2 < month && month < 6:
          date.setMonth(3);
          break;
        case 5 < month && month < 9:
          date.setMonth(6);
          break;
        case 8 < month && month < 11:
          date.setMonth(9);
          break;
        default:
          date.setDate(1);
      }
      return this.dateofAcquisition(date);
    },
    // 月区间计算
    monthlyInterval() {
      let date = new Date();
      date.setDate(1);
      return this.dateofAcquisition(date);
    },
    // 周的区间计算
    weeklyInterval() {
      let date = new Date();
      let weekday = date.getDay() || 7; //获取星期几,getDay()返回值是 0（周日） 到 6（周六） 之间的一个整数。0||7为7，即weekday的值为1-7
      date.setDate(date.getDate() - weekday + 1); //往前算（weekday-1）天，年份、月份会自动变化
      return this.dateofAcquisition(date);
    },
    // 获取年月日
    dateofAcquisition(date = new Date()) {
      let y = date.getFullYear(); //年
      let m = date.getMonth() + 1; //月
      let d = date.getDate(); //日
      return y + "-" + m + "-" + d;
    },
    handleClose() {
      this.$emit("update:rateTurnoverJudge", false);
    }
  }
};
</script>

<style lang="scss" scoped>
</style>