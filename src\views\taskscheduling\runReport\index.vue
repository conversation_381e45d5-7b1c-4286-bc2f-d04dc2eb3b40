<template>
<!-- 运行报表 -->
    <div class="runReport" >
    <!-- 顶部块 -->
        <div class="headBox">
            <div class="headBox_item" :style="item.style" v-for="item,index in headList" :key="index" >
                <div class="headBox_item_top" >
                    <div>{{item.title}}</div>
                    <div>{{item.num}}</div>
                </div>
                <div class="headBox_item_bottom" >{{item.notes}}</div>
            </div>
        </div>
        <!-- 数据表图 -->
        <div class="runReport_dispatch" >
            <div class="runReport_dispatch_head" >
                <div>调度报表</div>
                <el-date-picker :default-time="['00:00:00', '23:59:59']" @change="pickerEvent" :picker-options="pickerOptions" v-model="time" type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </div>
            <el-row :gutter="10">
                <el-col :span="16">
                    <div ref="foldLine" style="height:400px;"></div>
                </el-col>
                <el-col :span="8">
                    <div ref="pieChart" style="height:400px;"></div>
                </el-col>
            </el-row>
        </div>
    </div>
</template>

<script>
import {chwjobChartInfo,indexJson} from "../api/runReport.js"
    export default {
        data() {


            return {
                pickerOptions: {
                    shortcuts: [
                        {
                            text: '今天',
                            onClick: (picker) => {
                                picker.$emit('pick', [this.currentTime()+' 00:00:00',this.currentTime()+' 23:59:59']);
                            },
                        },
                        {
                            text: '昨天',
                            onClick:(picker)=> {
                                const date = new Date();
                                let newDate = this.currentTime(new Date(date.setTime(date.getTime() - 3600 * 1000 * 24)));
                                picker.$emit('pick', [newDate+" 00:00:00",newDate+' 23:59:59']);
                            }
                        },
                        {
                            text: '最近一周',
                            onClick:(picker)=> {
                                const start = new Date();
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                                picker.$emit('pick', [this.currentTime(new Date(start))+' 00:00:00', this.currentTime()+" 23:59:59"]);
                            }
                        }, 
                        {
                            text: '最近一个月',
                            onClick:(picker)=> {
                                const start = new Date();
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                                picker.$emit('pick', [this.currentTime(new Date(start))+' 00:00:00', this.currentTime()+" 23:59:59"]);
                            }
                        }, 
                        {
                            text: '最近三个月',
                            onClick:(picker)=> {
                                const start = new Date();
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                                picker.$emit('pick', [this.currentTime(new Date(start))+' 00:00:00', this.currentTime()+" 23:59:59"]);
                            }
                        }
                    ]
                },
                optionfFoldLine:{
                    tooltip : {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            label: {
                                backgroundColor: '#6a7985'
                            }
                        }
                    },
                    xAxis: {
                        type: 'category',
                    },
                    legend: {
                        data: ["成功","失败","进行中"],
                        left: 'center'
                    },
                     yAxis: {
                        type: 'value'
                    },
                    series: [],
                },
                time:[],
                headList:[
                    {
                        title:"任务数量",
                        num:0,
                        notes:"调度中心运行的任务数量",
                        style:{
                            background:"#00c0ef"
                        },
                    },
                    {
                        title:"调度次数",
                        num:0,
                        notes:"调度中心触发的调度次数",
                        style:{
                            background:"#f39c12"
                        },
                    },
                    {
                        title:"执行器数量",
                        num:0,
                        notes:"调度中心在线的执行器机器数量",
                        style:{
                            background:"#00a65a"
                        },
                    },
                ],
                chartPieChartOption:{
                    tooltip: {
                            trigger: 'item'
                        },
                        legend: {
                            orient: 'vertical',
                            left: 'left'
                        },
                        series: [
                            {
                            type: 'pie',
                            radius: '50%',
                            data: [],
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                },
            }
        },
        created() {
            this.oneEnvet();
        },
        methods: {
            oneEnvet(){
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                this.getList(this.currentTime(new Date(start))+' 00:00:00',this.currentTime()+" 23:59:59");
                indexJson().then(res=>{
                    let {jobLogCount,executorCount,jobInfoCount,jobLogSuccessCount} = res.data;
                    this.headList.forEach(x=>{
                        switch (x.title) {
                            case "任务数量" :(x.num = jobInfoCount);break;
                            case "调度次数" :(x.num = jobLogCount);break;
                            case "执行器数量" :(x.num = executorCount) ;break;
                        }
                    })
                })
            },
            currentTime(date = new Date()){
                let year = date.getFullYear(); 
                let month = date.getMonth() + 1;
                let day = date.getDate();
                month = (month > 9) ? month : ("0" + month);
                day = (day < 10) ? ("0" + day) : day;
                let today = year + "-" + month + "-" + day;
                return today
            },
            getList(startDate,endDate){
                chwjobChartInfo({startDate,endDate}).then(res=>{
                    let {triggerDayList,triggerDayCountFailList,triggerDayCountRunningList,triggerDayCountSucList,triggerCountFailTotal,triggerCountRunningTotal,triggerCountSucTotal} = res.content;
                    this.optionfFoldLine.xAxis.data = triggerDayList;//获取标题
                    this.optionfFoldLine.series[0]={name:'成功',data:triggerDayCountSucList,type:'line'};
                    this.optionfFoldLine.series[1]={name:'失败',data:triggerDayCountFailList,type:'line'};
                    this.optionfFoldLine.series[2]={name:'进行中',data:triggerDayCountRunningList,type:'line'}
                    this.chartPieChartOption.series[0].data[0]={name:"成功",value:triggerCountSucTotal};
                    this.chartPieChartOption.series[0].data[1]={name:"失败",value:triggerCountFailTotal};
                    this.chartPieChartOption.series[0].data[2]={name:"进行中",value:triggerCountRunningTotal};
                    this.chartFoldLineEvent();
                    this.chartPieChartEvent();
                });
            },
            pickerEvent(list){
                console.log(list)
                if(!list || list.length==0) return;
                let [start,end] = list
                this.getList(start,end);
            },
            timestampToTime(timestamp){
                var time = new Date(timestamp);
                var y = time.getFullYear();
                var m = time.getMonth()+1;
                var d = time.getDate();
                var h = time.getHours();
                var mm = time.getMinutes();
                var s = time.getSeconds();
                return y+'-'+m+'-'+d+' '+h+':'+mm+':'+s;
            },
            // 线图
            chartFoldLineEvent(){
                var myChart = this.echarts.init(this.$refs['foldLine']);
                myChart.setOption(this.optionfFoldLine);           
            },
            // 饼图
            chartPieChartEvent(){
                var myChart = this.echarts.init(this.$refs['pieChart']);
                myChart.setOption(this.chartPieChartOption);           
            },
        },
    }
</script>

<style lang="scss" scoped>
    .runReport{
        // padding:15px;
        box-sizing: border-box;
        background-color: #ffffff;
        margin-top: 10px;
        width: 98%;
        margin-left: 1%;
        border-radius: 10px;
        box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);
        .headBox{
            color:#ffffff;
            display:flex;
            justify-content: space-between;
            flex-wrap: wrap;
            padding: 10px;
            
            &_item{
                background:pink;
                width:calc(100% / 3.2);
                min-width:200px;
                margin-bottom:15px;
                .headBox_item_top{
                    padding: 5px;
                    line-height:25px;
                    border-bottom:3px solid #ffffff;
                }
                .headBox_item_bottom{
                    padding: 5px;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    overflow: hidden;
                }
            }
        }
    }
    .runReport_dispatch{
        background:#ecf0f5;
        height:450px;
        padding:5px 10px;
        .runReport_dispatch_head{
            display:flex;
            justify-content: space-between;
            padding: 5px 0 8px 0;
            font-size:25px;
            border-bottom:1px solid #ffffff; 
            margin-bottom:10px;
        }
    }
    .chartCss{
        display:flex;
        justify-content: space-around;
        align-items: center;
    }
</style>