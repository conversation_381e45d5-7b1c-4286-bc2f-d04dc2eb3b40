from pymodbus.client import ModbusTcpClient
from pymodbus.exceptions import ModbusException

class MistCannonController:
    def __init__(self, ip, port=502):
        self.client = ModbusTcpClient(host=ip, port=port)
        if not self.client.connect():
            raise ConnectionError(f"无法连接到Modbus服务器: {ip}:{port}")

    def __del__(self):
        self.client.close()

    # 读取雾炮状态
    def read_status(self):
        status = {}
        try:
            # 读取开关量状态
            coil_result = self.client.read_coils(4006, count=14)
            if not coil_result.isError():
                status['风机运行状态'] = coil_result.bits[0]
                status['水泵运行状态'] = coil_result.bits[1]
                status['上升状态'] = coil_result.bits[2]
                status['下降状态'] = coil_result.bits[3]
                status['左旋状态'] = coil_result.bits[4]
                status['右旋状态'] = coil_result.bits[5]
                status['手动状态'] = coil_result.bits[6]
                status['遥控状态'] = coil_result.bits[7]
                status['远程运行状态'] = coil_result.bits[8]
                status['故障'] = coil_result.bits[9]
                status['遥控运行状态'] = coil_result.bits[10]
                status['缺水故障'] = coil_result.bits[11]

            # 读取双整数状态
            register_result = self.client.read_holding_registers(4020, count=4)
            if not register_result.isError():
                status['当前俯仰位置'] = register_result.registers[0]
                status['当前旋转位置'] = register_result.registers[1]

        except ModbusException as e:
            print(f"读取状态错误: {e}")
            return None
        return status

    # 控制雾炮操作
    def control_mist_cannon(self, operation, value):
        try:
            if operation == '喷雾启停':
                self.client.write_coil(4000, value)
            elif operation == '喷射方式选择':
                self.client.write_coil(4002, value)
            elif operation == '定点旋转喷射角度':
                self.client.write_register(4038, value)
            elif operation == '定点俯仰喷射角度':
                self.client.write_register(4042, value)
            elif operation == '摇摆角度起始值':
                self.client.write_register(4030, value)
            elif operation == '摇摆角度终止值':
                self.client.write_register(4034, value)
            elif operation == '定零位':
                self.client.write_single_coil(4016, value)
            elif operation == '故障复位':
                self.client.write_single_coil(4004, value)
            elif operation == '左限位赋值':
                self.client.write_register(4046, value)
            elif operation == '右限位赋值':
                self.client.write_register(4052, value)
            elif operation == '一圈脉冲数':
                self.client.write_register(4056, value)
            else:
                raise ValueError(f"不支持的操作: {operation}")
            return True
        except ModbusException as e:
            print(f"控制操作错误: {e}")
            return False

# 示例使用
if __name__ == "__main__":
    ip = 'localhost'
    port = 502
    controller = MistCannonController(ip, port)

    # 读取雾炮状态
    status = controller.read_status()
    if status:
        print("雾炮当前状态:", status)

    # 控制雾炮进行喷雾启动
    if controller.control_mist_cannon('喷雾启停', 1):
        print("已启动喷雾")

    # 设置喷射方式为摇摆喷射
    if controller.control_mist_cannon('喷射方式选择', 1):
        print("已设置为摇摆喷射")
