// 列定义
export let fieldList = [
        {
            id: 1215,          // 列id，唯一  
            tableName: 'lx_device',       // 数据库表名，后端使用  
            tableAlias: 'lx_device',      // 数据库表别名，后端使用  
            columnName: 'id',      // 数据库字段列名，后端使用  
            columnAlias: 'id',     // 数据库字段别名，后端使用  
            javaType: 'Long',          // 后端字段类型，后端使用  
            dbType: 'bigint',          // 数据库字段类型，后端使用  
            property: 'id',         // 字段属性名  
            name: '主键',         // 字段名称（标签）  
            value: null,                             // 字段值，用于搜索、新增、修改时填入的值  
            compType:  1 ,       // 比较符类型，系统字典：sys_comp_type  
            boClass: 'LxDevice',                 // BO类，后端使用  
            voClass: 'LxDevice',                 // VO类，后端使用  
            doClass: 'LxDevice',                 // DO类，后端使用  
            operType: "SELECT",                      // 操作类型，SELECT：查询、SAVE：新增、UPDATE：修改  
            permission:  null ,      // 字段权限字符  
            sort: 1,                    // 排序顺序，从小到大排序  
            searchSort: 1,              // 排序顺序，从小到大排序  
            dict:  null ,          // 用户字典  
            sysDict:  null ,               // 系统字典  
            isDefault:  false ,                  // 是否默认查询条件  
            sortable:  false ,                      // 是否可排序  
            sortType:  'desc' ,                         // 排序类型，asc：升序、desc：降序  
            reflection:  null ,      // 引用（废弃）  
            insertable:  true ,                    // 字段是否可新增  
            updatable:  false ,                       // 字段是否可修改  
            selectable:  false ,                     // 字段是否可查询  
            listable:  false ,                        // 字段是否展示在列表上  
            selectType:   1 ,      // 控件类型，1：文本输入、2：数字输入、3：下拉选择、4：时间选择  
            tableWidth:  null ,      // 字段的列表宽度  
        },
        {
            id: 1216,         
            tableName: 'lx_device',      
            tableAlias: 'lx_device',     
            columnName: 'remark',     
            columnAlias: 'remark',    
            javaType: 'String',         
            dbType: 'varchar(500)',         
            property: 'remark',        
            name: '备注',        
            value: null,                            
            compType:  1 ,      
            boClass: 'LxDevice',                
            voClass: 'LxDevice',                
            doClass: 'LxDevice',                
            operType: "SELECT",                     
            permission:  null ,     
            sort: 10,                   
            searchSort: 2,             
            dict:  null ,         
            sysDict:  null ,              
            isDefault:  false ,                 
            sortable:  false ,                     
            sortType:  null ,                        
            reflection:  null ,     
            insertable:  true ,                   
            updatable:  true ,                      
            selectable:  false ,                    
            listable:  true ,                       
            selectType:   1 ,     
            tableWidth:  null ,     
        },
        {
            id: 1217,         
            tableName: 'lx_device',      
            tableAlias: 'lx_device',     
            columnName: 'name',     
            columnAlias: 'name',    
            javaType: 'String',         
            dbType: 'varchar(100)',         
            property: 'name',        
            name: '设备名称',        
            value: null,                            
            compType:  12 ,      
            boClass: 'LxDevice',                
            voClass: 'LxDevice',                
            doClass: 'LxDevice',                
            operType: "SELECT",                     
            permission:  null ,     
            sort: 3,                   
            searchSort: 3,             
            dict:  null ,         
            sysDict:  null ,              
            isDefault:  true ,                 
            sortable:  false ,                     
            sortType:  null ,                        
            reflection:  null ,     
            insertable:  true ,                   
            updatable:  true ,                      
            selectable:  true ,                    
            listable:  true ,                       
            selectType:   1 ,     
            tableWidth:  null ,     
        },
        {
            id: 1218,         
            tableName: 'lx_device',      
            tableAlias: 'lx_device',     
            columnName: 'status',     
            columnAlias: 'status',    
            javaType: 'String',         
            dbType: 'char(1)',         
            property: 'status',        
            name: '设备状态',        
            value: null,                            
            compType:  1 ,      
            boClass: 'LxDevice',                
            voClass: 'LxDevice',                
            doClass: 'LxDevice',                
            operType: "SELECT",                     
            permission:  null ,     
            sort: 4,                   
            searchSort: 4,             
            dict:  null ,         
            sysDict:  'lx_device_status' ,              
            isDefault:  true ,                 
            sortable:  false ,                     
            sortType:  null ,                        
            reflection:  null ,     
            insertable:  true ,                   
            updatable:  false ,                      
            selectable:  true ,                    
            listable:  true ,                       
            selectType:   2 ,     
            tableWidth:  null ,     
        },
        {
            id: 1219,         
            tableName: 'lx_device',      
            tableAlias: 'lx_device',     
            columnName: 'online_time',     
            columnAlias: 'online_time',    
            javaType: 'Date',         
            dbType: 'datetime',         
            property: 'onlineTime',        
            name: '最后在线时间',        
            value: null,                            
            compType:  1 ,      
            boClass: 'LxDevice',                
            voClass: 'LxDevice',                
            doClass: 'LxDevice',                
            operType: "SELECT",                     
            permission:  null ,     
            sort: 5,                   
            searchSort: 5,             
            dict:  null ,         
            sysDict:  null ,              
            isDefault:  false ,                 
            sortable:  false ,                     
            sortType:  null ,                        
            reflection:  null ,     
            insertable:  true ,                   
            updatable:  false ,                      
            selectable:  true ,                    
            listable:  true ,                       
            selectType:   4 ,     
            tableWidth:  null ,     
        },
        {
            id: 1220,         
            tableName: 'lx_device',      
            tableAlias: 'lx_device',     
            columnName: 'login_time',     
            columnAlias: 'login_time',    
            javaType: 'String',         
            dbType: 'varchar(100)',         
            property: 'loginTime',        
            name: '上次上线时间',        
            value: null,                            
            compType:  1 ,      
            boClass: 'LxDevice',                
            voClass: 'LxDevice',                
            doClass: 'LxDevice',                
            operType: "SELECT",                     
            permission:  null ,     
            sort: 6,                   
            searchSort: 6,             
            dict:  null ,         
            sysDict:  null ,              
            isDefault:  false ,                 
            sortable:  false ,                     
            sortType:  null ,                        
            reflection:  null ,     
            insertable:  true ,                   
            updatable:  false ,                      
            selectable:  true ,                    
            listable:  true ,                       
            selectType:   1 ,     
            tableWidth:  null ,     
        },
        {
            id: 1221,         
            tableName: 'lx_device',      
            tableAlias: 'lx_device',     
            columnName: 'type',     
            columnAlias: 'type',    
            javaType: 'String',         
            dbType: 'char(1)',         
            property: 'type',        
            name: '设备类型',        
            value: null,                            
            compType:  1 ,      
            boClass: 'LxDevice',                
            voClass: 'LxDevice',                
            doClass: 'LxDevice',                
            operType: "SELECT",                     
            permission:  null ,     
            sort: 7,                   
            searchSort: 7,             
            dict:  null ,         
            sysDict:  'lx_device_type' ,              
            isDefault:  true ,                 
            sortable:  false ,                     
            sortType:  null ,                        
            reflection:  null ,     
            insertable:  true ,                   
            updatable:  true ,                      
            selectable:  true ,                    
            listable:  true ,                       
            selectType:   2 ,     
            tableWidth:  null ,     
        },
        {
            id: 1222,         
            tableName: 'lx_device',      
            tableAlias: 'lx_device',     
            columnName: 'ip',     
            columnAlias: 'ip',    
            javaType: 'String',         
            dbType: 'varchar(100)',         
            property: 'ip',        
            name: '设备IP',        
            value: null,                            
            compType:  1 ,      
            boClass: 'LxDevice',                
            voClass: 'LxDevice',                
            doClass: 'LxDevice',                
            operType: "SELECT",                     
            permission:  null ,     
            sort: 8,                   
            searchSort: 8,             
            dict:  null ,         
            sysDict:  null ,              
            isDefault:  true ,                 
            sortable:  false ,                     
            sortType:  null ,                        
            reflection:  null ,     
            insertable:  true ,                   
            updatable:  true ,                      
            selectable:  true ,                    
            listable:  true ,                       
            selectType:   1 ,     
            tableWidth:  null ,     
        },
        {
            id: 1223,         
            tableName: 'lx_device',      
            tableAlias: 'lx_device',     
            columnName: 'create_by',     
            columnAlias: 'create_by',    
            javaType: 'String',         
            dbType: 'varchar(64)',         
            property: 'createBy',        
            name: '创建者',        
            value: null,                            
            compType:  1 ,      
            boClass: 'LxDevice',                
            voClass: 'LxDevice',                
            doClass: 'LxDevice',                
            operType: "SELECT",                     
            permission:  null ,     
            sort: 9,                   
            searchSort: 9,             
            dict:  null ,         
            sysDict:  null ,              
            isDefault:  false ,                 
            sortable:  false ,                     
            sortType:  null ,                        
            reflection:  null ,     
            insertable:  true ,                   
            updatable:  false ,                      
            selectable:  false ,                    
            listable:  false ,                       
            selectType:   1 ,     
            tableWidth:  null ,     
        },
        {
            id: 1224,         
            tableName: 'lx_device',      
            tableAlias: 'lx_device',     
            columnName: 'create_time',     
            columnAlias: 'create_time',    
            javaType: 'Date',         
            dbType: 'datetime',         
            property: 'createTime',        
            name: '创建时间',        
            value: null,                            
            compType:  1 ,      
            boClass: 'LxDevice',                
            voClass: 'LxDevice',                
            doClass: 'LxDevice',                
            operType: "SELECT",                     
            permission:  null ,     
            sort: 10,                   
            searchSort: 10,             
            dict:  null ,         
            sysDict:  null ,              
            isDefault:  false ,                 
            sortable:  false ,                     
            sortType:  null ,                        
            reflection:  null ,     
            insertable:  true ,                   
            updatable:  false ,                      
            selectable:  false ,                    
            listable:  false ,                       
            selectType:   4 ,     
            tableWidth:  null ,     
        },
        {
            id: 1225,         
            tableName: 'lx_device',      
            tableAlias: 'lx_device',     
            columnName: 'update_by',     
            columnAlias: 'update_by',    
            javaType: 'String',         
            dbType: 'varchar(64)',         
            property: 'updateBy',        
            name: '更新者',        
            value: null,                            
            compType:  1 ,      
            boClass: 'LxDevice',                
            voClass: 'LxDevice',                
            doClass: 'LxDevice',                
            operType: "SELECT",                     
            permission:  null ,     
            sort: 11,                   
            searchSort: 11,             
            dict:  null ,         
            sysDict:  null ,              
            isDefault:  false ,                 
            sortable:  false ,                     
            sortType:  null ,                        
            reflection:  null ,     
            insertable:  true ,                   
            updatable:  true ,                      
            selectable:  false ,                    
            listable:  false ,                       
            selectType:   1 ,     
            tableWidth:  null ,     
        },
        {
            id: 1226,         
            tableName: 'lx_device',      
            tableAlias: 'lx_device',     
            columnName: 'update_time',     
            columnAlias: 'update_time',    
            javaType: 'Date',         
            dbType: 'datetime',         
            property: 'updateTime',        
            name: '更新时间',        
            value: null,                            
            compType:  1 ,      
            boClass: 'LxDevice',                
            voClass: 'LxDevice',                
            doClass: 'LxDevice',                
            operType: "SELECT",                     
            permission:  null ,     
            sort: 12,                   
            searchSort: 12,             
            dict:  null ,         
            sysDict:  null ,              
            isDefault:  false ,                 
            sortable:  false ,                     
            sortType:  null ,                        
            reflection:  null ,     
            insertable:  true ,                   
            updatable:  true ,                      
            selectable:  false ,                    
            listable:  false ,                       
            selectType:   4 ,     
            tableWidth:  null ,     
        },
]