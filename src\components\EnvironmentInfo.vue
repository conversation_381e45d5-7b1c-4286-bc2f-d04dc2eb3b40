<script setup>
import { ref, onMounted, inject, onBeforeUnmount } from 'vue';

// 注入GSAP
const gsap = inject('gsap');

// 接收移动设备状态
const props = defineProps({
  isMobile: {
    type: Boolean,
    default: false
  }
});

// 网络状态
const isOnline = ref(navigator.onLine);
const checkNetworkStatus = () => {
  isOnline.value = navigator.onLine;
};

// 监听网络状态变化
onMounted(() => {
  window.addEventListener('online', checkNetworkStatus);
  window.addEventListener('offline', checkNetworkStatus);
});

onBeforeUnmount(() => {
  window.removeEventListener('online', checkNetworkStatus);
  window.removeEventListener('offline', checkNetworkStatus);
});

// 模拟环境数据
const environmentData = ref({
  temperature: '33°C',
  humidity: '89%',
  windDirection: '东南',
  windSpeed: '2级'
});

// 添加数据更新动画标记
const dataUpdateMarkers = ref({
  temperature: false,
  humidity: false,
  windDirection: false,
  windSpeed: false
});

// 模拟数据更新动画
const animateDataUpdate = (dataKey) => {
  dataUpdateMarkers.value[dataKey] = true;
  setTimeout(() => {
    dataUpdateMarkers.value[dataKey] = false;
  }, 1000);
};

// 获取当前时间
const currentDateTime = ref('');

const updateDateTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');

  currentDateTime.value = `${year}年${month}月${day}日 ${hours}:${minutes}`;
};

onMounted(() => {
  updateDateTime();
  // 每分钟更新一次时间
  setInterval(updateDateTime, 60000);

  // 添加进入动画
  const headerEl = document.querySelector('.header-container');
  if (headerEl) {
    gsap.from(headerEl, {
      y: -20,
      opacity: 0,
      duration: 0.8,
      ease: "power2.out"
    });
  }

  // 模拟数据更新动画（每隔10秒随机更新一个数据）
  setInterval(() => {
    const keys = ['temperature', 'humidity', 'windDirection', 'windSpeed'];
    const randomKey = keys[Math.floor(Math.random() * keys.length)];
    animateDataUpdate(randomKey);
  }, 10000);
});
</script>

<template>
  <div class="environment-info" :class="{ 'mobile': isMobile }">
    <div class="header-container">
      <!-- PC端布局 -->
      <div v-if="!isMobile" class="pc-header">
        <div class="datetime">{{ currentDateTime }}</div>
        <div class="title">DCS环境模拟新质生产力展示平台</div>
        <div class="weather-info">
          <div class="weather-item" :class="{ 'data-update': dataUpdateMarkers.temperature }">
            温度: <span class="value">{{ environmentData.temperature }}</span>
          </div>
          <div class="weather-item" :class="{ 'data-update': dataUpdateMarkers.humidity }">
            湿度: <span class="value">{{ environmentData.humidity }}</span>
          </div>
          <div class="weather-item" :class="{ 'data-update': dataUpdateMarkers.windDirection }">
            风向: <span class="value">{{ environmentData.windDirection }}</span>
          </div>
          <div class="weather-item" :class="{ 'data-update': dataUpdateMarkers.windSpeed }">
            风速: <span class="value">{{ environmentData.windSpeed }}</span>
          </div>
        </div>
      </div>

      <!-- 移动端布局 -->
      <div v-else class="mobile-header">
        <div class="title">DCS环境模拟新质生产力展示平台</div>
        <div class="weather-container">
          <div class="weather-row">
            <div class="weather-item" :class="{ 'data-update': dataUpdateMarkers.temperature }">
              温度: <span class="value">{{ environmentData.temperature }}</span>
            </div>
            <div class="weather-item" :class="{ 'data-update': dataUpdateMarkers.windDirection }">
              风向: <span class="value">{{ environmentData.windDirection }}</span>
            </div>
          </div>
          <div class="weather-row">
            <div class="weather-item" :class="{ 'data-update': dataUpdateMarkers.humidity }">
              湿度: <span class="value">{{ environmentData.humidity }}</span>
            </div>
            <div class="weather-item" :class="{ 'data-update': dataUpdateMarkers.windSpeed }">
              风速: <span class="value">{{ environmentData.windSpeed }}</span>
            </div>
          </div>
        </div>
        <div class="ant-card-extra">
          <span class="network-status" :class="{ 'online': isOnline, 'offline': !isOnline }">
            <span class="status-dot"></span>
            {{ isOnline ? '在线' : '离线' }}
          </span>
          <span class="ant-tag ant-tag-has-color status-tag running-tag" style="background-color: rgb(46, 204, 113);">运行中</span>
          <button class="ant-btn ant-btn-primary ant-btn-circle ant-btn-sm power-btn" type="button" style="margin-left: 8px; background-color: rgb(52, 152, 219); border-color: rgb(52, 152, 219);">
            <span role="img" aria-label="poweroff" class="anticon anticon-poweroff">
              <svg focusable="false" data-icon="poweroff" width="1em" height="1em" fill="currentColor" aria-hidden="true" viewBox="64 64 896 896">
                <path d="M705.6 124.9a8 8 0 00-11.6 7.2v64.2c0 5.5 2.9 10.6 7.5 13.6a352.2 352.2 0 0162.2 49.8c32.7 32.8 58.4 70.9 76.3 113.3a355 355 0 0127.9 138.7c0 48.1-9.4 94.8-27.9 138.7a355.92 355.92 0 01-76.3 113.3 353.06 353.06 0 01-113.2 76.4c-43.8 18.6-90.5 28-138.5 28s-94.7-9.4-138.5-28a353.06 353.06 0 01-113.2-76.4A355.92 355.92 0 01184 650.4a355 355 0 01-27.9-138.7c0-48.1 9.4-94.8 27.9-138.7 17.9-42.4 43.6-80.5 76.3-113.3 19-19 39.8-35.6 62.2-49.8 4.7-2.9 7.5-8.1 7.5-13.6V132c0-6-6.3-9.8-11.6-7.2C178.5 195.2 82 339.3 80 506.3 77.2 745.1 272.5 943.5 511.2 944c239 .5 432.8-193.3 432.8-432.4 0-169.2-97-315.7-238.4-386.7zM480 560h64c4.4 0 8-3.6 8-8V88c0-4.4-3.6-8-8-8h-64c-4.4 0-8 3.6-8 8v464c0 4.4 3.6 8 8 8z"></path>
              </svg>
            </span>
          </button>
        </div>
        <div class="datetime">{{ currentDateTime }}</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 添加数据更新动效 */
@keyframes pulse {
  0% { transform: scale(1); color: #FFFFFF; }
  50% { transform: scale(1.1); color: #3498DB; }
  100% { transform: scale(1); color: #FFFFFF; }
}

.data-update .value {
  display: inline-block;
  animation: pulse 1s ease;
}

/* 添加卡片悬停效果 */
.header-container {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.header-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.4);
}

/* 网络状态样式 */
.network-status {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  margin-right: 8px;
  transition: all 0.3s;
}

.network-status .status-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 4px;
}

.network-status.online {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.network-status.online .status-dot {
  background-color: #2ecc71;
  box-shadow: 0 0 6px #2ecc71;
}

.network-status.offline {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.network-status.offline .status-dot {
  background-color: #e74c3c;
  box-shadow: 0 0 6px #e74c3c;
}

.environment-info {
  width: 100%;
}

.header-container {
  width: 100%;
  background-color: #0D275A;
  border: none;
  box-shadow: 0 8px 16px rgba(0,0,0,0.3);
  border-radius: 12px;
  margin-bottom: 16px;
}

/* PC端样式 */
.pc-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.1rem 0.2rem;
  height: 0.5rem;
  position: relative;
  border-bottom: none;
}

.datetime {
  font-size: 0.14rem;
  color: #FFFFFF;
  flex: 1;
}

.title {
  font-size: 0.18rem;
  font-weight: bold;
  color: #FFFFFF;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
}

.weather-info {
  display: flex;
  gap: 0.15rem;
  flex: 1;
  justify-content: flex-end;
}

.weather-item {
  font-size: 0.14rem;
  color: #FFFFFF;
  white-space: nowrap;
}

/* 移动端样式 */
.mobile-header {
  padding: 0.1rem;
  text-align: center;
}

.mobile-header .title {
  font-size: 0.16rem;
  font-weight: bold;
  margin-bottom: 0.1rem;
  position: static;
  transform: none;
  color: #FFFFFF;
}

.weather-container {
  margin: 0.1rem 0;
}

.weather-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.05rem;
}

.mobile-header .datetime {
  font-size: 0.12rem;
  text-align: center;
  margin-top: 0.05rem;
  color: #FFFFFF;
}

.mobile-header .weather-item {
  font-size: 0.12rem;
  white-space: nowrap;
  color: #FFFFFF;
}
</style>
