import request from '@/utils/request'

// 4.执行器管理 执行日志详细
export function actuatorJobgroupApi(query) {
    return request({
        url: '/xxl-job/jobgroup/pageList',
        method: 'get',
        params: query
    })
}
// 4.执行器管理 新增执行器
export function actuatorSaveApi(query) {
    return request({
        url: '/xxl-job/jobgroup/save',
        method: 'get',
        params: query
    })
}

// 4.执行器管理 编辑执行器
export function actuatorUpdateApi(query) {
    return request({
        url: '/xxl-job/jobgroup/update',
        method: 'post',
        params: query
    })
}
// 4.执行器管理 删除执行器
export function actuatorRemoveApi(query) {
    return request({
        url: '/xxl-job/jobgroup/remove',
        method: 'post',
        params: query
    })
}