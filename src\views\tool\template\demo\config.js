// 列定义
export let fieldList = [
        {
            id: 689,
            tableName: 'lx_demo',
            tableAlias: 'lx_demo',
            columnName: 'id',
            columnAlias: 'id',
            javaType: 'Long',
            dbType: 'bigint',
            property: 'id',
            name: '主键',
            value: null,
            compType:  1 ,
            boClass: 'LxDemo',
            voClass: 'LxDemo',
            doClass: 'LxDemo',
            operType: "SELECT",
            permission:  null ,
            sort: 1,
            searchSort: 1,
            dict:  null ,
            sysDict:  null ,
            isDefault:  false ,
            sortable:  false ,
            sortType:  'desc' ,
            reflection:  null ,
            insertable:  true ,
            updatable:  false ,
            selectable:  false ,
            listable:  false ,
            selectType:   1 ,
            tableWidth:  null ,
        },
        {
            id: 690,
            tableName: 'lx_demo',
            tableAlias: 'lx_demo',
            columnName: 'name',
            columnAlias: 'name',
            javaType: 'String',
            dbType: 'varchar(100)',
            property: 'name',
            name: '名称',
            value: null,
            compType:  12 ,
            boClass: 'LxDemo',
            voClass: 'LxDemo',
            doClass: 'LxDemo',
            operType: "SELECT",
            permission:  null ,
            sort: 2,
            searchSort: 2,
            dict:  null ,
            sysDict:  null ,
            isDefault:  true ,
            sortable:  false ,
            sortType:  null ,
            reflection:  null ,
            insertable:  true ,
            updatable:  true ,
            selectable:  true ,
            listable:  true ,
            selectType:   1 ,
            tableWidth:  null ,
        },
        {
            id: 691,
            tableName: 'lx_demo',
            tableAlias: 'lx_demo',
            columnName: 'code',
            columnAlias: 'code',
            javaType: 'String',
            dbType: 'varchar(100)',
            property: 'code',
            name: '编号',
            value: null,
            compType:  1 ,
            boClass: 'LxDemo',
            voClass: 'LxDemo',
            doClass: 'LxDemo',
            operType: "SELECT",
            permission:  null ,
            sort: 3,
            searchSort: 3,
            dict:  null ,
            sysDict:  null ,
            isDefault:  false ,
            sortable:  false ,
            sortType:  null ,
            reflection:  null ,
            insertable:  true ,
            updatable:  true ,
            selectable:  true ,
            listable:  true ,
            selectType:   1 ,
            tableWidth:  null ,
        },
        {
            id: 692,
            tableName: 'lx_demo',
            tableAlias: 'lx_demo',
            columnName: 'create_by',
            columnAlias: 'create_by',
            javaType: 'String',
            dbType: 'varchar(64)',
            property: 'createBy',
            name: '创建者',
            value: null,
            compType:  1 ,
            boClass: 'LxDemo',
            voClass: 'LxDemo',
            doClass: 'LxDemo',
            operType: "SELECT",
            permission:  null ,
            sort: 4,
            searchSort: 4,
            dict:  null ,
            sysDict:  null ,
            isDefault:  false ,
            sortable:  false ,
            sortType:  null ,
            reflection:  null ,
            insertable:  true ,
            updatable:  false ,
            selectable:  false ,
            listable:  false ,
            selectType:   1 ,
            tableWidth:  null ,
        },
        {
            id: 693,
            tableName: 'lx_demo',
            tableAlias: 'lx_demo',
            columnName: 'create_time',
            columnAlias: 'create_time',
            javaType: 'Date',
            dbType: 'datetime',
            property: 'createTime',
            name: '创建时间',
            value: null,
            compType:  12 ,
            boClass: 'LxDemo',
            voClass: 'LxDemo',
            doClass: 'LxDemo',
            operType: "SELECT",
            permission:  null ,
            sort: 5,
            searchSort: 5,
            dict:  null ,
            sysDict:  null ,
            isDefault:  true ,
            sortable:  false ,
            sortType:  null ,
            reflection:  null ,
            insertable:  true ,
            updatable:  true ,
            selectable:  true ,
            listable:  false ,
            selectType:   4 ,
            tableWidth:  null ,
        },
        {
            id: 694,
            tableName: 'lx_demo',
            tableAlias: 'lx_demo',
            columnName: 'update_by',
            columnAlias: 'update_by',
            javaType: 'String',
            dbType: 'varchar(64)',
            property: 'updateBy',
            name: '更新者',
            value: null,
            compType:  1 ,
            boClass: 'LxDemo',
            voClass: 'LxDemo',
            doClass: 'LxDemo',
            operType: "SELECT",
            permission:  null ,
            sort: 6,
            searchSort: 6,
            dict:  null ,
            sysDict:  null ,
            isDefault:  false ,
            sortable:  false ,
            sortType:  null ,
            reflection:  null ,
            insertable:  true ,
            updatable:  true ,
            selectable:  false ,
            listable:  false ,
            selectType:   1 ,
            tableWidth:  null ,
        },
        {
            id: 695,
            tableName: 'lx_demo',
            tableAlias: 'lx_demo',
            columnName: 'update_time',
            columnAlias: 'update_time',
            javaType: 'Date',
            dbType: 'datetime',
            property: 'updateTime',
            name: '更新时间',
            value: null,
            compType:  1 ,
            boClass: 'LxDemo',
            voClass: 'LxDemo',
            doClass: 'LxDemo',
            operType: "SELECT",
            permission:  null ,
            sort: 7,
            searchSort: 7,
            dict:  null ,
            sysDict:  null ,
            isDefault:  false ,
            sortable:  false ,
            sortType:  null ,
            reflection:  null ,
            insertable:  true ,
            updatable:  true ,
            selectable:  false ,
            listable:  false ,
            selectType:   4 ,
            tableWidth:  null ,
        },
        {
            id: 696,
            tableName: 'lx_demo',
            tableAlias: 'lx_demo',
            columnName: 'remark',
            columnAlias: 'remark',
            javaType: 'String',
            dbType: 'varchar(500)',
            property: 'remark',
            name: '备注',
            value: null,
            compType:  1 ,
            boClass: 'LxDemo',
            voClass: 'LxDemo',
            doClass: 'LxDemo',
            operType: "SELECT",
            permission:  null ,
            sort: 8,
            searchSort: 8,
            dict:  null ,
            sysDict:  null ,
            isDefault:  false ,
            sortable:  false ,
            sortType:  null ,
            reflection:  null ,
            insertable:  true ,
            updatable:  true ,
            selectable:  false ,
            listable:  true ,
            selectType:   1 ,
            tableWidth:  null ,
        },
        {
            id: 697,
            tableName: 'lx_demo',
            tableAlias: 'lx_demo',
            columnName: 'gender',
            columnAlias: 'gender',
            javaType: 'String',
            dbType: 'char(1)',
            property: 'gender',
            name: '性别',
            value: null,
            compType:  1 ,
            boClass: 'LxDemo',
            voClass: 'LxDemo',
            doClass: 'LxDemo',
            operType: "SELECT",
            permission:  null ,
            sort: 9,
            searchSort: 9,
            dict:  null ,
            sysDict:  'lx_demo_gender' ,
            isDefault:  true ,
            sortable:  false ,
            sortType:  null ,
            reflection:  null ,
            insertable:  true ,
            updatable:  true ,
            selectable:  true ,
            listable:  true ,
            selectType:   2 ,
            tableWidth:  null ,
        },
        {
            id: 698,
            tableName: 'lx_demo',
            tableAlias: 'lx_demo',
            columnName: 'status',
            columnAlias: 'status',
            javaType: 'String',
            dbType: 'char(1)',
            property: 'status',
            name: '状态',
            value: null,
            compType:  1 ,
            boClass: 'LxDemo',
            voClass: 'LxDemo',
            doClass: 'LxDemo',
            operType: "SELECT",
            permission:  null ,
            sort: 10,
            searchSort: 10,
            dict:  null ,
            sysDict:  'lx_demo_status' ,
            isDefault:  true ,
            sortable:  false ,
            sortType:  null ,
            reflection:  null ,
            insertable:  true ,
            updatable:  true ,
            selectable:  true ,
            listable:  true ,
            selectType:   2 ,
            tableWidth:  null ,
        },
        {
            id: 699,
            tableName: 'lx_demo',
            tableAlias: 'lx_demo',
            columnName: 'notice_type',
            columnAlias: 'notice_type',
            javaType: 'String',
            dbType: 'char(2)',
            property: 'noticeType',
            name: '通知类型',
            value: null,
            compType:  1 ,
            boClass: 'LxDemo',
            voClass: 'LxDemo',
            doClass: 'LxDemo',
            operType: "SELECT",
            permission:  null ,
            sort: 11,
            searchSort: 11,
            dict:  null ,
            sysDict:  'lx_demo_notice_type' ,
            isDefault:  true ,
            sortable:  false ,
            sortType:  null ,
            reflection:  null ,
            insertable:  true ,
            updatable:  true ,
            selectable:  true ,
            listable:  true ,
            selectType:   2 ,
            tableWidth:  null ,
        },
]