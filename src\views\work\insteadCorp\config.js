// 列定义
export let fieldList = [
        {
            id: 1150,          // 列id，唯一  
            tableName: 'lx_instant_corp',       // 数据库表名，后端使用  
            tableAlias: 'lx_instant_corp',      // 数据库表别名，后端使用  
            columnName: '主键',      // 数据库字段列名，后端使用  
            columnAlias: '主键',     // 数据库字段别名，后端使用  
            javaType: 'Long',          // 后端字段类型，后端使用  
            dbType: 'bigint',          // 数据库字段类型，后端使用  
            property: '主键',         // 字段属性名  
            name: '主键',         // 字段名称（标签）  
            value: null,                             // 字段值，用于搜索、新增、修改时填入的值  
        compType:  1 ,       // 比较符类型，系统字典：sys_comp_type  
            boClass: 'LxInstantCorp',                 // BO类，后端使用  
            voClass: 'LxInstantCorp',                 // VO类，后端使用  
            doClass: 'LxInstantCorp',                 // DO类，后端使用  
            operType: "SELECT",                      // 操作类型，SELECT：查询、SAVE：新增、UPDATE：修改  
        permission:  null ,      // 字段权限字符  
            sort: 1,                    // 排序顺序，从小到大排序  
            searchSort: 1,              // 排序顺序，从小到大排序  
        dict:  null ,          // 用户字典  
        sysDict:  null ,               // 系统字典  
        isDefault:  false ,                  // 是否默认查询条件  
        sortable:  false ,                      // 是否可排序  
        sortType:  'desc' ,                         // 排序类型，asc：升序、desc：降序  
        reflection:  null ,      // 引用（废弃）  
        insertable:  true ,                    // 字段是否可新增  
        updatable:  false ,                       // 字段是否可修改  
        selectable:  false ,                     // 字段是否可查询  
        listable:  false ,                        // 字段是否展示在列表上  
        selectType:   1 ,      // 控件类型，1：文本输入、2：数字输入、3：下拉选择、4：时间选择  
        tableWidth:  null ,      // 字段的列表宽度  
        },
        {
            id: 1151,         
            tableName: 'lx_instant_corp',      
            tableAlias: 'lx_instant_corp',     
            columnName: 'permanent_code',     
            columnAlias: 'permanent_code',    
            javaType: 'String',         
            dbType: 'varchar(128)',         
            property: 'permanentCode',        
            name: '企业微信永久授权码',        
            value: null,                            
        compType:  1 ,      
            boClass: 'LxInstantCorp',                
            voClass: 'LxInstantCorp',                
            doClass: 'LxInstantCorp',                
            operType: "SELECT",                     
        permission:  null ,     
            sort: 2,                   
            searchSort: 2,             
        dict:  null ,         
        sysDict:  null ,              
        isDefault:  false ,                 
        sortable:  false ,                     
        sortType:  null ,                        
        reflection:  null ,     
        insertable:  true ,                   
        updatable:  false ,                      
        selectable:  false ,                    
        listable:  true ,                       
        selectType:   1 ,     
        tableWidth:  null ,     
        },
        {
            id: 1152,         
            tableName: 'lx_instant_corp',      
            tableAlias: 'lx_instant_corp',     
            columnName: 'corpid',     
            columnAlias: 'corpid',    
            javaType: 'String',         
            dbType: 'varchar(100)',         
            property: 'corpid',        
            name: '企业微信id',        
            value: null,                            
        compType:  1 ,      
            boClass: 'LxInstantCorp',                
            voClass: 'LxInstantCorp',                
            doClass: 'LxInstantCorp',                
            operType: "SELECT",                     
        permission:  null ,     
            sort: 3,                   
            searchSort: 3,             
        dict:  null ,         
        sysDict:  null ,              
        isDefault:  false ,                 
        sortable:  false ,                     
        sortType:  null ,                        
        reflection:  null ,     
        insertable:  true ,                   
        updatable:  false ,                      
        selectable:  false ,                    
        listable:  true ,                       
        selectType:   1 ,     
        tableWidth:  null ,     
        },
        {
            id: 1153,         
            tableName: 'lx_instant_corp',      
            tableAlias: 'lx_instant_corp',     
            columnName: 'corp_name',     
            columnAlias: 'corp_name',    
            javaType: 'String',         
            dbType: 'varchar(100)',         
            property: 'corpName',        
            name: '企业简称',        
            value: null,                            
        compType:  12 ,      
            boClass: 'LxInstantCorp',                
            voClass: 'LxInstantCorp',                
            doClass: 'LxInstantCorp',                
            operType: "SELECT",                     
        permission:  null ,     
            sort: 4,                   
            searchSort: 4,             
        dict:  null ,         
        sysDict:  null ,              
        isDefault:  false ,                 
        sortable:  false ,                     
        sortType:  null ,                        
        reflection:  null ,     
        insertable:  true ,                   
        updatable:  false ,                      
        selectable:  true ,                    
        listable:  true ,                       
        selectType:   1 ,     
        tableWidth:  null ,     
        },
        {
            id: 1154,         
            tableName: 'lx_instant_corp',      
            tableAlias: 'lx_instant_corp',     
            columnName: 'corp_type',     
            columnAlias: 'corp_type',    
            javaType: 'String',         
            dbType: 'char(20)',         
            property: 'corpType',        
            name: '授权方企业类型',        
            value: null,                            
        compType:  1 ,      
            boClass: 'LxInstantCorp',                
            voClass: 'LxInstantCorp',                
            doClass: 'LxInstantCorp',                
            operType: "SELECT",                     
        permission:  null ,     
            sort: 5,                   
            searchSort: 5,             
        dict:  null ,         
        sysDict:  null ,              
        isDefault:  false ,                 
        sortable:  false ,                     
        sortType:  null ,                        
        reflection:  null ,     
        insertable:  true ,                   
        updatable:  false ,                      
        selectable:  false ,                    
        listable:  true ,                       
        selectType:   2 ,     
        tableWidth:  null ,     
        },
        {
            id: 1155,         
            tableName: 'lx_instant_corp',      
            tableAlias: 'lx_instant_corp',     
            columnName: 'corp_square_logo_url',     
            columnAlias: 'corp_square_logo_url',    
            javaType: 'String',         
            dbType: 'varchar(100)',         
            property: 'corpSquareLogoUrl',        
            name: '授权方企业方形头像',        
            value: null,                            
        compType:  1 ,      
            boClass: 'LxInstantCorp',                
            voClass: 'LxInstantCorp',                
            doClass: 'LxInstantCorp',                
            operType: "SELECT",                     
        permission:  null ,     
            sort: 6,                   
            searchSort: 6,             
        dict:  null ,         
        sysDict:  null ,              
        isDefault:  false ,                 
        sortable:  false ,                     
        sortType:  null ,                        
        reflection:  null ,     
        insertable:  true ,                   
        updatable:  false ,                      
        selectable:  false ,                    
        listable:  true ,                       
        selectType:   1 ,     
        tableWidth:  null ,     
        },
        {
            id: 1156,         
            tableName: 'lx_instant_corp',      
            tableAlias: 'lx_instant_corp',     
            columnName: 'corp_user_max',     
            columnAlias: 'corp_user_max',    
            javaType: 'String',         
            dbType: 'varchar(100)',         
            property: 'corpUserMax',        
            name: '授权方企业用户规模',        
            value: null,                            
        compType:  1 ,      
            boClass: 'LxInstantCorp',                
            voClass: 'LxInstantCorp',                
            doClass: 'LxInstantCorp',                
            operType: "SELECT",                     
        permission:  null ,     
            sort: 7,                   
            searchSort: 7,             
        dict:  null ,         
        sysDict:  null ,              
        isDefault:  false ,                 
        sortable:  false ,                     
        sortType:  null ,                        
        reflection:  null ,     
        insertable:  true ,                   
        updatable:  false ,                      
        selectable:  false ,                    
        listable:  true ,                       
        selectType:   1 ,     
        tableWidth:  null ,     
        },
        {
            id: 1157,         
            tableName: 'lx_instant_corp',      
            tableAlias: 'lx_instant_corp',     
            columnName: 'subject_type',     
            columnAlias: 'subject_type',    
            javaType: 'String',         
            dbType: 'char(1)',         
            property: 'subjectType',        
            name: '企业类型',        
            value: null,                            
        compType:  1 ,      
            boClass: 'LxInstantCorp',                
            voClass: 'LxInstantCorp',                
            doClass: 'LxInstantCorp',                
            operType: "SELECT",                     
        permission:  null ,     
            sort: 8,                   
            searchSort: 8,             
        dict:  null ,         
        sysDict:  null ,              
        isDefault:  false ,                 
        sortable:  false ,                     
        sortType:  null ,                        
        reflection:  null ,     
        insertable:  true ,                   
        updatable:  false ,                      
        selectable:  false ,                    
        listable:  true ,                       
        selectType:   2 ,     
        tableWidth:  null ,     
        },
        {
            id: 1158,         
            tableName: 'lx_instant_corp',      
            tableAlias: 'lx_instant_corp',     
            columnName: 'verified_end_time',     
            columnAlias: 'verified_end_time',    
            javaType: 'Date',         
            dbType: 'datetime',         
            property: 'verifiedEndTime',        
            name: '认证到期时间',        
            value: null,                            
        compType:  11 ,      
            boClass: 'LxInstantCorp',                
            voClass: 'LxInstantCorp',                
            doClass: 'LxInstantCorp',                
            operType: "SELECT",                     
        permission:  null ,     
            sort: 9,                   
            searchSort: 9,             
        dict:  null ,         
        sysDict:  null ,              
        isDefault:  false ,                 
        sortable:  false ,                     
        sortType:  null ,                        
        reflection:  null ,     
        insertable:  true ,                   
        updatable:  false ,                      
        selectable:  true ,                    
        listable:  true ,                       
        selectType:   4 ,     
        tableWidth:  null ,     
        },
        {
            id: 1159,         
            tableName: 'lx_instant_corp',      
            tableAlias: 'lx_instant_corp',     
            columnName: 'agentid',     
            columnAlias: 'agentid',    
            javaType: 'String',         
            dbType: 'varchar(100)',         
            property: 'agentid',        
            name: '授权方应用id',        
            value: null,                            
        compType:  1 ,      
            boClass: 'LxInstantCorp',                
            voClass: 'LxInstantCorp',                
            doClass: 'LxInstantCorp',                
            operType: "SELECT",                     
        permission:  null ,     
            sort: 10,                   
            searchSort: 10,             
        dict:  null ,         
        sysDict:  null ,              
        isDefault:  false ,                 
        sortable:  false ,                     
        sortType:  null ,                        
        reflection:  null ,     
        insertable:  true ,                   
        updatable:  false ,                      
        selectable:  false ,                    
        listable:  true ,                       
        selectType:   1 ,     
        tableWidth:  null ,     
        },
        {
            id: 1160,         
            tableName: 'lx_instant_corp',      
            tableAlias: 'lx_instant_corp',     
            columnName: 'name',     
            columnAlias: 'name',    
            javaType: 'String',         
            dbType: 'varchar(100)',         
            property: 'name',        
            name: '授权方应用名字',        
            value: null,                            
        compType:  12 ,      
            boClass: 'LxInstantCorp',                
            voClass: 'LxInstantCorp',                
            doClass: 'LxInstantCorp',                
            operType: "SELECT",                     
        permission:  null ,     
            sort: 11,                   
            searchSort: 11,             
        dict:  null ,         
        sysDict:  null ,              
        isDefault:  false ,                 
        sortable:  false ,                     
        sortType:  null ,                        
        reflection:  null ,     
        insertable:  true ,                   
        updatable:  false ,                      
        selectable:  true ,                    
        listable:  true ,                       
        selectType:   1 ,     
        tableWidth:  null ,     
        },
        {
            id: 1161,         
            tableName: 'lx_instant_corp',      
            tableAlias: 'lx_instant_corp',     
            columnName: 'content',     
            columnAlias: 'content',    
            javaType: 'String',         
            dbType: 'text',         
            property: 'content',        
            name: '源数据json内容',        
            value: null,                            
        compType:  1 ,      
            boClass: 'LxInstantCorp',                
            voClass: 'LxInstantCorp',                
            doClass: 'LxInstantCorp',                
            operType: "SELECT",                     
        permission:  null ,     
            sort: 12,                   
            searchSort: 12,             
        dict:  null ,         
        sysDict:  null ,              
        isDefault:  false ,                 
        sortable:  false ,                     
        sortType:  null ,                        
        reflection:  null ,     
        insertable:  false ,                   
        updatable:  false ,                      
        selectable:  false ,                    
        listable:  false ,                       
        selectType:   1 ,     
        tableWidth:  null ,     
        },
        {
            id: 1162,         
            tableName: 'lx_instant_corp',      
            tableAlias: 'lx_instant_corp',     
            columnName: 'create_uid',     
            columnAlias: 'create_uid',    
            javaType: 'Long',         
            dbType: 'bigint',         
            property: 'createUid',        
            name: '创建用户id',        
            value: null,                            
        compType:  1 ,      
            boClass: 'LxInstantCorp',                
            voClass: 'LxInstantCorp',                
            doClass: 'LxInstantCorp',                
            operType: "SELECT",                     
        permission:  null ,     
            sort: 13,                   
            searchSort: 13,             
        dict:  null ,         
        sysDict:  null ,              
        isDefault:  false ,                 
        sortable:  false ,                     
        sortType:  null ,                        
        reflection:  null ,     
        insertable:  false ,                   
        updatable:  false ,                      
        selectable:  false ,                    
        listable:  false ,                       
        selectType:   1 ,     
        tableWidth:  null ,     
        },
        {
            id: 1163,         
            tableName: 'lx_instant_corp',      
            tableAlias: 'lx_instant_corp',     
            columnName: 'create_time',     
            columnAlias: 'create_time',    
            javaType: 'Date',         
            dbType: 'datetime',         
            property: 'createTime',        
            name: '创建时间',        
            value: null,                            
        compType:  1 ,      
            boClass: 'LxInstantCorp',                
            voClass: 'LxInstantCorp',                
            doClass: 'LxInstantCorp',                
            operType: "SELECT",                     
        permission:  null ,     
            sort: 14,                   
            searchSort: 14,             
        dict:  null ,         
        sysDict:  null ,              
        isDefault:  false ,                 
        sortable:  false ,                     
        sortType:  null ,                        
        reflection:  null ,     
        insertable:  true ,                   
        updatable:  false ,                      
        selectable:  false ,                    
        listable:  false ,                       
        selectType:   4 ,     
        tableWidth:  null ,     
        },
        {
            id: 1164,         
            tableName: 'lx_instant_corp',      
            tableAlias: 'lx_instant_corp',     
            columnName: 'update_uid',     
            columnAlias: 'update_uid',    
            javaType: 'Long',         
            dbType: 'bigint',         
            property: 'updateUid',        
            name: '更新用户id',        
            value: null,                            
        compType:  1 ,      
            boClass: 'LxInstantCorp',                
            voClass: 'LxInstantCorp',                
            doClass: 'LxInstantCorp',                
            operType: "SELECT",                     
        permission:  null ,     
            sort: 15,                   
            searchSort: 15,             
        dict:  null ,         
        sysDict:  null ,              
        isDefault:  false ,                 
        sortable:  false ,                     
        sortType:  null ,                        
        reflection:  null ,     
        insertable:  false ,                   
        updatable:  false ,                      
        selectable:  false ,                    
        listable:  false ,                       
        selectType:   1 ,     
        tableWidth:  null ,     
        },
        {
            id: 1165,         
            tableName: 'lx_instant_corp',      
            tableAlias: 'lx_instant_corp',     
            columnName: 'update_time',     
            columnAlias: 'update_time',    
            javaType: 'Date',         
            dbType: 'datetime',         
            property: 'updateTime',        
            name: '更新时间',        
            value: null,                            
        compType:  1 ,      
            boClass: 'LxInstantCorp',                
            voClass: 'LxInstantCorp',                
            doClass: 'LxInstantCorp',                
            operType: "SELECT",                     
        permission:  null ,     
            sort: 16,                   
            searchSort: 16,             
        dict:  null ,         
        sysDict:  null ,              
        isDefault:  false ,                 
        sortable:  false ,                     
        sortType:  null ,                        
        reflection:  null ,     
        insertable:  true ,                   
        updatable:  true ,                      
        selectable:  false ,                    
        listable:  false ,                       
        selectType:   4 ,     
        tableWidth:  null ,     
        },
]