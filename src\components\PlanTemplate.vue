<script setup>
import { ref, onMounted, inject, onBeforeUnmount } from 'vue';
import { PoweroffOutlined, PlayCircleOutlined, StopOutlined } from '@ant-design/icons-vue';
import { getWebSocketInstance } from '../utils/websocket.js';

// 注入GSAP
const gsap = inject('gsap');

// 网络状态
const isOnline = ref(navigator.onLine);
const checkNetworkStatus = () => {
  isOnline.value = navigator.onLine;
};

// WebSocket实例
const ws = ref(null);

// 模板控制状态（仅用于UI显示）
const templateControlling = ref({});

// 工程模板数据
const templates = ref([
  {
    id: 1,
    name: '全区域清洗模板',
    devices: [
      { type: 'waterPump', name: '水泵1', ip: '*************' },
      { type: 'waterCannon', name: '水炮1', ip: '*************' },
      { type: 'waterCannon', name: '水炮2', ip: '*************' },
      { type: 'fogCannon', name: '雾炮1', ip: '192.168.1.301' }
    ]
  },
  {
    id: 2,
    name: '局部清洗模板',
    devices: [
      { type: 'waterPump', name: '水泵1', ip: '*************' },
      { type: 'waterCannon', name: '水炮1', ip: '*************' }
    ]
  },
  {
    id: 3,
    name: '降尘模板',
    devices: [
      { type: 'fogCannon', name: '雾炮1', ip: '192.168.1.301' },
      { type: 'fogCannon', name: '雾炮2', ip: '192.168.1.302' }
    ]
  },
  {
    id: 4,
    name: '应急清洗模板',
    devices: [
      { type: 'waterPump', name: '水泵1', ip: '*************' },
      { type: 'waterPump', name: '水泵2', ip: '*************' },
      { type: 'waterCannon', name: '水炮1', ip: '*************' },
      { type: 'waterCannon', name: '水炮2', ip: '*************' },
      { type: 'waterCannon', name: '水炮3', ip: '*************' },
      { type: 'fogCannon', name: '雾炮1', ip: '192.168.1.301' },
      { type: 'fogCannon', name: '雾炮1', ip: '192.168.1.301' }
    ]
  }
]);

// 监听网络状态变化
onMounted(() => {
  window.addEventListener('online', checkNetworkStatus);
  window.addEventListener('offline', checkNetworkStatus);
});

onBeforeUnmount(() => {
  window.removeEventListener('online', checkNetworkStatus);
  window.removeEventListener('offline', checkNetworkStatus);
  
  // 关闭WebSocket连接
  if (ws.value) {
    ws.value.close();
  }
});

// 初始化WebSocket连接
const initWebSocket = () => {
  ws.value = getWebSocketInstance({
    reconnectInterval: 5000,
    maxReconnectAttempts: 10,
    heartbeatInterval: 30000
  });

  // 监听WebSocket事件
  ws.value.on('open', () => {
    console.log('工程模板WebSocket连接成功');
  });

  ws.value.on('message', handleWebSocketMessage);
  
  ws.value.on('error', (error) => {
    console.error('工程模板WebSocket错误:', error);
  });

  ws.value.on('close', () => {
    console.log('工程模板WebSocket连接关闭');
  });

  // 连接到WebSocket服务器
  ws.value.connect('ws://localhost:8080/ws/device');
};

// 处理WebSocket消息
const handleWebSocketMessage = (data) => {
  console.log('收到工程模板WebSocket消息:', data);
  
  if (data.type === 'TEMPLATE_CONTROL_RESULT') {
    console.log('模板控制结果:', data.payload);
    // 控制完成后重置控制状态
    const templateId = data.payload.templateId;
    if (templateControlling.value[templateId]) {
      templateControlling.value[templateId] = false;
    }
  } else if (data.type === 'ERROR') {
    console.error('模板操作错误:', data.payload?.message);
    // 发生错误时也重置控制状态
    Object.keys(templateControlling.value).forEach(id => {
      templateControlling.value[id] = false;
    });
  }
};

// 启动模板（发送设备控制指令）
const startTemplate = (template) => {
  if (!ws.value || !ws.value.isConnected()) {
    console.warn('WebSocket未连接，无法控制模板');
    return;
  }

  // 设置控制中状态
  templateControlling.value[template.id] = true;

  const message = {
    type: 'START_TEMPLATE',
    requestId: `template_start_${template.id}_${Date.now()}`,
    payload: {
      templateId: template.id,
      devices: template.devices
    }
  };

  ws.value.send(message);
  console.log(`启动模板: ${template.name}`);
};

// 停止模板（发送设备停止指令）
const stopTemplate = (template) => {
  if (!ws.value || !ws.value.isConnected()) {
    console.warn('WebSocket未连接，无法控制模板');
    return;
  }

  // 设置控制中状态
  templateControlling.value[template.id] = true;

  const message = {
    type: 'STOP_TEMPLATE',
    requestId: `template_stop_${template.id}_${Date.now()}`,
    payload: {
      templateId: template.id,
      devices: template.devices
    }
  };

  ws.value.send(message);
  console.log(`停止模板: ${template.name}`);
};

// 判断模板是否正在控制中
const isTemplateControlling = (template) => {
  return templateControlling.value[template.id] || false;
};

// 判断模板是否在线（基于网络状态）
const isTemplateOnline = () => {
  return isOnline.value;
};

// 获取设备类型显示名称
const getDeviceTypeName = (type) => {
  const typeMap = {
    waterPump: '水泵',
    waterCannon: '水炮',
    fogCannon: '雾炮'
  };
  return typeMap[type] || type;
};

// 获取设备类型颜色
const getDeviceTypeColor = (type) => {
  const colorMap = {
    waterPump: '#3498DB',
    waterCannon: '#2ECC71',
    fogCannon: '#F39C12'
  };
  return colorMap[type] || '#95A5A6';
};

// 控制按钮点击动画
const animateControlButton = (template) => {
  const templateCard = document.querySelector(`.template-card-${template.id}`);
  if (templateCard) {
    // 控制按钮动画
    const controlBtn = templateCard.querySelector('.control-btn');
    if (controlBtn) {
      gsap.to(controlBtn, {
        scale: 0.9,
        duration: 0.1,
        ease: "power2.out",
        yoyo: true,
        repeat: 1,
        clearProps: "all"
      });
    }
  }
};

// 初始化模板控制状态
const initTemplateControlling = () => {
  templates.value.forEach(template => {
    templateControlling.value[template.id] = false;
  });
};

onMounted(() => {
  // 初始化模板控制状态
  initTemplateControlling();
  
  // 初始化WebSocket连接
  initWebSocket();
  
  // 卡片入场动画
  const cards = document.querySelectorAll('.template-card');
  gsap.from(cards, {
    y: 30,
    opacity: 0,
    duration: 0.6,
    stagger: 0.1,
    ease: "power2.out",
    clearProps: "transform,opacity"
  });

  // 信息入场动画
  gsap.from('.template-info', {
    opacity: 0,
    x: -20,
    duration: 0.5,
    stagger: 0.1,
    delay: 0.3,
    clearProps: "all"
  });
});
</script>

<template>
  <div class="template-container">
    <a-row :gutter="[16, 24]">
      <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="6" v-for="template in templates" :key="template.id">
        <a-card
          :title="template.name"
          :bordered="false"
          :class="['template-card', `template-card-${template.id}`, {'controlling': isTemplateControlling(template), 'offline': !isTemplateOnline()}]"
          :headStyle="{color: '#FFFFFF', backgroundColor: '#0D275A', borderBottom: 'none'}"
        >
          <!-- <template #extra>
            <a-tag :color="isTemplateControlling(template) ? '#F39C12' : '#95A5A6'" class="status-tag">
              {{ isTemplateControlling(template) ? '控制中' : '就绪' }}
            </a-tag>
          </template> -->
          <div style="font-size:13px;line-height:1.6;" class="template-info">
            <div class="info-row">
              <span class="info-label">模板设备：</span>
              <div class="devices-container">
                <a-tag 
                  v-for="device in template.devices" 
                  :key="device.ip"
                  :color="getDeviceTypeColor(device.type)"
                  size="small"
                  class="device-tag"
                >
                  {{ getDeviceTypeName(device.type) }}: {{ device.name }}
                </a-tag>
              </div>
            </div>
            <div class="info-row">
              <span class="info-label">设备数量：</span>
              <span class="device-count">{{ template.devices.length }} 台</span>
            </div>
            <div class="info-row">
              <span class="info-label">模板控制：</span>
              <div class="control-status">
                <a-button
                  type="primary"
                  size="small"
                  @click="startTemplate(template); animateControlButton(template)"
                  :disabled="!isTemplateOnline() || isTemplateControlling(template)"
                  :loading="isTemplateControlling(template)"
                  class="control-button start-btn"
                  style="margin-right: 8px;"
                >
                  <PlayCircleOutlined />
                  启动模板
                </a-button>
                <a-button
                  danger
                  size="small"
                  @click="stopTemplate(template); animateControlButton(template)"
                  :disabled="!isTemplateOnline() || isTemplateControlling(template)"
                  :loading="isTemplateControlling(template)"
                  class="control-button stop-btn"
                >
                  <StopOutlined />
                  停止模板
                </a-button>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<style scoped>
/* 网络状态样式 */
.network-status {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  margin-right: 8px;
  transition: all 0.3s;
}

.network-status .status-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 4px;
}

.network-status.online {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.network-status.online .status-dot {
  background-color: #2ecc71;
  box-shadow: 0 0 6px #2ecc71;
}

.network-status.offline {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.network-status.offline .status-dot {
  background-color: #e74c3c;
  box-shadow: 0 0 6px #e74c3c;
}

/* 卡片悬停效果 */
.template-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.5s ease;
}

.template-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 20px rgba(0,0,0,0.4);
}

.template-card.controlling {
  border-left: 4px solid #F39C12;
  box-shadow: 0 4px 12px rgba(243, 156, 18, 0.15);
}

.template-card.controlling .ant-card-head {
  background: linear-gradient(135deg, #E67E22 0%, #F39C12 100%);
}

/* 状态标签动画 */
.status-tag {
  transition: all 0.3s ease;
}

.template-container {
  width: 100%;
}

.template-card {
  background: #0D275A;
  box-shadow: 0 8px 16px rgba(0,0,0,0.3);
  border-radius: 12px;
  margin: 0;
  color: #FFFFFF;
  border: none;
  height: auto;
  min-height: 280px;
  display: flex;
  flex-direction: column;
  transform: none !important;
  will-change: auto;
}

.info-row {
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
}

.info-label {
  width: 80px;
  text-align: right;
  padding-right: 10px;
  color: rgba(255, 255, 255, 0.8);
  flex-shrink: 0;
}

.devices-container {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-left: 8px;
}

.device-tag {
  margin-bottom: 4px;
  font-size: 11px;
  border-radius: 4px;
}

.device-count {
  font-weight: bold;
  color: #3498DB;
  margin-left: 8px;
}

.control-status {
  flex: 1;
  margin-left: 8px;
}

.control-button {
  transition: all 0.3s ease;
  border-radius: 6px;
  font-weight: 500;
}

.control-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.start-btn {
  background-color: #27AE60;
  border-color: #27AE60;
}

.start-btn:hover {
  background-color: #2ECC71;
  border-color: #2ECC71;
}

.stop-btn:hover {
  background-color: #C0392B;
  border-color: #C0392B;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .template-card {
    margin-bottom: 4px;
    padding: 4px 0;
  }
  
  .info-label {
    width: 70px;
    font-size: 12px;
  }
  
  .devices-container {
    margin-left: 4px;
  }
  
  .device-tag {
    font-size: 10px;
  }
  
  .a-row {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  
  .a-col {
    padding-left: 2px !important;
    padding-right: 2px !important;
  }
}
</style>