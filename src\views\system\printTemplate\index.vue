<template>
  <div class="app-container">

    <!-- 搜索过滤条件 -->
    <searchForm @change="searchFormChange" :searchList.sync="queryList" :keys="url" @scopedSlotsEmit="scopedSlotsEmit"
      @getList="getList" :dicts="dicts" :formItemNum="formItemNum" :defaultQueryKeys="defaultQueryKeys">
      <template #left>
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['system:printTemplate:add']">新增</el-button>
        <el-button type="success" plain icon="el-icon-edit" append :disabled="single" size="mini" @click="handleUpdate"
          v-hasPermi="['system:printTemplate:edit']">修改</el-button>
        <el-button type="danger" plain icon="el-icon-delete" append size="mini" :disabled="multiple"
          @click="handleDelete" v-hasPermi="['system:printTemplate:remove']">删除</el-button>
        <el-dropdown trigger="click" style="margin: 0 10px;">
          <el-button type="warning" plain icon="el-icon-download" size="mini" class="el-dropdown-link"
            v-hasPermi="['system:printTemplate:export']">导出</el-button>
          <el-dropdown-menu slot="dropdown" class="dropdownMenu">
            <el-dropdown-item class="clearfix">
              <div @click="handleExport">全部导出</div>
            </el-dropdown-item>
            <el-dropdown-item class="clearfix">
              <div @click="handleExportForIds">选中导出</div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template #right>

      </template>
    </searchForm>

    <!-- 表格 -->
    <defineTable v-loading="loading" :dicts="dicts" :tableKeysList="queryList" :parentName="$options.name"
      @selection-change="handleSelectionChange" :tableDataList.sync="tableDataList" :config="tableConfig">
    </defineTable>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改打印模板对话框 -->
    <el-dialog v-if="open" :title="title" :visible.sync="open" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" :inline="true">
        <el-form-item label="模板内容">
          <editor v-model="form.content" :min-height="192" />
        </el-form-item>
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="loading">确 定</el-button>
        <el-button @click="cancel" :loading="loading">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import pageMixins from "@/components/A_custom/mixins/page.js"
import rightOptionMixins from "@/components/A_custom/mixins/rightOption.js"
import { listPrintTemplateGet, listPrintTemplatePost, getPrintTemplate, delPrintTemplate, addPrintTemplate, updatePrintTemplate } from "@/api/system/printTemplate";

export default {
  mixins: [pageMixins, rightOptionMixins],
  name: "PrintTemplate",
  data() {
    return {
      // ——————————————————————————————————————————————————————————————————————————————
      // 默认查询参数
      defaultQueryKeys: {

      },
      //页面进来调接口的默认参数
      tableListApi: listPrintTemplatePost,
      url: "system/printTemplate",
      formItemNum: 4, //默认搜索展示多少个条件
      tableConfig: {
        butStatus: {
          "修改": (row = {}) => { return false },
          "删除": (row = {}) => { return false },
        },
        rowKey: "id",
        // checkbox:true, //选择多行数据
        // selection:true,     //是否需要选择
        // amountToJudge:false, //需要合计
        // align:"center", //文字位置
        // border:true, //边框线
        // textOverflow:false, //文字超出是否隐藏
        // checkboxDisable: (row) => { return true;}        // 判断行是否允许选择
      },
      // ——————————————————————————————————————————————————————————————————————————————
      // 打印模板表格数据
      queryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {
        id: null,
        content: null,
        name: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      },
      // 表单校验
      rules: {
      }
    };
  },
  methods: {
    // 操作事件
    commandDropdownClick(key, row) {
      switch (key) {
        case "修改": this.handleUpdate(row); break;
        case "删除": this.handleDelete(row); break;
      }
    },
    // 选中列表返回事件
    selectionReturnChange(selection) {
      /* 不能重名；ids,single,multiple,selectedList */
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        content: null,
        name: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push({path: '/test/test', query: {id: this.form.id}});
      return;
      this.reset();
      this.open = true;
      this.title = "添加打印模板";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id ? [row.id] : this.ids;
      this.$router.push({path: '/test/test', query: {id: id}});
      return
      getPrintTemplate(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改打印模板";
      });
    },
    /** 提交按钮 */
    submitForm() {
      console.log(this.form, 'chakan 数据啊');
      return;
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updatePrintTemplate(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPrintTemplate(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id ? [row.id] : this.ids;
      let name = this.deleteName(ids);
      this.$modal.confirm('是否确认删除打印模板编号为"' + name + '"的数据项？').then(function () {
        return delPrintTemplate(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    //删除行的提示名
    deleteName(ids) {
      let resultMap = {};
      this.tableDataList.map((item, index) => {
        resultMap[item.id] = item;
      });
      return ids.map(id => resultMap[id].id).join(',')
    },
    /** 导出按钮操作 */
    handleExport() {
      this.queryParams.columnList = JSON.stringify(this.queryList.filter(x => { return (x.value && x.isDefault && x.selectable) }))
      this.download('system/printTemplate/export', {
        ...this.queryParams
      }, `printTemplate_${new Date().getTime()}.xlsx`)
    },
    /** 导出按钮操作--根据选中的id */
    handleExportForIds() {
      let ids = this.selectedList.map(item => item.id).join(',')
      if (ids) {
        this.download('system/demo/exportbyid', { ids }, `demo_${new Date().getTime()}.xlsx`)
      } else {
        this.$message.warning(`请选择需要导出的数据`);
        return
      }
    },
  }
};
</script>
