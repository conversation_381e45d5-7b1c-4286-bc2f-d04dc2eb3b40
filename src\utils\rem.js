/**
 * REM适配方案
 * 基础设计尺寸为750px
 * 1rem = 100px
 */

// 设计稿基准宽度
const DESIGN_WIDTH = 750;
// 最大宽度（超过这个宽度不再放大）
const MAX_WIDTH = 750;
// 最小宽度（低于这个宽度不再缩小）
const MIN_WIDTH = 320;
// 基准字体大小（1rem = 100px）
const BASE_FONT_SIZE = 100;

/**
 * 设置 rem 基准值
 */
function setRem() {
  // 当前设备宽度
  let clientWidth = document.documentElement.clientWidth || document.body.clientWidth;
  
  // 限制最大最小宽度
  clientWidth = Math.max(MIN_WIDTH, Math.min(MAX_WIDTH, clientWidth));
  
  // 计算缩放比例
  const scale = clientWidth / DESIGN_WIDTH;
  
  // 设置 html 元素的字体大小
  document.documentElement.style.fontSize = BASE_FONT_SIZE * scale + 'px';
}

// 初始化
setRem();

// 窗口大小变化时重新计算
window.addEventListener('resize', setRem);
window.addEventListener('pageshow', function(e) {
  if (e.persisted) {
    setRem();
  }
});

export default {
  DESIGN_WIDTH,
  BASE_FONT_SIZE
};
