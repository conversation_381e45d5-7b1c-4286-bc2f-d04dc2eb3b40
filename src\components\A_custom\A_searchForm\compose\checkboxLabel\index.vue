<template>
    <div class="checkboxLabel" >
        <el-checkbox v-model="values" @input="valueInput" />
        <div v-if="disabled" class="checkboxLabelBox" @click="valueInput(!values)" >{{label}}</div>
        <el-select class="schemeQueryInput" size="mini" v-else  v-model="value" placeholder="请选择">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
    </div>
</template>

<script>
    export default {
        provide(){ return {  deep:()=>{return this.deep } }  },
        data(){
            return{
                values:this.value
            }
        },
        props:{
            disabled:{
                type:<PERSON>olean,
                default:()=>{return false}
            },
            label:{
                type:String,
                default:()=>{return ""}
            },
            value:{
                type:<PERSON>olean,
                default:()=>{return false}
            },
        },
        methods:{
            valueInput(val){
                this.values = val;
                this.$emit('input',val);
            },
            inputClean(){
                this.values = false;
                this.$emit('input',false);
            }
        },
    }
</script>

<style lang="scss" scoped>
.checkboxLabel{
    display:flex;
    flex-direction:row;
    align-items:center;
    .checkboxLabelBox{
        width:150px;
        font-size: 12px;
        margin-left:5px;
        border:1px solid #dcdfe6;
        padding:0 10px;
        border-radius:4px;
        background:#ffffff;
        color:#1b2944;
        height:28px;
        text-overflow: ellipsis;
		white-space: nowrap;
		overflow: hidden;
        cursor: pointer;
    }
}
</style>