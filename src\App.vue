<script setup>
import { ref, onMounted, provide } from 'vue';
import 'animate.css';
import gsap from 'gsap';
import FogCannonControl from './components/FogCannonControl.vue';
import WaterCannonControl from './components/WaterCannonControl.vue';
import WaterPumpControl from './components/WaterPumpControl.vue';
import PlanTemplate from './components/PlanTemplate.vue';
import { request } from './utils';

const activeTab = ref('fogCannon');
const isMobile = ref(false);

// 设备数据
const deviceData = ref({
  fogCannons: [], // 雾炮机 type: 1
  waterCannons: [], // 水炮机 type: 2
  waterPumps: [] // 水泵 type: 3
});

// 环境数据
const environmentData = ref({
  temperature: '33°C',
  humidity: '89%',
  windDirection: '东南',
  windSpeed: '2级'
});

// 当前时间
const currentDateTime = ref('');

const updateDateTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');

  currentDateTime.value = `${year}年${month}月${day}日 ${hours}:${minutes}`;
};

// 提供GSAP给子组件使用
provide('gsap', gsap);

// 获取设备列表并分组
const fetchDeviceList = async () => {
  try {
    const res = await request.get("/system/device/list");
    let devices = res.rows || [];
    devices = [
      {
            "createBy": "admin",
            "createTime": "2025-05-09 17:51:22",
            "updateBy": "admin",
            "updateTime": "2025-07-29 11:39:44",
            "remark": null,
            "id": 1,
            "name": "1号雾炮机",
            "status": "2",
            "onlineTime": null,
            "loginTime": null,
            "type": "2",
            "ip": "*************"
        },
        {
            "createBy": "admin",
            "createTime": "2025-05-09 17:51:22",
            "updateBy": "admin",
            "updateTime": "2025-07-29 11:39:44",
            "remark": null,
            "id": 2,
            "name": "2号雾炮机",
            "status": "2",
            "onlineTime": null,
            "loginTime": null,
            "type": "2",
            "ip": "*************"
        },
        {
            "createBy": "admin",
            "createTime": "2025-05-09 17:51:22",
            "updateBy": "admin",
            "updateTime": "2025-07-29 11:39:44",
            "remark": null,
            "id": 3,
            "name": "3号雾炮机",
            "status": "2",
            "onlineTime": null,
            "loginTime": null,
            "type": "2",
            "ip": "*************"
        },
        {
            "createBy": "admin",
            "createTime": "2025-05-09 17:51:22",
            "updateBy": "admin",
            "updateTime": "2025-07-29 11:39:44",
            "remark": null,
            "id": 4,
            "name": "4号雾炮机",
            "status": "2",
            "onlineTime": null,
            "loginTime": null,
            "type": "2",
            "ip": "*************"
        },
        {
            "createBy": "admin",
            "createTime": "2025-05-09 17:51:22",
            "updateBy": "admin",
            "updateTime": "2025-07-29 11:39:44",
            "remark": null,
            "id": 5,
            "name": "5号雾炮机",
            "status": "2",
            "onlineTime": null,
            "loginTime": null,
            "type": "2",
            "ip": "*************"
        },
        {
            "createBy": "admin",
            "createTime": "2025-05-09 17:51:22",
            "updateBy": "admin",
            "updateTime": "2025-07-29 11:39:44",
            "remark": null,
            "id": 6,
            "name": "6号雾炮机",
            "status": "2",
            "onlineTime": null,
            "loginTime": null,
            "type": "2",
            "ip": "*************"
        },
        {
            "createBy": "admin",
            "createTime": "2025-05-09 17:51:22",
            "updateBy": "admin",
            "updateTime": "2025-07-29 11:39:44",
            "remark": null,
            "id": 7,
            "name": "7号雾炮机",
            "status": "2",
            "onlineTime": null,
            "loginTime": null,
            "type": "2",
            "ip": "*************"
        },
        {
            "createBy": "admin",
            "createTime": "2025-05-09 17:51:22",
            "updateBy": "admin",
            "updateTime": "2025-07-29 11:39:44",
            "remark": null,
            "id": 8,
            "name": "8号雾炮机",
            "status": "2",
            "onlineTime": null,
            "loginTime": null,
            "type": "2",
            "ip": "*************"
        },
        {
            "createBy": "admin",
            "createTime": "2025-05-09 17:51:22",
            "updateBy": "admin",
            "updateTime": "2025-07-29 11:39:44",
            "remark": null,
            "id": 9,
            "name": "9号雾炮机",
            "status": "2",
            "onlineTime": null,
            "loginTime": null,
            "type": "2",
            "ip": "*************"
        },
        {
            "createBy": "admin",
            "createTime": "2025-05-09 17:51:22",
            "updateBy": "admin",
            "updateTime": "2025-07-29 11:39:44",
            "remark": null,
            "id": 3,
            "name": "水泵1",
            "status": "2",
            "onlineTime": null,
            "loginTime": null,
            "type": "3",
            "ip": "*************"
        },
        {
            "createBy": "admin",
            "createTime": "2025-05-09 17:51:22",
            "updateBy": "admin",
            "updateTime": "2025-07-29 11:39:44",
            "remark": null,
            "id": 4,
            "name": "水泵2",
            "status": "2",
            "onlineTime": null,
            "loginTime": null,
            "type": "3",
            "ip": "*************"
        }
    ]
    
    // 按设备类型分组
    deviceData.value.fogCannons = devices.filter(device => device.type == '2');
    deviceData.value.waterCannons = devices.filter(device => device.type == '1');
    deviceData.value.waterPumps = devices.filter(device => device.type == '3');
    
    console.log('设备数据分组:', deviceData.value);
  } catch (err) {
    console.error('获取设备列表失败:', err);
  }
};

onMounted(() => {
  fetchDeviceList();
  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);

  // 初始化时间并每分钟更新
  updateDateTime();
  setInterval(updateDateTime, 60000);

  // 页面加载动画
  const timeline = gsap.timeline({
    defaults: { duration: 0.6, ease: "power2.out" }
  });

  timeline
    .from('.header', { y: -30, opacity: 0, clearProps: "all" })
    .from('.content', { opacity: 0, clearProps: "all" }, '-=0.3')
    .from('.footer', { y: 20, opacity: 0, clearProps: "all" }, '-=0.3');
});

const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768;
};

const handleTabChange = (key) => {
  activeTab.value = key;
};
</script>

<template>
  <router-view v-if="$route.path === '/biscreen'" />
  <div v-else class="app-container" :class="{ 'mobile': isMobile }">
    <a-layout class="main-layout">
      <!-- PC端顶部导航 -->
      <a-layout-header class="header">
        <div class="logo">淋雨广场控制平台</div>
        <a-menu
          v-if="!isMobile"
          mode="horizontal"
          :selectedKeys="[activeTab]"
          @select="({ key }) => handleTabChange(key)"
          theme="dark"
          style="background-color: #0D274A; border-bottom: none;flex:1;"
        >
          <a-menu-item key="planTemplate" style="color: #FFFFFF;">工程模板</a-menu-item>
          <a-menu-item key="fogCannon" style="color: #FFFFFF;">雾炮机监控</a-menu-item>
          <a-menu-item key="waterCannon" style="color: #FFFFFF;">水炮机监控</a-menu-item>
          <a-menu-item key="waterPump" style="color: #FFFFFF;">水泵监控</a-menu-item>
        </a-menu>

        <!-- PC端环境信息 -->
        <div v-if="!isMobile" class="header-right">
          <a-button 
            type="primary" 
            danger 
            size="large" 
            class="emergency-stop"
            @click="handleEmergencyStop"
          >
            <!-- <template #icon></template> -->
            全局急停
          </a-button>
          <div class="weather-info">
            <div class="weather-item">温度: {{ environmentData.temperature }}</div>
            <div class="weather-item">湿度: {{ environmentData.humidity }}</div>
            <div class="weather-item">风向: {{ environmentData.windDirection }}</div>
            <div class="weather-item">风速: {{ environmentData.windSpeed }}</div>
            <div class="datetime">{{ currentDateTime }}</div>
          </div>
        </div>
      </a-layout-header>

      <a-layout-content class="content">
        <!-- 移动端环境信息 -->
        <div v-if="isMobile" class="mobile-weather-info">
          <div class="weather-row">
            <div class="weather-item">温度: {{ environmentData.temperature }}</div>
            <div class="weather-item">湿度: {{ environmentData.humidity }}</div>
          </div>
          <div class="weather-row">
            <div class="weather-item">风向: {{ environmentData.windDirection }}</div>
            <div class="weather-item">风速: {{ environmentData.windSpeed }}</div>
          </div>
        </div>

        <a-tabs
          v-if="isMobile"
          v-model:activeKey="activeTab"
          @change="handleTabChange"
          class="mobile-tabs"
          :centered="true"
        >
          <a-tab-pane key="planTemplate" tab="工程模板"></a-tab-pane>
          <a-tab-pane key="fogCannon" tab="雾炮机监控"></a-tab-pane>
          <a-tab-pane key="waterCannon" tab="水炮机监控"></a-tab-pane>
          <a-tab-pane key="waterPump" tab="水泵监控"></a-tab-pane>
        </a-tabs>

        <div class="control-container">
          <transition name="fade-slide" mode="out-in">
            <component
              :is="activeTab === 'planTemplate' ? PlanTemplate :
              activeTab === 'fogCannon' ? FogCannonControl :
                   activeTab === 'waterCannon' ? WaterCannonControl :
                   WaterPumpControl"
              :key="activeTab"
              :devices="activeTab === 'fogCannon' ? deviceData.fogCannons :
                       activeTab === 'waterCannon' ? deviceData.waterCannons :
                       deviceData.waterPumps"
            />
          </transition>
        </div>
      </a-layout-content>

      <!-- <a-layout-footer class="footer"> -->
        <!-- 淋雨广场控制平台 &copy; 2025 -->
      <!-- </a-layout-footer> -->
    </a-layout>
  </div>
</template>

<style>
/* 全局过渡效果 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.4s ease;
}
.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(20px);
}
.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}
.fade-slide-enter-to,
.fade-slide-leave-from {
  transform: translateX(0);
  opacity: 1;
}

/* 水波纹效果 */
@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1);
  transform-origin: 50% 50%;
  pointer-events: none;
}

.ripple-effect:active::after {
  animation: ripple 0.6s ease-out;
}

.app-container {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.main-layout {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  align-items: center;
  padding: 0 20px;
  justify-content: space-between;
  background-color: #0D274A;
}

.logo {
  color: #FFFFFF;
  font-size: 20px;
  font-weight: bold;
  margin-right: 30px;
  white-space: nowrap;
}

/* PC端环境信息样式 */
.weather-info {
  display: flex;
  align-items: center;
  margin-left: auto;
  background-color: #0D274A;
  padding: 0 15px;
  height: 100%;
}

.weather-item {
  color: #FFFFFF;
  margin-left: 15px;
  font-size: 14px;
  white-space: nowrap;
}

.datetime {
  color: #FFFFFF;
  margin-left: 15px;
  font-size: 14px;
  white-space: nowrap;
}

.content {
  flex: 1 1 auto;
  padding: 0;
  background: #0D274A;
  min-height: 0;
  overflow-x: hidden;
  height: auto;
}

.control-container {
  background: #0D274A;
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

/* 隐藏滚动条 */
.control-container::-webkit-scrollbar {
  display: none;
}

/* 确保卡片高度一致 */
.control-container .ant-row {
  display: flex;
  flex-wrap: wrap;
}

.control-container .ant-col {
  display: flex;
  margin-bottom: 16px;
  height: auto !important;
  transform: none !important; /* 防止transform属性影响布局 */
}

.control-container .ant-card {
  width: 100%;
  flex: 1;
  transform: none !important; /* 防止transform属性影响布局 */
}

/* 卡片内容样式 */
.control-container .ant-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 确保所有卡片高度一致 */
.control-container .ant-card-head {
  min-height: 48px;
}

/* 确保所有卡片在动画后正确对齐 */
.control-container .ant-row > .ant-col {
  transform: none !important;
}

/* 防止动画残留影响布局 */
.control-container .ant-card,
.control-container .cannon-card,
.control-container .pump-card {
  transform: none !important;
  transition: background-color 0.5s ease, box-shadow 0.3s ease !important;
}

.footer {
  text-align: center;
  padding: 15px;
  background: #0D274A;
  color: #FFFFFF;
  width: 100vw;
  left: 0;
  bottom: 0;
  position: relative;
}

/* 移动端样式 */
.mobile .header {
  padding: 0 10px;
  flex-direction: column;
  height: auto;
  line-height: normal;
  padding: 10px;
}

.mobile .logo {
  margin-right: 0;
}

.mobile .content {
  padding: 0;
  height: calc(100vh - 64px - 48px);
  display: flex;
  flex-direction: column;
}

/* 移动端环境信息样式 */
.mobile-weather-info {
  background-color: #0D274A;
  padding: 8px 10px;
  color: #FFFFFF;
  border-bottom: 1px solid #3498DB;
}

.mobile-weather-info .weather-row {
  display: flex;
  justify-content: center;
  margin-bottom: 4px;
}

.mobile-weather-info .weather-item {
  color: #FFFFFF;
  font-size: 12px;
  white-space: nowrap;
}

.mobile .control-container {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
  background-color: #0D274A;
  border-top: none;
}

.mobile .control-container .ant-col {
  margin-bottom: 16px;
}

/* 移动端Tab样式 */
.mobile-tabs {
  width: 100%;
  margin-bottom: 0;
  background-color: #0D274A; /* 深蓝色背景 */
}

.mobile-tabs .ant-tabs-nav {
  width: 100%;
  margin-bottom: 0;
  background-color: #0D274A; /* 深蓝色背景 */
  border: none;
}

.mobile-tabs .ant-tabs-nav::before {
  display: none; /* 移除底部边框 */
}

.mobile-tabs .ant-tabs-nav-list {
  width: 100%;
  display: flex;
}

.mobile-tabs .ant-tabs-tab {
  flex: 1;
  display: flex;
  justify-content: center;
  margin: 0 !important;
  padding: 12px 0;
  transition: all 0.3s ease;
  background-color: #0D274A; /* 深蓝色背景 */
  border: none;
  border-radius: 0;
}

.mobile-tabs .ant-tabs-tab-active {
  background-color: #3498DB; /* 亮蓝色背景 */
  box-shadow: none;
}

.mobile-tabs .ant-tabs-tab-btn {
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: #FFFFFF; /* 白色文本 */
  padding: 0 8px;
}

.mobile-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #FFFFFF; /* 白色文本 */
}

.mobile-tabs .ant-tabs-ink-bar {
  display: none; /* 隐藏底部指示条，因为我们使用背景色来表示选中状态 */
}
.header-right{
  display: flex;
}
.emergency-stop{
  margin-top: auto;
  margin-bottom: auto;
}
</style>
