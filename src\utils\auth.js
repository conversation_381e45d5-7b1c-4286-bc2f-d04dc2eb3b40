import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'

const VersionName = 'systemVersionNumber'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(Token<PERSON>ey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function getVersion() {
  return localStorage.getItem(VersionName)
}

/**
 * 
 * @param {*} Version 
 * @returns 
 */
export function setVersion(Version) {
  return localStorage.setItem(VersionName, Version)
}

export function removeVersion() {
  return localStorage.removeItem(VersionName)
}
