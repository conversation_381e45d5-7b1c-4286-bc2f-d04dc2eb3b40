import request from '@/utils/request'
import {tansParams} from '@/utils/ruoyi.js'

// 查询人才主档列表
export function listFile(paging, data) {
  return request({
    url: '/master/file/list?' + tansParams(paging),
    method: 'post',
    data: data
  })
}

// 查询人才主档详细
export function getFile(id) {
  return request({
    url: '/master/file/' + id,
    method: 'get'
  })
}

// 新增人才主档
export function addFile(data) {
  return request({
    url: '/master/file',
    method: 'post',
    data: data
  })
}

// 修改人才主档
export function updateFile(data) {
  return request({
    url: '/master/file',
    method: 'put',
    data: data
  })
}

// 删除人才主档
export function delFile(id) {
  return request({
    url: '/master/file/' + id,
    method: 'delete'
  })
}
