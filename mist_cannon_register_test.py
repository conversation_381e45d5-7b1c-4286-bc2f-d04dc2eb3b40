# mist_cannon_register_test.py
# -*- coding: utf-8 -*-
"""
雾炮机寄存器读取测试脚本
功能: 连接雾炮机************，读取寄存器40024-40044的数据
依赖: pymodbus (请先通过 pip install pymodbus 安装)
"""

import time
import struct
from pymodbus.client import ModbusTcpClient
from pymodbus.exceptions import ModbusException

class MistCannonTester:
    """雾炮机寄存器测试器"""
    
    def __init__(self, host="***********", port=502, unit_id=1):
        self.host = host
        self.port = port
        self.unit_id = unit_id
        self.client = ModbusTcpClient(host, port=port)
        print(f"--- 初始化雾炮机测试器: {host}:{port} ---")
    
    def connect(self):
        """连接到雾炮机"""
        try:
            if self.client.connect():
                print(f"✓ 成功连接到雾炮机 {self.host}")
                return True
            else:
                print(f"✗ 无法连接到雾炮机 {self.host}")
                return False
        except Exception as e:
            print(f"✗ 连接异常: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        self.client.close()
        print("--- 已断开连接 ---")
    
    def read_int32(self, address):
        """读取32位有符号整数"""
        try:
            rr = self.client.read_holding_registers(address=address, count=2, slave=self.unit_id)
            if rr.isError():
                return None
            # 使用新的API替代已弃用的BinaryPayloadDecoder
            # 手动解析32位有符号整数 (大端序)
            if len(rr.registers) >= 2:
                high_word = rr.registers[0]
                low_word = rr.registers[1]
                # 组合为32位整数
                value = (high_word << 16) | low_word
                # 转换为有符号整数
                if value >= 0x80000000:
                    value -= 0x100000000
                return value
            return None
        except ModbusException:
            return None
    
    def read_uint16(self, address):
        """读取16位无符号整数"""
        try:
            rr = self.client.read_holding_registers(address=address, count=1, slave=self.unit_id)
            if rr.isError():
                return None
            return rr.registers[0]
        except ModbusException:
            return None
    
    def read_float(self, address):
        """读取32位浮点数"""
        try:
            rr = self.client.read_holding_registers(address=address, count=2, slave=self.unit_id)
            if rr.isError():
                return None
            # 使用struct手动解析32位浮点数 (大端序)
            if len(rr.registers) >= 2:
                high_word = rr.registers[0]
                low_word = rr.registers[1]
                # 组合为32位数据并解析为浮点数
                packed_data = struct.pack('>HH', high_word, low_word)
                return struct.unpack('>f', packed_data)[0]
            return None
        except ModbusException:
            return None
    
    def read_registers_range(self, start_addr, end_addr):
        """读取指定范围的寄存器"""
        print(f"\n--- 读取寄存器范围 {start_addr}-{end_addr} ---")
        
        for addr in range(start_addr, end_addr + 1):
            # 尝试不同的数据类型读取
            print(f"\n寄存器 {addr}:")
            
            # 读取为16位无符号整数
            uint16_val = self.read_uint16(addr)
            if uint16_val is not None:
                print(f"  UINT16: {uint16_val}")
                print(f"  UINT16 (HEX): 0x{uint16_val:04X}")
                print(f"  UINT16 (BIN): {bin(uint16_val)}")
            
            # 如果地址是偶数，尝试读取为32位数据
            if addr % 2 == 0 and addr + 1 <= end_addr:
                # 读取为32位有符号整数
                int32_val = self.read_int32(addr)
                if int32_val is not None:
                    print(f"  INT32: {int32_val}")
                    print(f"  INT32 (HEX): 0x{int32_val:08X}")
                
                # 读取为32位浮点数
                float_val = self.read_float(addr)
                if float_val is not None:
                    print(f"  FLOAT: {float_val:.6f}")
    
    def continuous_monitor(self, start_addr, end_addr, interval=2):
        """连续监控寄存器变化"""
        print(f"\n--- 开始连续监控寄存器 {start_addr}-{end_addr} (间隔{interval}秒) ---")
        print("按 Ctrl+C 停止监控")
        
        try:
            while True:
                print(f"\n[{time.strftime('%H:%M:%S')}] 寄存器状态:")
                
                for addr in range(start_addr, end_addr + 1):
                    uint16_val = self.read_uint16(addr)
                    if uint16_val is not None:
                        print(f"  {addr}: {uint16_val} (0x{uint16_val:04X})")
                    else:
                        print(f"  {addr}: 读取失败")
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n--- 停止监控 ---")

def main():
    """主函数 - 直接执行模式"""
    print("\n===============================================")
    print("     雾炮机寄存器读取测试脚本")
    print("===============================================")
    
    # 创建测试器实例
    tester = MistCannonTester()
    
    # 连接到雾炮机
    if not tester.connect():
        print("连接失败，程序退出")
        return
    
    try:
        print(f"连接设备: {tester.host}:{tester.port}")
        print(f"从站ID: {tester.unit_id}")
        print("\n开始读取寄存器40016-40017的数据...\n")
        
        # 直接读取寄存器范围40016-40017
        tester.read_registers_range(40016, 40017)
        
        print("\n=== 数据读取完成 ===")
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
    finally:
        tester.disconnect()
        print("程序结束")

if __name__ == "__main__":
    main()