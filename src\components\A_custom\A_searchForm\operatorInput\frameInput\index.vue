<template>
    <div>
        <el-input :disabled="config.disabled" clearable @clear="clearValue" class="schemeQueryInput" :size="size" :placeholder="placeholder" @input="valueInput" :value="inputPlayUp">
            <el-button :disabled="config.disabled" slot="append" :size="size" icon="el-icon-search" @click="openEvent" ></el-button>
        </el-input>
        <baseFrame ref="baseFrame" :show.sync="show" :config="{...config,...tableConfig,other}" @frameSalesmanRetureEvent="frameSalesmanRetureEvent" >
            <template  v-for="item,index in scopedSlots" #[item]="{row}">
                <slot :name="item" :row="row" ></slot>
            </template>
        </baseFrame>
    </div>
</template>

<script>
import {inputMixin} from "../mixins/index.js"
import baseFrame from "./baseFrame/index.vue"
    export default {
        components:{
            baseFrame
        },
        props:{
            otherConfig:{
                type:[Object],
                default:()=>{
                    return {}
                }
            },
        },
        mixins:[inputMixin],
        data(){
            return {
                show:false,//弹框打开
                queryList:[],
                tableConfig:{},
                selectedTableList:[],//列表选中的值
                mainValue:null,// 核心，根据单选多选判断 值是否为数组 不定义否则会报错
                judgeShow:true,
                other:false, // 判断是否引用单个组件
                scopedSlots:[],//插槽
            }
        },
        watch:{
            value:{
                handler(){
                    if(this.value == undefined || !this.value || this.value == null){
                        this.mainValue = undefined;
                    }else{
                        this.mainValue = this.value;
                    }
                },
                immediate: true, // 马上监听触发
            },
        },
        created(){
            this.$nextTick(()=>{
                this.scopedSlots = Object.keys(this.$scopedSlots)
            })
        },
        mounted(){
            this.judgeShow && this.oneJudgeShowEvent();
        },
        computed:{
            inputPlayUp(){
                if(this.mainValue && this.selectedTableList.length == 0){return this.value}
                if(this.mainValue && this.selectedTableList.length > 0){
                    if(!this.tableConfig?.echo){ return undefined }
                    let str = "";
                    let keys = this.tableConfig.keys;
                    if(Array.isArray(this.mainValue)){
                        this.selectedTableList.forEach((x,i)=>{
                            if(this.selectedTableList.length == i+1){
                                str += x[keys];
                            }else{
                                str += x[keys]+',';
                            }
                        })
                    }else{
                        // let obj = this.selectedTableList.find(x=>{
                        //     return x[keys] == this.mainValue
                        // });
                        // obj && (str = obj[keys]);
                        str = this.mainValue
                    }
                    return str.replace(/^\s*|\s*$/g,"").toUpperCase()
                }
            }
        },
        methods:{
            clearValue(){
                this.$refs.baseFrame.toggleSelectionAll()
            },
            oneJudgeShowEvent(){
                this.$nextTick(()=>{
                    //获取 页面的配置
                    let pageData
                    try{
                        pageData = require(`@/views/${this.config.data.reflection}/config.js`).pageData
                    }catch(err){
                        pageData = this.otherConfig;
                        this.other = true;
                    }
                    this.tableConfig = {...this.$refs.baseFrame.configDefault,...pageData}
                    this.judgeShow = false;
                })
            },
            valueInput(e){
                this.$emit('input', e);
            },
            openEvent(e){
                this.show = true;
            },
            frameSalesmanRetureEvent(list){
                // 获取相对应的keys 返回到输入框中
                this.selectedTableList = list;
                let {keys} = this.tableConfig;
                let {multiple} = this.config
                let newList = list.map(x=>{ return x[keys]});        
                if(newList.length > 0){
                    if(multiple){
                        this.$emit('input',newList.join(","));
                        this.$emit('change',list);
                    }else{
                        this.$emit('change',list[0]);
                        this.$emit('input',newList[0]);
                    }
                }
            }
        },
    }
</script>

<style lang="scss" scoped>

</style>