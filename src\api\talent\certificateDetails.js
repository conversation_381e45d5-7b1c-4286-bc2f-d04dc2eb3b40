import request from '@/utils/request'
import {tansParams} from '@/utils/ruoyi.js'

// 查询证书关联人才采购列表
export function listCertificate(paging, data) {
  return request({
    url: '/gxzs/certificate/list?' + tansParams(paging),
    method: 'post',
    data: data
  })
}

// 查询证书关联人才采购详细
export function getCertificate(id) {
  return request({
    url: '/gxzs/certificate/' + id,
    method: 'get'
  })
}

// 新增证书关联人才采购
export function addCertificate(data) {
  return request({
    url: '/gxzs/certificate',
    method: 'post',
    data: data
  })
}

// 修改证书关联人才采购
export function updateCertificate(data) {
  return request({
    url: '/gxzs/certificate',
    method: 'put',
    data: data
  })
}

// 删除证书关联人才采购
export function delCertificate(id) {
  return request({
    url: '/gxzs/certificate/' + id,
    method: 'delete'
  })
}
