<template>
    <div class="lableInput_input" >
        <div v-if="lable" class="lableInput_selectInput_lable" >{{lable}}</div>
        <el-date-picker :value="value" @input="(e)=>{$emit('input',e)}" type="datetimerange" :picker-options="pickerOptions" :start-placeholder="placeholder[0]" :end-placeholder="placeholder[1]" :default-time="['00:00:00','23:59:59']"></el-date-picker>
    </div>
</template>

<script>
    export default {
        props:{
            width:{
                type:String,
                default:()=>{return "240px"}
            },
            lable:{
                type:String,
            },
            value:{
                type:[String,Number,Array]
            },
            placeholder:{
                type:[Array],
                default:()=>{return ['',''] }
            },
        },
        data(){
            return{
                styles:{"--width":this.width},
                pickerOptions: {
                    shortcuts: [
                        {
                            text: '最近一小时',
                            onClick: (picker) => {
                                const date = new Date();
                                picker.$emit('pick', [this.currentAllTime(new Date(date.setTime(date.getTime()-1 * 60 * 60 * 1000 ))),this.currentAllTime()]);
                            },
                        },
                        {
                            text: '今天',
                            onClick: (picker) => {
                                picker.$emit('pick', [this.currentTime()+' 00:00:00',this.currentTime()+' 23:59:59']);
                            },
                        },
                        {
                            text: '昨天',
                            onClick:(picker)=> {
                                const date = new Date();
                                let newDate = this.currentTime(new Date(date.setTime(date.getTime() - 3600 * 1000 * 24)));
                                picker.$emit('pick', [newDate+" 00:00:00",newDate+' 23:59:59']);
                            }
                        },
                        {
                            text: '最近一周',
                            onClick:(picker)=> {
                                const start = new Date();
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                                picker.$emit('pick', [this.currentTime(new Date(start))+' 00:00:00', this.currentTime()+" 23:59:59"]);
                            }
                        }, 
                        {
                            text: '最近一个月',
                            onClick:(picker)=> {
                                const start = new Date();
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                                picker.$emit('pick', [this.currentTime(new Date(start))+' 00:00:00', this.currentTime()+" 23:59:59"]);
                            }
                        }, 
                        {
                            text: '最近三个月',
                            onClick:(picker)=> {
                                const start = new Date();
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                                picker.$emit('pick', [this.currentTime(new Date(start))+' 00:00:00', this.currentTime()+" 23:59:59"]);
                            }
                        }
                    ]
                },
            }
        },
        methods:{
            currentTime(date = new Date()){
                let year = date.getFullYear(); 
                let month = date.getMonth() + 1;
                let day = date.getDate();
                month = (month > 9) ? month : ("0" + month);
                day = (day < 10) ? ("0" + day) : day;
                let today = year + "-" + month + "-" + day;
                return today
            },
            currentAllTime(date = new Date()){
                let sign2 = ":";
                let year = date.getFullYear() // 年
                let month = date.getMonth() + 1; // 月
                let day = date.getDate(); // 日
                let hour = date.getHours(); // 时
                let minutes = date.getMinutes(); // 分
                let seconds = date.getSeconds() //秒
                let weekArr = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期天'];
                let week = weekArr[date.getDay()];
                // 给一位数的数据前面加 “0”
                month >= 1 && month <= 9 && (month = "0" + month);
                day >= 0 && day <= 9 && (day = "0" + day);
                hour >= 0 && hour <= 9 && (hour = "0" + hour);
                minutes >= 0 && minutes <= 9 && (minutes = "0" + minutes);
                seconds >= 0 && seconds <= 9 && (seconds >= 0 && seconds <= 9);
                return year + "-" + month + "-" + day + " " + hour + sign2 + minutes + sign2 + seconds;
            },
        },
    }
</script>

<style lang="scss">
    .lableInput_input{
        display:flex;
        &_lable{
            display:flex;
            justify-content: center;
            align-items:center;
            height:36px;
            border:1px solid #dcdfe6;
            border-right:none;
            padding:0 8px;
            box-sizing:border-box;
            color:#1c2943;
            white-space: nowrap;
            margin-left:10px;
            font-size:14px;
            flex:none;
        }
        input{
            border-radius:0px;
        }
        .lableInput_input,.el-input__inner{
            width:var(--width);
        }
    }
</style>