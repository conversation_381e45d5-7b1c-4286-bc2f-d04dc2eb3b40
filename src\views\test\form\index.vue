<template>
  <div class="container">
    <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55">
      </el-table-column>
      <el-table-column label="日期" width="120">
        <template slot-scope="scope">{{ scope.row.date }}</template>
      </el-table-column>
      <el-table-column prop="name" label="姓名" width="120">
      </el-table-column>
      <el-table-column prop="address" label="地址" show-overflow-tooltip>
      </el-table-column>
    </el-table>
    <div style="margin-top: 20px">
      <!-- <el-button @click="look">查看</el-button> -->
      <el-button @click="test">测试按钮</el-button>
      <!-- <el-button @click="showModelLst">点击展示模板列表</el-button> -->


      <el-dropdown trigger="click" @visible-change="showModelLst">
        <el-button type="primary" :disabled="multipleSelection.length != 1">
          打印<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <div class="dropdowns">
            <el-dropdown-item v-for="(item, index) in modelLst" :key="index">
              <div style="padding: 0 20px;" @click="chooseModel(item)">{{ item.name }}</div>
            </el-dropdown-item>
          </div>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <test v-show="false" :transferData="transferData" ref="testRef"></test>

  </div>
</template>

<script>
import test from '../test/index.vue';
import { listPrintTemplateGet } from "@/api/system/printTemplate";
export default {
  name: 'form',
  components: { test },
  data() {
    return {
      tableData: [{
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄',
        modelData: { "panels": [{ "name": null, "height": 296.6, "width": 210, "paperHeader": 0, "paperFooter": 840.7559055118112, "printElements": [{ "options": { "left": 34.5, "top": 46.5, "height": 36, "width": 550, "field": "table", "coordinateSync": false, "widthHeightSync": false, "columns": [[{ "width": 275, "title": "姓名\n", "field": "name", "checked": true, "columnId": "name", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0, "tableSummaryTitle": true, "tableSummary": "" }, { "width": 275, "title": "年龄\n", "field": "age", "checked": true, "columnId": "age", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0, "tableSummaryTitle": true, "tableSummary": "" }]] }, "printElementType": { "title": "空白表格", "type": "table", "editable": true, "columnDisplayEditable": true, "columnDisplayIndexEditable": true, "columnTitleEditable": true, "columnResizable": true, "columnAlignEditable": true, "isEnableEditField": true, "isEnableContextMenu": true, "isEnableInsertRow": true, "isEnableDeleteRow": true, "isEnableInsertColumn": true, "isEnableDeleteColumn": true, "isEnableMergeCell": true } }], "paperNumberLeft": null, "paperNumberTop": null, "paperNumberContinue": true, "watermarkOptions": {} }] },
        showData: {
          table: [
            { name: '张三1', age: 10 },
            { name: '张三2', age: 11 },
            { name: '张三3', age: 12 },
            { name: '张三4', age: 13 },
            { name: '张三5', age: 14 },
            { name: '张三6', age: 15 },
            { name: '张三7', age: 16 },
            { name: '张三8', age: 17 },
            { name: '张三9', age: 18 },
            { name: '张三10', age: 90 },
            { name: '张三11', age: 20 },
            { name: '张三12', age: 21 },
            { name: '张三13', age: 22 },
            { name: '张三14', age: 23 },
            { name: '张三15', age: 24 },
            { name: '张三16', age: 25 },
          ]
        },
      }, {
        date: '2016-05-02',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄',
        modelData: { "panels": [{ "name": null, "height": 210, "width": 296.6, "paperHeader": 0, "paperFooter": 595.2755905511812, "printElements": [{ "options": { "left": 33, "top": 39, "height": 51, "width": 550, "right": 582.2500114440918, "bottom": 75, "vCenter": 307.2500114440918, "hCenter": 57, "field": "table", "coordinateSync": false, "widthHeightSync": false, "columns": [[{ "width": 275, "title": "金额\n", "field": "price", "checked": true, "columnId": "price", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0, "tableSummaryTitle": true, "tableSummary": "sum" }, { "width": 275, "title": "数量\n", "field": "count", "checked": true, "columnId": "count", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0, "tableSummaryTitle": true, "tableSummary": "" }, { "width": 100, "title": "", "field": "", "checked": false, "columnId": "", "fixed": false, "rowspan": 1, "colspan": 1 }, { "width": 100, "title": "", "field": "", "checked": false, "columnId": "", "fixed": false, "rowspan": 1, "colspan": 1 }]] }, "printElementType": { "title": "空白表格", "type": "table", "editable": true, "columnDisplayEditable": true, "columnDisplayIndexEditable": true, "columnTitleEditable": true, "columnResizable": true, "columnAlignEditable": true, "isEnableEditField": true, "isEnableContextMenu": true, "isEnableInsertRow": true, "isEnableDeleteRow": true, "isEnableInsertColumn": true, "isEnableDeleteColumn": true, "isEnableMergeCell": true } }], "paperNumberLeft": null, "paperNumberTop": null, "paperNumberContinue": true, "rotate": true, "watermarkOptions": {} }] },
        showData: {
          table: [
            { price: '1', count: 10 },
            { price: '2', count: 11 },
            { price: '3', count: 12 },
            { price: '3', count: 13 },
            { price: '4', count: 14 },
            { price: '5', count: 15 },
            { price: '6', count: 16 },
            { price: '7', count: 17 },
            { price: '8', count: 18 },
            { price: '9', count: 90 },
            { price: '10', count: 20 },
            { price: '11', count: 21 },
            { price: '12', count: 22 },
            { price: '12', count: 23 },
            { price: '13', count: 24 },
            { price: '14', count: 25 },
          ]
        }
      }, {
        date: '2016-05-04',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        date: '2016-05-01',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        date: '2016-05-08',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        date: '2016-05-06',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        date: '2016-05-07',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }],
      multipleSelection: [], // 选中的
      transferData: {}, // 传递给子组件的

      modelLst: [], // 存储模板的列表

    }
  },

  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    // 测试按钮
    test() {
      this.transferData = {
        showData: this.multipleSelection[0].showData,
      }
      this.$refs.testRef.importJson(this.multipleSelection[0].modelData);
      setTimeout(() => {
        this.$refs.testRef.print();
      }, 0);
    },

    // 查看按钮
    look() {
      console.log(this.multipleSelection, 'this.multipleSelection');
    },

    // 展开模板列表
    async showModelLst(bool) {
      if (!bool) return; // 如果是bool是false，证明是关，则不用走下面
      this.modelLst = [];
      const res = await listPrintTemplateGet();
      console.log(res, '查看结果');
      this.modelLst = res.rows;
    },

    // 点击选中模板
    chooseModel(item) {
      let printData = {
        table: [
          { name: '张三1', age: 10 },
          { name: '张三2', age: 11 },
          { name: '张三3', age: 12 },
          { name: '张三4', age: 13 },
          { name: '张三5', age: 14 },
          { name: '张三6', age: 15 },
          { name: '张三7', age: 16 },
          { name: '张三8', age: 17 },
          { name: '张三9', age: 18 },
          { name: '张三10', age: 92220 },
          { name: '张三11', age: 22220 },
          { name: '张三12', age: 21222 },
          { name: '张三13', age: 22222 },
          { name: '张三14', age: 22223 },
          { name: '张三15', age: 24222 },
          { name: '张三16', age: 22225 },
        ], name: '李四'
      }

      /**
      * this.$refs.testRef.showDialog(templateData, printData, width) 的传参
      * @param templateData: 必填 物流单模板代码，必须是对象(如果是JSON，则自行转换成对象)
      * @param printData: 必填 物流单的数据，对象
      * @param width: 选填 dialog框的宽度，默认纸张宽度
      */
      console.log('执行了');
      this.$refs.testRef.showDialog(JSON.parse(item.content), printData);


    },
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 98%;
  margin: 0 auto;
  border-radius: 20px;
  padding: 10px;
  background-color: #fff;
}

.dropdowns {
  ::v-deep .el-dropdown-menu__item {
    padding: 0;
  }
}
</style>