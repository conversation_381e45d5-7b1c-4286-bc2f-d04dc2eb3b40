<script setup>
import { ref, onMounted, inject, onBeforeUnmount } from 'vue';
// 注入GSAP
const gsap = inject('gsap');
import { PlusOutlined, MinusOutlined } from '@ant-design/icons-vue';

// 接收父组件传递的设备数据
const props = defineProps({
  devices: {
    type: Array,
    default: () => []
  }
});

// 网络状态
const isOnline = ref(navigator.onLine);
const checkNetworkStatus = () => {
  isOnline.value = navigator.onLine;
};

// 监听网络状态变化
onMounted(() => {
  window.addEventListener('online', checkNetworkStatus);
  window.addEventListener('offline', checkNetworkStatus);
});

onBeforeUnmount(() => {
  window.removeEventListener('online', checkNetworkStatus);
  window.removeEventListener('offline', checkNetworkStatus);
});

// 模拟水炮机数据
const waterCannons = ref([
  {
    id: 1,
    running: true,
    currentAngle: 45,
    valveStatus: true,
    pumpStatus: true,
    faults: { valveFault: false, pumpFault: false },
  },
  {
    id: 2,
    running: false,
    currentAngle: 30,
    valveStatus: false,
    pumpStatus: false,
    faults: { valveFault: true, pumpFault: false },
  },
  {
    id: 3,
    running: true,
    currentAngle: 60,
    valveStatus: true,
    pumpStatus: true,
    faults: { valveFault: false, pumpFault: false },
  },
  {
    id: 4,
    running: false,
    currentAngle: 0,
    valveStatus: false,
    pumpStatus: false,
    faults: { valveFault: false, pumpFault: true },
  },
]);

const toggleValve = (cannon) => {
  cannon.valveStatus = !cannon.valveStatus;
  updateRunningStatus(cannon);

  // 动画效果
  animateToggle(cannon);
};

const togglePump = (cannon) => {
  cannon.pumpStatus = !cannon.pumpStatus;
  updateRunningStatus(cannon);

  // 动画效果
  animateToggle(cannon);
};

const updateRunningStatus = (cannon) => {
  const wasRunning = cannon.running;
  cannon.running = cannon.valveStatus && cannon.pumpStatus;

  // 角度动画
  if (cannon.running && !wasRunning) {
    const randomAngle = Math.floor(Math.random() * 90);
    animateAngleChange(cannon, randomAngle);
  } else if (!cannon.running) {
    animateAngleChange(cannon, 0);
  }
};

// 角度变化动画
const animateAngleChange = (cannon, targetAngle) => {
  gsap.to(cannon, {
    currentAngle: targetAngle,
    duration: 1.5,
    ease: "power2.inOut"
  });
};

// 状态切换动画
const animateToggle = (cannon) => {
  const targetCard = document.querySelector(`.water-cannon-${cannon.id}`);
  if (targetCard) {
    gsap.to(targetCard, {
      backgroundColor: '#0D275A',
      duration: 0.5,
      ease: "power2.inOut",
      clearProps: "transform", // 确保清除transform属性
      onComplete: () => {
        // 动画完成后强制清除transform属性
        targetCard.style.transform = "none";
      }
    });

    // 标签动画
    const statusTag = targetCard.querySelector('.status-tag');
    if (statusTag) {
      gsap.fromTo(statusTag,
        { scale: 0.8, opacity: 0.7 },
        {
          scale: 1,
          opacity: 1,
          duration: 0.3,
          ease: "back.out(1.7)",
          clearProps: "all" // 清除所有动画属性
        }
      );
    }
  }
};

onMounted(() => {
  // 卡片入场动画
  const cards = document.querySelectorAll('.cannon-card');
  gsap.from(cards, {
    y: 30,
    opacity: 0,
    duration: 0.6,
    stagger: 0.1,
    ease: "power2.out",
    clearProps: "transform,opacity" // 确保动画结束后清除所有属性
  });
});

const adjustAngle = (cannon, increment) => {
  if (!cannon.running) return;

  if (increment) {
    cannon.currentAngle = Math.min(90, cannon.currentAngle + 5);
  } else {
    cannon.currentAngle = Math.max(0, cannon.currentAngle - 5);
  }
};
</script>

<template>
  <div class="water-cannon-container">
    <a-row :gutter="[16, 24]">
      <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8" v-for="cannon in waterCannons" :key="cannon.id">
        <a-card
          :title="`${cannon.id}号水炮机`"
          :bordered="false"
          :class="['cannon-card', `water-cannon-${cannon.id}`, {'running': cannon.running}]"
          :headStyle="{color: '#FFFFFF', backgroundColor: '#0D275A', borderBottom: 'none'}"
        >
          <template #extra>
            <span class="network-status" :class="{ 'online': isOnline, 'offline': !isOnline }">
              <span class="status-dot"></span>
              {{ isOnline ? '在线' : '离线' }}
            </span>
            <a-tag :color="cannon.running ? '#2ECC71' : '#E74C3C'" class="status-tag">
              {{ cannon.running ? '运行中' : '已停止' }}
            </a-tag>
          </template>
          <div style="font-size:13px;line-height:1.6;" class="angle-display">
            当前角度: <span class="angle-value">{{ Math.round(cannon.currentAngle) }}°</span>
            <div class="angle-indicator" :style="{ transform: `rotate(${cannon.currentAngle}deg)` }">
              <div class="indicator-line"></div>
            </div>
          </div>
          <div class="switch-row">
            <span style="color: #FFFFFF;">阀门:</span>
            <a-switch :checked="cannon.valveStatus" @change="toggleValve(cannon)"
                     active-color="#2ECC71" inactive-color="#6C5B7B" class="control-switch" />
            <span style="margin-left:8px; color: #FFFFFF;">水泵:</span>
            <a-switch :checked="cannon.pumpStatus" @change="togglePump(cannon)"
                     active-color="#2ECC71" inactive-color="#6C5B7B" class="control-switch" />
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<style scoped>
/* 网络状态样式 */
.network-status {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  margin-right: 8px;
  transition: all 0.3s;
}

.network-status .status-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 4px;
}

.network-status.online {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.network-status.online .status-dot {
  background-color: #2ecc71;
  box-shadow: 0 0 6px #2ecc71;
}

.network-status.offline {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.network-status.offline .status-dot {
  background-color: #e74c3c;
  box-shadow: 0 0 6px #e74c3c;
}

/* 卡片悬停效果 */
.cannon-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.5s ease;
}

.cannon-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 20px rgba(0,0,0,0.4);
}

.cannon-card.running {
  background-color: #0D275A;
}

/* 角度显示动画 */
.angle-display {
  position: relative;
  margin-bottom: 10px;
}

.angle-value {
  transition: all 0.3s ease;
  font-weight: bold;
}

.angle-indicator {
  width: 20px;
  height: 20px;
  position: relative;
  display: inline-block;
  margin-left: 10px;
  transition: transform 1.5s ease;
}

.indicator-line {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #3498DB;
  transform-origin: center left;
}

/* 开关动画 */
.control-switch {
  transition: all 0.4s ease !important;
}

.control-switch.ant-switch-checked {
  transform: scale(1.05);
}

/* 状态标签动画 */
.status-tag {
  transition: all 0.3s ease;
}

.water-cannon-container {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}
.section-title {
  margin-bottom: 20px;
  color: #FFFFFF;
  font-weight: bold;
}
.cannon-card {
  background: #0D275A;
  box-shadow: 0 8px 16px rgba(0,0,0,0.3);
  border-radius: 12px;
  height: auto;
  min-height: 280px; /* 增加最小高度 */
  margin: 0;
  color: #FFFFFF;
  border: none;
  display: flex;
  flex-direction: column;
  transform: none !important; /* 防止transform属性影响布局 */
  will-change: auto; /* 优化动画性能 */
}
.fault-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}
.fault-tag {
  margin-bottom: 8px;
}
.control-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.switch-row {
  display: flex;
  align-items: center;
  gap: 2px;
  margin-top: 8px;
  flex-wrap: wrap;
}
.angle-control {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px 0;
}
@media (max-width: 768px) {
  .cannon-card {
    margin-bottom: 4px;
    padding: 4px 0;
  }
  .section-title {
    margin-bottom: 8px;
    font-size: 16px;
  }
  .a-row {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  .a-col {
    padding-left: 2px !important;
    padding-right: 2px !important;
  }
  .control-buttons { flex-direction: column; }
  .control-buttons button { margin-bottom: 8px; }
}
</style>
