// 工具类统一导出文件

// 请求工具
import request, { request as axiosInstance } from './request.js'

// WebSocket工具
import WebSocketClient, { getWebSocketInstance, destroyWebSocketInstance } from './websocket.js'

// 导出所有工具
export {
  // 请求相关
  request,
  axiosInstance,
  
  // WebSocket相关
  WebSocketClient,
  getWebSocketInstance,
  destroyWebSocketInstance,
}

// 默认导出常用工具
export default {
  request,
  WebSocketClient,
  getWebSocketInstance,
}