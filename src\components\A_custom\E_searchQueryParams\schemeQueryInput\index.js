export let operatorControl = {
    // 操作符 代表值 多选 和 单选 禁用
    single:[1,2,3,5,4,6,12],//单选的值 
    multiple:[9,10],//多选的值
    disabled:[7,8],//禁用的值
    range:[11],//范围
    /*
        1:等于,
        2:不等于,
        3:大于
        4:大于等于
        5:小于
        6:小于等于
        7:为空
        8:不为空
        9:包含
        10:不包含
        11:范围
        12:类似于
    */
}

// 选择操作符输入框需要回显的状态
export function insertConfiguration(id,type){
    return {
        'multiple': operatorControl.multiple.includes(id),
        "disabled": operatorControl.disabled.includes(id),
        "range" : operatorControl.range.includes(id),
    }
}

// 输入框中可以点击操作符的权限
export let inputJudgeOperator = {
    1:["1","2","3","4","5","6","7","8","9","10","12"],//输入框
    2:["1","2","3","4","5","6","7","8","9","10","12"],//选择框
    3:["1","2","3","4","5","6","7","8","11"],//数字框 "9","10",
    4:["1","2","3","4","5","6","7","8","11"],//日期
    5:["1","2","3","4","5","6","7","8","9","10","12"],//弹框
}
