from itertools import count
from pymodbus.client import ModbusTcpClient
from pymodbus.exceptions import ModbusException
import struct

class WaterCannonController:
    def __init__(self, ip, port):
        self.client = ModbusTcpClient(host=ip, port=port)
        if not self.client.connect():
            raise ConnectionError(f"无法连接到Modbus服务器: {ip}:{port}")

    def __del__(self):
        self.client.close()

    # 读取水炮状态信息
    def read_status(self):
        status = {}
        # 读取开关量状态
        try:
            result = self.client.read_coils(4000, count=13)
            if not result.isError():
                status['水泵运行状态'] = result.bits[0]
                status['水平反转状态'] = result.bits[1]
                status['水平正转状态'] = result.bits[2]
                status['上下反转状态'] = result.bits[3]
                status['上下正转状态'] = result.bits[4]
                status['远射状态'] = result.bits[5]
                status['花洒状态'] = result.bits[6]
                status['远程运行状态'] = result.bits[7]
                status['水泵联动状态'] = result.bits[8]
                status['上下巡航联动'] = result.bits[9]
                status['故障'] = result.bits[10]
        except ModbusException as e:
            print(f"读取线圈错误: {e}")

        # 读取浮点数状态
        try:
            result = self.client.read_holding_registers(4004, count=8)
            if not result.isError():
                raw_data = result.registers
                status['当前水平角度'] = struct.unpack('!f', struct.pack('!HH', raw_data[0], raw_data[1]))[0]
                status['当前上下角度'] = struct.unpack('!f', struct.pack('!HH', raw_data[2], raw_data[3]))[0]
                status['水平巡航低限角度'] = struct.unpack('!f', struct.pack('!HH', raw_data[4], raw_data[5]))[0]
                status['水平巡航高限角度'] = struct.unpack('!f', struct.pack('!HH', raw_data[6], raw_data[7]))[0]
        except ModbusException as e:
            print(f"读取保持寄存器(4004)错误: {e}")

        try:
            result = self.client.read_holding_registers(4020, count=4)
            if not result.isError():
                raw_data = result.registers
                status['上下巡航低限角度'] = struct.unpack('!f', struct.pack('!HH', raw_data[0], raw_data[1]))[0]
                status['上下巡航高限角度'] = struct.unpack('!f', struct.pack('!HH', raw_data[2], raw_data[3]))[0]
        except ModbusException as e:
            print(f"读取保持寄存器(4020)错误: {e}")

        return status

    # 控制水炮操作
    def control_water_cannon(self, operation, value):
        try:
            if operation == '巡航喷洒':
                self.client.write_coil(4050,value)
            elif operation == '故障复位':
                self.client.write_coil(4051,value)
            elif operation == '巡航运行时间':
                self.client.write_register(4054,value)
            elif operation == '巡航间隔时间':
                self.client.write_register(4056,value)
            else:
                raise ValueError(f"不支持的操作: {operation}")
        except ModbusException as e:
            print(f"写入操作错误: {e}")


# 示例使用
if __name__ == "__main__":
    ip = 'localhost'
    port = 502
    controller = WaterCannonController(ip, port)

    # 读取水炮状态
    status = controller.read_status()
    print("水炮当前状态:", status)

    # 控制水炮进行巡航喷洒
    controller.control_water_cannon('巡航喷洒', 1)
    print("已启动巡航喷洒")

    # 设置巡航运行时间为10分钟
    controller.control_water_cannon('巡航运行时间', 10)
    print("已设置巡航运行时间为10分钟")

    status = controller.read_status()
    print("水炮当前状态:", status)