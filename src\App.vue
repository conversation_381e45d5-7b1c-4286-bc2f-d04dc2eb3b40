<template>
  <div id="app" v-loading="$store.state.settings.loadingStatus">
    <router-view />
    <theme-picker />
  </div>
</template>

<script>
import ThemePicker from "@/components/ThemePicker";

export default {
  name: "App",
  components: { ThemePicker },
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
      titleTemplate: title => {
        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
      }
    }
  }
};
</script>
<style lang="scss">

.el-table__fixed {
  height: calc(100% - 10px) !important;
}


.el-table__fixed-right {
  height: calc(100% - 10px) !important;
  right: 10px !important;
}


.el-table__fixed-right-patch {
  width: 10px !important;
}


.el-table__fixed-body-wrapper .el-table__body {
  padding-bottom: 10px;
  box-sizing: border-box;
}

.el-table__fixed-right::before,
.el-table__fixed::before {
  background: transparent !important;
}


.el-table__body-wrapper::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  background: transparent;
  border-radius: 4px;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
  border-radius: 12px;
}

.el-table__header-wrapper {
  padding-right: 10px;
}

.el-loading-mask {
  z-index: 99995 !important;
  height: 100vh;
}

.el-loading-spinner .circular {
  height: 80px;
  width: 80px;
}

#app .theme-picker {
  display: none;
}

#nprogress .bar {
  background: #1ABC9C !important;
}
</style>
