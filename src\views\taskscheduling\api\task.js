import request from '@/utils/request'

// 2.任务管理 参数查询
export function jobinfoJsonTaskApi(query) {
    return request({
      url: '/xxl-job/jobinfo/indexJson',
      method: 'get',
      params: query
    })
}

// 2.任务管理 列表
export function jobinfoTableTaskApi(query) {
    return request({
      url: '/xxl-job/jobinfo/pageList',
      method: 'get',
      params: query
    })
}

// 2.任务管理 执行一次保存接口
export function jobinfoTriggerTaskApi(query) {
    return request({
      url: '/xxl-job/jobinfo/trigger',
      method: 'get',
      params: query
    })
}
// 2.任务管理 注册节点 
export function jobinfoRegisterNodesTaskApi(query) {
    return request({
      url: '/xxl-job/jobgroup/loadById',
      method: 'get',
      params: query
    })
}

// 2.任务管理 下次执行时间 
export function jobinfoNextExecutionTimeTaskApi(query) {
    return request({
      url: '/xxl-job/jobinfo/nextTriggerTime',
      method: 'get',
      params: query
    })
}
// 2.任务管理 启动 
export function jobinfoNextStartTaskApi(query) {
    return request({
      url: '/xxl-job/jobinfo/start',
      method: 'get',
      params: query
    })
}

// 2.任务管理 停用 
export function jobinfoNextStopTaskApi(query) {
    return request({
      url: '/xxl-job/jobinfo/stop',
      method: 'get',
      params: query
    })
}
//  2.任务管理 新增 
export function jobinfoAddTaskApi(query) {
    return request({
      url: '/xxl-job/jobinfo/add',
      method: 'get',
      params: query
    })
}

//  2.任务管理 编辑 
export function jobinfoUpdateTaskApi(data) {
    return request({
      url: '/xxl-job/jobinfo/update',
      method: 'post',
      data: data
    })
}
// 2. 任务管理 获取任务设置时间/jobinfo/nextTriggerTime
export function jobinfoNextTriggerTimeTaskApi(query) {
  return request({
    url: '/xxl-job/jobinfo/nextTriggerTime',
    method: 'get',
    params: query
  })
}
// 
// 2. 任务管理 删除列表数据
export function jobinfoRemoveApi(query) {
  return request({
    url: '/xxl-job/jobinfo/remove',
    method: 'get',
    params: query
  })
}
