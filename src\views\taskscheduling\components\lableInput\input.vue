<template>
    <div class="lableInput_input" >
        <div v-if="lable" class="lableInput_selectInput_lable" >{{lable}}</div>
        <el-input type="text" :style="[styles]" v-bind="$attrs" :value="value" @input="inputValue" :placeholder="placeholder" >
            <template v-for="(index, name) in $slots" :slot="name">
                <slot :name="name" ></slot>
            </template>
        </el-input>
    </div>
</template>

<script>
    export default {
        props:{
            width:{
                type:String,
                default:()=>{return "255px"}
            },
            lable:{
                type:String,
            },
            value:{
                type:[String,Number,Array]
            },
            placeholder:{
                type:[String],
                default:()=>{return "请选择" }
            },
            type:{
                type:[String],
                default:()=>{return "test" }
            },
        },
        data(){
            return{
                styles:{"--width":this.width},
            }
        },
        methods:{
            inputValue(val){
                let value = val
                switch(this.type){
                    case "number" : value = val.replace(/\D/g,'');break;
                }
                this.$emit("input",value)
            }
        },
    }
</script>

<style lang="scss">
    .lableInput_input{
        display:flex;
        &_lable{
            display:flex;
            justify-content: center;
            align-items:center;
            height:36px;
            border:1px solid #dcdfe6;
            border-right:none;
            padding:0 8px;
            box-sizing:border-box;
            color:#1c2943;
            white-space: nowrap;
            margin-left:10px;
            font-size:14px;
            flex:none;
        }
        input,.el-textarea__inner{
            border-radius:0px;
        }
        .lableInput_input,.el-input__inner,.el-textarea__inner{
            width:var(--width);
        }
    }
</style>