<template>
  <div class="bi-screen">
    <!-- 顶部状态栏 -->
    <div class="header">
      <div class="header-left">
        <h1 class="title">智能设备监控大屏</h1>
      </div>
      <div class="header-center">
        <div class="datetime">{{ currentDateTime }}</div>
      </div>
      <div class="header-right">
        <div class="weather">
          <span class="weather-icon">☀️</span>
          <span class="temperature">{{ environmentData.temperature }}</span>
          <span class="humidity">{{ environmentData.humidity }}</span>
          <span class="wind">{{ environmentData.windDirection }} {{ environmentData.windSpeed }}</span>
        </div>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 左侧数据统计 -->
      <div class="left-panel">
        <div class="data-card">
          <div class="card-title">设备状态概览</div>
          <div class="status-overview">
            <div class="status-item" v-for="item in statusOverview" :key="item.type">
              <div class="status-icon" :class="item.type">
                <i :class="item.icon"></i>
              </div>
              <div class="status-info">
                <div class="status-count">{{ item.count }}</div>
                <div class="status-name">{{ item.name }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="data-card">
          <div class="card-title">设备类型分布</div>
          <div ref="deviceTypeChart" class="chart-container"></div>
        </div>
      </div>

      <!-- 中间图表区 -->
      <div class="center-panel">
        <div class="map-container">
          <div class="map-title">设备分布图</div>
          <div class="map-content" ref="mapChart"></div>
        </div>
      </div>

      <!-- 右侧数据区 -->
      <div class="right-panel">
        <div class="data-card">
          <div class="card-title">实时监控</div>
          <div class="monitor-list">
            <div class="monitor-item" v-for="device in deviceList" :key="device.id">
              <div class="device-info">
                <div class="device-name">{{ device.name }}</div>
                <div class="device-status" :class="device.status"></div>
              </div>
              <div class="device-data">
                <div class="data-item">
                  <span class="label">状态</span>
                  <span class="value" :class="device.status">{{ getStatusText(device.status) }}</span>
                </div>
                <div class="data-item">
                  <span class="label">温度</span>
                  <span class="value">{{ device.temperature }}°C</span>
                </div>
                <div class="data-item">
                  <span class="label">湿度</span>
                  <span class="value">{{ device.humidity }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部状态表格 -->
    <div class="footer">
      <div class="data-card">
        <div class="card-title">设备状态明细</div>
        <div class="status-table">
          <div class="table-header">
            <div class="header-cell" v-for="col in tableColumns" :key="col.key">{{ col.title }}</div>
          </div>
          <div class="table-body">
            <div class="table-row" v-for="row in tableData" :key="row.id">
              <div class="table-cell" v-for="col in tableColumns" :key="col.key">
                <template v-if="col.key === 'status'">
                  <span class="status-badge" :class="row.status">{{ getStatusText(row.status) }}</span>
                </template>
                <template v-else>
                  {{ row[col.key] }}
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
// 导入中国地图 JSON 数据
import chinaJson from 'echarts/map/json/china.json';

// 注册中国地图
if (!echarts.getMap('china')) {
  echarts.registerMap('china', chinaJson);
}

// 环境数据
const environmentData = ref({
  temperature: '28°C',
  humidity: '65%',
  windDirection: '东南风',
  windSpeed: '3级'
});

// 当前时间
const currentDateTime = ref('');

// 状态概览数据
const statusOverview = ref([
  { type: 'online', name: '在线设备', count: 128, icon: 'icon-online' },
  { type: 'offline', name: '离线设备', count: 12, icon: 'icon-offline' },
  { type: 'warning', name: '告警设备', count: 8, icon: 'icon-warning' },
  { type: 'error', name: '故障设备', count: 3, icon: 'icon-error' }
]);

// 设备列表数据
const deviceList = ref([
  { id: 1, name: '设备A', status: 'online', temperature: 25.5, humidity: 60 },
  { id: 2, name: '设备B', status: 'warning', temperature: 28.1, humidity: 65 },
  { id: 3, name: '设备C', status: 'offline', temperature: 0, humidity: 0 },
  { id: 4, name: '设备D', status: 'online', temperature: 26.8, humidity: 62 },
  { id: 5, name: '设备E', status: 'error', temperature: 0, humidity: 0 }
]);

// 表格列配置
const tableColumns = [
  { key: 'id', title: '设备ID' },
  { key: 'name', title: '设备名称' },
  { key: 'type', title: '设备类型' },
  { key: 'location', title: '位置' },
  { key: 'status', title: '状态' },
  { key: 'lastUpdate', title: '最后更新时间' }
];

// 表格数据
const tableData = [
  { id: 'DEV001', name: '烟雾机-01', type: '烟雾机', location: 'A区', status: 'online', lastUpdate: '2023-05-19 14:30:22' },
  { id: 'DEV002', name: '水炮机-02', type: '水炮机', location: 'B区', status: 'offline', lastUpdate: '2023-05-19 13:45:10' },
  { id: 'DEV003', name: '水泵-01', type: '水泵', location: 'C区', status: 'warning', lastUpdate: '2023-05-19 14:15:33' },
  { id: 'DEV004', name: '烟雾机-03', type: '烟雾机', location: 'A区', status: 'error', lastUpdate: '2023-05-19 12:20:15' },
  { id: 'DEV005', name: '水炮机-05', type: '水炮机', location: 'B区', status: 'online', lastUpdate: '2023-05-19 14:29:45' }
];

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    online: '在线',
    offline: '离线',
    warning: '告警',
    error: '故障'
  };
  return statusMap[status] || status;
};

// 更新时间
const updateDateTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  
  currentDateTime.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 图表实例
let deviceTypeChart = null;
let mapChart = null;

// 处理窗口大小变化 - 使用后面定义的handleResize函数

// 组件挂载
onMounted(() => {
  // 更新并设置时间
  updateDateTime();
  setInterval(updateDateTime, 1000);
  
  // 初始化图表
  setTimeout(() => {
    initCharts();
  }, 100);
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
  
  // 检测屏幕尺寸
  checkScreenSize();
  
  // 初始化饼图
  initMainPieChart();
  
  // 页面加载动画
  const timeline = gsap.timeline({
    defaults: { duration: 0.6, ease: "power2.out" }
  });

  timeline
    .from('.header-section', { y: -30, opacity: 0, clearProps: "all" })
    .from('.nav-section', { opacity: 0, y: 20, clearProps: "all" }, '-=0.3')
    .from('.left-panel', { opacity: 0, x: -20, clearProps: "all" }, '-=0.3')
    .from('.right-panel', { opacity: 0, x: 20, clearProps: "all" }, '-=0.3')
    .from('.footer-section', { y: 20, opacity: 0, clearProps: "all" }, '-=0.3');
});

// 组件卸载前
onBeforeUnmount(() => {
  if (deviceTypeChart) {
    deviceTypeChart.dispose();
  }
  if (mapChart) {
    mapChart.dispose();
  }
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('resize', checkScreenSize);
});
</script>

<style scoped>
/* 全局样式 */
:root {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --text-color: #fff;
  --text-secondary: rgba(255, 255, 255, 0.65);
  --background: #0f1c3f;
  --card-bg: #1a2f5e;
  --card-border: #2a3f6f;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body, html {
  width: 100%;
  height: 100%;
  font-family: 'Microsoft YaHei', sans-serif;
  color: var(--text-color);
  background: var(--background);
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
}

/* 布局 */
.bi-screen {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background: var(--background);
  color: var(--text-color);
  overflow: hidden;
}

/* 顶部状态栏 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 20px;
  background: rgba(26, 47, 94, 0.8);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 10;
}

.header-left .title {
  font-size: 20px;
  font-weight: bold;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent; /* 标准属性，确保在不支持 -webkit-text-fill-color 的浏览器中也能透明显示 */
}

.header-center {
  flex: 1;
  text-align: center;
}

.datetime {
  font-size: 16px;
  color: #fff;
  letter-spacing: 1px;
}

.header-right {
  display: flex;
  align-items: center;
}

.weather {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 14px;
}

.weather-icon {
  font-size: 20px;
}

/* 主内容区 */
.main-content {
  display: flex;
  flex: 1;
  padding: 15px;
  gap: 15px;
  overflow: hidden;
}

/* 左侧面板 */
.left-panel {
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 右侧面板 */
.right-panel {
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 中间面板 */
.center-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 数据卡片 */
.data-card {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #fff;
  position: relative;
  padding-left: 10px;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: var(--primary-color);
  border-radius: 2px;
}

/* 状态概览 */
.status-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-top: 10px;
}

.status-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 12px;
  transition: all 0.3s;
}

.status-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
}

.status-icon.online {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.status-icon.offline {
  background: rgba(140, 140, 140, 0.1);
  color: #8c8c8c;
}

.status-icon.warning {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.status-icon.error {
  background: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}

.status-info {
  flex: 1;
}

.status-count {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 4px;
}

.status-name {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 图表容器 */
.chart-container,
.map-content {
  flex: 1;
  min-height: 200px;
  width: 100%;
}

/* 地图容器 */
.map-container {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 8px;
  padding: 15px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.map-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #fff;
}

/* 监控列表 */
.monitor-list {
  flex: 1;
  overflow-y: auto;
  padding-right: 5px;
}

.monitor-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
  transition: all 0.3s;
}

.monitor-item:last-child {
  margin-bottom: 0;
}

.monitor-item:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.device-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.device-name {
  font-weight: 500;
  font-size: 14px;
}

.device-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.device-status.online {
  background: var(--success-color);
  box-shadow: 0 0 6px var(--success-color);
}

.device-status.offline {
  background: #8c8c8c;
  box-shadow: 0 0 6px #8c8c8c;
}

.device-status.warning {
  background: var(--warning-color);
  box-shadow: 0 0 6px var(--warning-color);
}

.device-status.error {
  background: var(--error-color);
  box-shadow: 0 0 6px var(--error-color);
}

.device-data {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.data-item {
  display: flex;
  flex-direction: column;
}

.data-item .label {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.data-item .value {
  font-size: 14px;
  font-weight: 500;
}

.data-item .value.online {
  color: var(--success-color);
}

.data-item .value.offline {
  color: #8c8c8c;
}

.data-item .value.warning {
  color: var(--warning-color);
}

.data-item .value.error {
  color: var(--error-color);
}

/* 底部表格 */
.footer {
  height: 250px;
  padding: 0 15px 15px;
}

.status-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.table-header,
.table-row {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  padding: 12px 15px;
  align-items: center;
}

.table-header {
  background: rgba(0, 0, 0, 0.2);
  font-weight: bold;
  font-size: 14px;
}

.table-body {
  flex: 1;
  overflow-y: auto;
}

.table-row {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s;
  font-size: 13px;
}

.table-row:hover {
  background: rgba(255, 255, 255, 0.05);
}

.status-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: normal;
}

.status-badge.online {
  background: rgba(82, 196, 26, 0.2);
  color: #52c41a;
}

.status-badge.offline {
  background: rgba(140, 140, 140, 0.2);
  color: #8c8c8c;
}

.status-badge.warning {
  background: rgba(250, 173, 20, 0.2);
  color: #faad14;
}

.status-badge.error {
  background: rgba(245, 34, 45, 0.2);
  color: #f5222d;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }
  
  .left-panel,
  .right-panel {
    width: 100%;
    flex-direction: row;
  }
  
  .left-panel .data-card,
  .right-panel .data-card {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .status-overview {
    grid-template-columns: 1fr;
  }
  
  .header {
    flex-direction: column;
    height: auto;
    padding: 10px;
    text-align: center;
  }
  
  .header-left,
  .header-center,
  .header-right {
    margin: 5px 0;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .table-cell:nth-child(4),
  .table-cell:nth-child(5) {
    display: none;
  }
}

/* 动画 */
@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(10px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

.data-card {
  animation: fadeIn 0.5s ease-out forwards;
}

/* 状态灯动画 */
@keyframes pulse {
  0% { 
    opacity: 0.6; 
  }
  50% { 
    opacity: 1; 
  }
  100% { 
    opacity: 0.6; 
  }
}

.device-status {
  animation: pulse 2s infinite;
}

.device-status.offline {
  animation: none;
  opacity: 0.6;
}
</style>

<script setup>
// 设备数据
const fogCannons = ref([
  {
    id: 1,
    running: true,
    totalFlow: 1234.5,
    instantFlow: 15.2,
    pressure: 3.2,
    faults: {
      valveFault: false,
      pumpFault: false,
      emergencyStop: false
    }
  },
  {
    id: 2,
    running: false,
    totalFlow: 1432.7,
    instantFlow: 0,
    pressure: 0,
    faults: {
      valveFault: true,
      pumpFault: false,
      emergencyStop: false
    }
  },
  {
    id: 3,
    running: true,
    totalFlow: 2145.9,
    instantFlow: 29.8,
    pressure: 3.9,
    faults: {
      valveFault: false,
      pumpFault: false,
      emergencyStop: false
    }
  }
]);

// 水泵数据
const waterPumps = ref([
  {
    id: 1,
    running: true,
    power: 45.8,
    pressure: 4.5,
    faults: {
      overload: false,
      phaseLoss: false,
      emergencyStop: false
    }
  },
  {
    id: 2,
    running: false,
    power: 0,
    pressure: 0,
    faults: {
      overload: false,
      phaseLoss: true,
      emergencyStop: false
    }
  },
  {
    id: 3,
    running: true,
    power: 42.3,
    pressure: 4.2,
    faults: {
      overload: false,
      phaseLoss: false,
      emergencyStop: false
    }
  }
]);

// 检查设备是否有故障
const hasFault = (faults) => {
  return Object.values(faults).some(value => value === true);
};

// 切换设备电源
const togglePower = (device) => {
  device.running = !device.running;
  
  // 如果开启设备，清除故障
  if (device.running) {
    Object.keys(device.faults).forEach(key => {
      device.faults[key] = false;
    });
  }
  
  // 更新饼图
  initMainPieChart();
};

// 检测屏幕尺寸
const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768;
};

// 趋势图时间周期选择
const selectedTrendPeriod = ref('day');

// 设备统计数据
const fogCannonStats = ref({
  total: 3,
  running: 1,
  fault: 1,
  stopped: 1
});

const waterCannonStats = ref({
  total: 3,
  running: 1,
  fault: 1,
  stopped: 1
});

const waterPumpStats = ref({
  total: 3,
  running: 1,
  fault: 1,
  stopped: 1
});

// 实时数据
const totalFlowRate = ref(125.6);
const avgPressure = ref(3.5);
const totalPower = ref(45.8);

// 图表引用
const mainPieChart = ref(null);
const fogCannonChart = ref(null);
const waterCannonChart = ref(null);
const waterPumpChart = ref(null);

// 故障警报
const alerts = ref([
  {
    time: '19:05:32',
    device: '2号雾炮机',
    message: '风机故障',
    level: 'critical'
  },
  {
    time: '18:47:15',
    device: '4号水泵',
    message: '相位缺失',
    level: 'warning'
  },
  {
    time: '18:30:22',
    device: '1号水炮机',
    message: '阀门故障',
    level: 'warning'
  }
]);

// 活动标签页
const activeTabKey = ref('fogCannon');

// 初始化设备状态饼图
const initStatusChart = (chartRef, data, colors) => {
  const chartDom = chartRef.value;
  const myChart = echarts.init(chartDom);
  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: ['60%', '80%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: false
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: data.running, name: '运行中', itemStyle: { color: colors[0] } },
          { value: data.fault, name: '故障', itemStyle: { color: colors[1] } },
          { value: data.stopped, name: '停机', itemStyle: { color: colors[2] } }
        ]
      }
    ]
  };
  myChart.setOption(option);
  return myChart;
};

// 初始化实时数据折线图
const initLineChart = (chartRef, color) => {
    const chartDom = chartRef.value;
    const myChart = echarts.init(chartDom);

    // 生成随机数据
    const generateData = () => {
      const now = new Date();
      const data = [];
      for (let i = 0; i < 20; i++) {
        const time = new Date(now - (20 - i) * 1000);
        data.push({
          name: time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', second: '2-digit' }),
          value: [time.getTime(), Math.random() * 10 + 30]
        });
      }
      return data;
    };

    const option = {
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'time',
        boundaryGap: false,
        axisLine: {
          lineStyle: {
            color: '#999'
          }
        },
        axisLabel: {
          fontSize: 10,
          formatter: '{HH}:{mm}:{ss}'
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#999'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        },
        axisLabel: {
          fontSize: 10
        }
      },
      series: [
        {
          type: 'line',
          smooth: true,
          symbol: 'none',
          lineStyle: {
            color: color,
            width: 2
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: color.includes('rgba') ? color.replace(/([\d.]+)\)$/, '0.6)') : color.replace(/\)$/, ', 0.6)')
              },
              {
                offset: 1,
                color: color.includes('rgba') ? color.replace(/([\d.]+)\)$/, '0.1)') : color.replace(/\)$/, ', 0.1)')
              }
            ])
          },
          data: generateData()
        }
      ]
    };
    myChart.setOption(option);
    return myChart;
  };

// 初始化所有图表
const initCharts = () => {
  // 确保图表容器存在
  if (!fogCannonChart.value || !waterCannonChart.value || !waterPumpChart.value ||
      !flowRateChart.value || !pressureChart.value || !powerChart.value ||
      !faultTypeChart.value) {
    console.error('Some chart containers are not available');
    return;
  }

  // 计算设备状态数据
  const calculateDeviceStats = () => {
    const running = fogCannons.value.filter(c => c.running).length + 
                   waterCannons.value.filter(c => c.running).length + 
                   waterPumps.value.filter(p => p.running).length;
                   
    const fault = fogCannons.value.filter(c => !c.running && hasFault(c.faults)).length + 
                 waterCannons.value.filter(c => !c.running && hasFault(c.faults)).length + 
                 waterPumps.value.filter(p => !p.running && hasFault(p.faults)).length;
                 
    const stopped = fogCannons.value.filter(c => !c.running && !hasFault(c.faults)).length + 
                   waterCannons.value.filter(c => !c.running && !hasFault(c.faults)).length + 
                   waterPumps.value.filter(p => !p.running && !hasFault(p.faults)).length;
                   
    return { running, fault, stopped, total: running + fault + stopped };
  };

  // 初始化主饼图
  const initMainPieChart = () => {
    const chartDom = mainPieChart.value;
    if (!chartDom) return;
    
    const stats = calculateDeviceStats();
    
    const myChart = echarts.init(chartDom);
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}台 ({d}%)',
        backgroundColor: 'rgba(0,0,0,0.7)',
        borderWidth: 0,
        textStyle: {
          color: '#fff'
        }
      },
      legend: {
        orient: 'vertical',
        left: 10,
        top: 'center',
        textStyle: {
          color: '#fff',
          fontSize: 14
        },
        itemGap: 20
      },
      series: [
        {
          name: '设备状态',
          type: 'pie',
          radius: ['50%', '70%'],
          center: ['60%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#0D274A',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold',
              color: '#fff'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            { value: stats.running, name: '在线', itemStyle: { color: '#52c41a' } },
            { value: stats.fault, name: '故障', itemStyle: { color: '#f5222d' } },
            { value: stats.stopped, name: '停止', itemStyle: { color: '#faad14' } }
          ]
        }
      ]
    };
    
    myChart.setOption(option);
    window.addEventListener('resize', () => {
      myChart.resize();
    });
    
    return myChart;
  };

  initMainPieChart();

  // 初始化状态图表
  const fogChart = initStatusChart(fogCannonChart, fogCannonStats.value, ['rgba(46, 204, 113, 0.8)', 'rgba(231, 76, 60, 0.8)', 'rgba(149, 165, 166, 0.8)']);
  const waterCanChart = initStatusChart(waterCannonChart, waterCannonStats.value, ['rgba(52, 152, 219, 0.8)', 'rgba(231, 76, 60, 0.8)', 'rgba(149, 165, 166, 0.8)']);
  const pumpChart = initStatusChart(waterPumpChart, waterPumpStats.value, ['rgba(155, 89, 182, 0.8)', 'rgba(231, 76, 60, 0.8)', 'rgba(149, 165, 166, 0.8)']);

  // 初始化折线图
  const flowChart = initLineChart(flowRateChart, 'rgba(52, 152, 219, 1)');
  const pressChart = initLineChart(pressureChart, 'rgba(46, 204, 113, 1)');
  const powChart = initLineChart(powerChart, 'rgba(155, 89, 182, 1)');

  // 初始化故障类型分析饼图
  const faultChart = initFaultTypeChart(faultTypeChart);

  // 初始化设备效率分析图表
  const efficiencyChart = initEfficiencyChart(deviceEfficiencyChart);

  // 初始化运行时间分析图表
  const operationChart = initOperationTimeChart(operationTimeChart);

  // 监听周期变化
  watch(selectedTrendPeriod, (newPeriod) => {
    // 重新初始化趋势图
    initDeviceStatusTrendChart(deviceStatusTrendChart, newPeriod);
  });

  // 窗口大小变化时重绘图表
  window.addEventListener('resize', handleResize);

  // 组件卸载时移除事件监听
  onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize);
  });

  // 定时更新数据
  setInterval(() => {
    // 更新流量
    totalFlowRate.value = Math.random() * 20 + 120;
    // 更新压力
    avgPressure.value = Math.random() * 1 + 3;
    // 更新功率
    totalPower.value = Math.random() * 10 + 40;

    // 更新图表数据
    const updateLineChart = (chart) => {
      const now = new Date();
      const newData = {
        name: now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', second: '2-digit' }),
        value: [now.getTime(), Math.random() * 10 + 30]
      };

      const option = chart.getOption();
      const data = [...option.series[0].data];
      data.shift();
      data.push(newData);

      chart.setOption({
        series: [{
          data: data
        }]
      });
    };

    updateLineChart(flowChart);
    updateLineChart(pressChart);
    updateLineChart(powChart);
  }, 3000);

};

// 初始化故障类型分析饼图
const initFaultTypeChart = (chartRef) => {
  const chartDom = chartRef.value;
  const myChart = echarts.init(chartDom);
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      textStyle: {
        color: '#FFFFFF'
      }
    },
    series: [
      {
        name: '故障类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#0A1929',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
          emphasis: {
            label: {
              show: true,
              fontSize: '16',
              fontWeight: 'bold',
              color: '#FFFFFF'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            { value: 12, name: '风机故障', itemStyle: { color: '#E74C3C' } },
            { value: 8, name: '水泵故障', itemStyle: { color: '#3498DB' } },
            { value: 6, name: '油泵故障', itemStyle: { color: '#F39C12' } },
            { value: 4, name: '超限保护', itemStyle: { color: '#9B59B6' } },
            { value: 3, name: '伽服故障', itemStyle: { color: '#1ABC9C' } }
          ]
        }
      ]
    };
    myChart.setOption(option);
    return myChart;
  };

  // 初始化设备效率分析图表
  const initEfficiencyChart = (chartRef) => {
    const chartDom = chartRef.value;
    const myChart = echarts.init(chartDom);
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: ['雾炮机1', '雾炮机2', '雾炮机3', '雾炮机4', '水炮机1', '水炮机2', '水泵1', '水泵2'],
          axisTick: {
            alignWithLabel: true
          },
          axisLine: {
            lineStyle: {
              color: '#999'
            }
          },
          axisLabel: {
            color: '#FFFFFF',
            rotate: 45
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '效率(%)',
          nameTextStyle: {
            color: '#FFFFFF'
          },
          min: 0,
          max: 100,
          axisLine: {
            lineStyle: {
              color: '#999'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          },
          axisLabel: {
            color: '#FFFFFF'
          }
        }
      ],
      series: [
        {
          name: '设备效率',
          type: 'bar',
          barWidth: '60%',
          data: [
            {
              value: 92,
              itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#83bff6' },
                { offset: 0.5, color: '#188df0' },
                { offset: 1, color: '#188df0' }
              ]) }
            },
            {
              value: 78,
              itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#83bff6' },
                { offset: 0.5, color: '#188df0' },
                { offset: 1, color: '#188df0' }
              ]) }
            },
            {
              value: 89,
              itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#83bff6' },
                { offset: 0.5, color: '#188df0' },
                { offset: 1, color: '#188df0' }
              ]) }
            },
            {
              value: 65,
              itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#83bff6' },
                { offset: 0.5, color: '#188df0' },
                { offset: 1, color: '#188df0' }
              ]) }
            },
            {
              value: 88,
              itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#2ecc71' },
                { offset: 0.5, color: '#27ae60' },
                { offset: 1, color: '#27ae60' }
              ]) }
            },
            {
              value: 79,
              itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#2ecc71' },
                { offset: 0.5, color: '#27ae60' },
                { offset: 1, color: '#27ae60' }
              ]) }
            },
            {
              value: 94,
              itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#9b59b6' },
                { offset: 0.5, color: '#8e44ad' },
                { offset: 1, color: '#8e44ad' }
              ]) }
            },
            {
              value: 82,
              itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#9b59b6' },
                { offset: 0.5, color: '#8e44ad' },
                { offset: 1, color: '#8e44ad' }
              ]) }
            }
          ]
        }
      ]
    };
    myChart.setOption(option);
    return myChart;
  };

  // 初始化运行时间分析图表
  const initOperationTimeChart = (chartRef) => {
    const chartDom = chartRef.value;
    const myChart = echarts.init(chartDom);
    const option = {
      tooltip: {
        trigger: 'item'
      },
      legend: {
        top: '5%',
        left: 'center',
        textStyle: {
          color: '#FFFFFF'
        }
      },
      series: [
        {
          name: '运行时间',
          type: 'pie',
          radius: ['30%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#0A1929',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '16',
              fontWeight: 'bold',
              color: '#FFFFFF'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            { value: 48, name: '8小时以上', itemStyle: { color: '#2ECC71' } },
            { value: 32, name: '4-8小时', itemStyle: { color: '#3498DB' } },
            { value: 12, name: '2-4小时', itemStyle: { color: '#F39C12' } },
            { value: 8, name: '2小时以下', itemStyle: { color: '#E74C3C' } }
          ]
        }
      ]
    };
    myChart.setOption(option);
    return myChart;
  };

  // 初始化设备状态变化趋势图表
  const initDeviceStatusTrendChart = (chartRef, period) => {
    const chartDom = chartRef.value;
    const myChart = echarts.init(chartDom);

    // 根据周期生成不同的数据
    const generateTrendData = (period) => {
      let dates = [];
      let runningData = [];
      let faultData = [];
      let stoppedData = [];

      const now = new Date();
      let dataPoints = 0;

      if (period === 'day') {
        dataPoints = 24; // 24小时
        for (let i = 0; i < dataPoints; i++) {
          const time = new Date(now);
          time.setHours(i, 0, 0, 0);
          dates.push(time.getHours() + ':00');
          runningData.push(Math.round(Math.random() * 5) + 5);
          faultData.push(Math.round(Math.random() * 2));
          stoppedData.push(Math.round(Math.random() * 3) + 1);
      }
      data.push({
        date: period === 'day' ? date.getHours() + ':00' : date.toLocaleDateString('zh-CN'),
        running: Math.floor(Math.random() * 10) + 1,
        fault: Math.floor(Math.random() * 5),
        stopped: Math.floor(Math.random() * 3)
      });
    }
    return data;
  };

  const data = generateData(period);

  // 准备数据
  const xAxisData = data.map(item => item.date);
  const runningData = data.map(item => item.running);
  const faultData = data.map(item => item.fault);
  const stoppedData = data.map(item => item.stopped);

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['运行中', '故障', '停机'],
      textStyle: {
        color: '#FFFFFF'
      },
      right: 10,
      top: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#999'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#999'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 10
      }
    },
    series: [
      {
        name: '运行中',
        type: 'bar',
        stack: 'total',
        emphasis: {
          focus: 'series'
        },
        itemStyle: {
          color: '#2ECC71'
        },
        data: runningData
      },
      {
        name: '故障',
        type: 'bar',
        stack: 'total',
        emphasis: {
          focus: 'series'
        },
        itemStyle: {
          color: '#E74C3C'
        },
        data: faultData
      },
      {
        name: '停机',
        type: 'bar',
        stack: 'total',
        emphasis: {
          focus: 'series'
        },
        itemStyle: {
          color: '#95A5A6'
        },
        data: stoppedData
      }
    ]
  };

  myChart.setOption(option);
  return myChart;
};

// 窗口大小变化时重绘图表（带防抖）
let resizeTimer = null;
const handleResize = () => {
  if (resizeTimer) clearTimeout(resizeTimer);
  resizeTimer = setTimeout(() => {
    // 基础图表
    if (deviceTypeChart) deviceTypeChart.resize();
    if (mapChart) mapChart.resize();
    
    // 其他图表
    const charts = [
      fogChart, waterCanChart, pumpChart, flowChart, 
      pressChart, powChart, faultChart, efficiencyChart, 
      operationChart, trendChart
    ];
    
    charts.forEach(chart => {
      if (chart && typeof chart.resize === 'function') {
        try {
          chart.resize();
        } catch (e) {
          console.error('图表重绘失败:', e);
        }
      }
    });
  }, 200); // 200ms 防抖
};

// 页面加载动画
const animatePageLoad = () => {
  // 卡片入场动画
  const cards = document.querySelectorAll('.status-card, .data-card');
  gsap.from(cards, {
    y: 30,
    opacity: 0,
    duration: 0.6,
    stagger: 0.1,
    ease: "power2.out"
  });

  // 标题动画
  const titles = document.querySelectorAll('.dashboard-title, .section-title');
  gsap.from(titles, {
    x: -20,
    opacity: 0,
    duration: 0.5,
    stagger: 0.1,
    delay: 0.3
  });

  // 警报动画
  const alertItems = document.querySelectorAll('.alert-item');
  gsap.from(alertItems, {
    x: 20,
    opacity: 0,
    duration: 0.5,
    stagger: 0.1,
    delay: 0.6
  });
};

onMounted(() => {
  // 检测屏幕尺寸
  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);
  
  // 初始化时间并每分钟更新
  updateDateTime();
  setInterval(updateDateTime, 60000);

  // 初始化饼图
  initMainPieChart();

  // 页面加载动画
  const timeline = gsap.timeline({
    defaults: { duration: 0.6, ease: "power2.out" }
  });

  timeline
    .from('.header-section', { y: -30, opacity: 0, clearProps: "all" })
    .from('.nav-section', { opacity: 0, y: 20, clearProps: "all" }, '-=0.3')
    .from('.left-panel', { opacity: 0, x: -20, clearProps: "all" }, '-=0.3')
    .from('.right-panel', { opacity: 0, x: 20, clearProps: "all" }, '-=0.3')
    .from('.footer-section', { y: 20, opacity: 0, clearProps: "all" }, '-=0.3');
});
</script>

<style scoped>
.bi-screen-container {
  background-color: #0D274A;
  color: #FFFFFF;
  min-height: 100vh;
  box-sizing: border-box;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  display: flex;
  flex-direction: column;
  border: 2px solid #1a3a8f;
}

/* 顶部标题栏 */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 50px;
  line-height: 50px;
  background: #fff;
  color: #000;
  border-bottom: 1px solid #ccc;
}

.header-left {
  display: flex;
  align-items: center;
}

.platform-title {
  font-size: 18px;
  font-weight: bold;
}

.environment-info {
  font-size: 14px;
}

/* 导航栏 */
.nav-section {
  background-color: #0D274A;
  padding: 10px;
  border-bottom: 1px solid #1a3a8f;
}

.device-nav {
  display: flex;
  gap: 10px;
}

.nav-item {
  padding: 5px 15px;
  background-color: #1a3a8f;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.nav-item.active {
  background-color: #2980b9;
  font-weight: bold;
}

/* 主内容区 */
.main-content {
  display: flex;
  flex: 1;
  padding: 10px;
  gap: 20px;
}

/* 左侧面板 - 饼图区域 */
.left-panel {
  flex: 0 0 40%;
  display: flex;
  flex-direction: column;
  border: 1px solid #1a3a8f;
  border-radius: 4px;
  padding: 10px;
  background-color: #0D274A;
}

.pie-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-area {
  width: 100%;
  height: 400px;
  max-width: 500px;
}

/* 右侧面板 - 设备卡片区域 */
.right-panel {
  flex: 0 0 60%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow-y: auto;
}

.device-card {
  background-color: #0D274A;
  border: 1px solid #1a3a8f;
  border-radius: 4px;
  padding: 15px;
  transition: all 0.3s ease;
}

.device-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-title {
  font-size: 16px;
  font-weight: bold;
}

.card-status {
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 12px;
}

.card-status.running {
  background-color: #52c41a;
  color: #fff;
}

.card-status.fault {
  background-color: #f5222d;
  color: #fff;
}

.card-status.stopped {
  background-color: #faad14;
  color: #fff;
}

.card-body {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
}

.card-controls {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.power-btn {
  font-size: 18px;
  height: 40px;
  width: 40px;
}

/* 状态概览 */
.status-overview {
  background: rgba(16, 31, 63, 0.8);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-top: 10px;
}

.status-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 12px;
  transition: all 0.3s;
}

.status-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-title {
  font-size: 16px;
  font-weight: 500;
}

.card-count {
  font-size: 14px;
  color: #1890ff;
  font-weight: bold;
}

.card-body {
  display: flex;
  justify-content: space-between;
  margin: 12px 0;
}

.status-item {
  text-align: center;
  flex: 1;
}

.status-label {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 5px;
}

.status-value {
  font-size: 18px;
  font-weight: bold;
}

.running { color: #52c41a; }
.fault { color: #f5222d; }
.stopped { color: #faad14; }

.chart-container {
  height: 80px;
  margin-top: 10px;
}

/* 实时监控 */
.realtime-monitor {
  flex: 1;
  background: rgba(16, 31, 63, 0.8);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.monitor-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-top: 10px;
  flex: 1;
}

.monitor-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.monitor-title {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 5px;
}

.monitor-value {
  font-size: 24px;
  font-weight: bold;
  margin: 10px 0;
  flex: 1;
  display: flex;
  align-items: center;
}

.unit {
  font-size: 14px;
  color: #8c8c8c;
  margin-left: 5px;
}

.monitor-chart {
  height: 60px;
  margin-top: 10px;
}

/* 控制面板 */
.control-panel {
  background: rgba(16, 31, 63, 0.8);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.control-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.control-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.control-tabs :deep(.ant-tabs-nav) {
  margin: 0;
}

.control-tabs :deep(.ant-tabs-content) {
  flex: 1;
  overflow: auto;
  padding-top: 15px;
}

/* 分析面板 */
.analysis-panel {
  background: rgba(16, 31, 63, 0.8);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.analysis-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 10px;
}

.analysis-chart {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  padding: 10px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chart-title {
  font-size: 13px;
  color: #8c8c8c;
  margin-bottom: 10px;
}

/* 底部状态栏 */
.footer-section {
  background: rgba(16, 31, 63, 0.8);
  border-radius: 8px;
  margin: 0 15px 15px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}

.status-trend {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.trend-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.trend-title {
  font-size: 16px;
  font-weight: 500;
}

.trend-chart {
  flex: 1;
  min-height: 200px;
}

/* 底部状态表格 */
.footer-section {
  padding: 10px;
  margin-top: auto;
  background-color: #0D274A;
}

.device-status-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #1a3a8f;
  background-color: #0D274A;
}

.table-header {
  display: flex;
  background-color: #1a3a8f;
  color: white;
  font-weight: bold;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #1a3a8f;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  flex: 1;
  padding: 8px 12px;
  text-align: center;
  border-right: 1px solid #1a3a8f;
}

.table-cell:last-child {
  border-right: none;
}

.device-name {
  text-align: left;
  font-weight: bold;
  background-color: #0D274A;
}

/* 公共样式 */
.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #fff;
  margin: 0 0 15px 0;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }
  
  .left-panel,
  .right-panel {
    flex: none;
    width: 100%;
  }
  
  .status-cards,
  .monitor-cards {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .status-cards,
  .monitor-cards {
    grid-template-columns: 1fr;
  }
  
  .header-section {
    padding: 0 10px;
  }
  
  .main-content {
    padding: 10px;
  }
  
  .footer-section {
    margin: 0 10px 10px;
  }
}
</style>