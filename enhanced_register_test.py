# -*- coding: utf-8 -*-
"""
雾炮机寄存器测试脚本
使用4x寄存器映射模式 (地址-40001)
支持16位和32位寄存器的有符号/无符号整数读写
特别支持PLC VD信号地址的32位数据处理
"""

from pymodbus.client import ModbusTcpClient
from pymodbus.exceptions import ModbusException
import time

class MistCannonTester:
    def __init__(self, host, port=502, unit_id=1):
        self.host = host
        self.port = port
        self.unit_id = unit_id
        self.client = ModbusTcpClient(host, port=port)
        
    def connect(self):
        result = self.client.connect()
        if result:
            print(f"✓ 成功连接到雾炮机: {self.host}:{self.port}")
            return True
        else:
            print(f"✗ 连接失败: {self.host}:{self.port}")
            return False
    
    def to_signed_int16(self, unsigned_value):
        """将无符号16位整数转换为有符号16位整数"""
        if unsigned_value > 32767:
            return unsigned_value - 65536
        return unsigned_value
    
    def from_signed_int16(self, signed_value):
        """将有符号16位整数转换为无符号16位整数"""
        if signed_value < 0:
            return signed_value + 65536
        return signed_value
    
    def to_signed_int32(self, high_word, low_word):
        """将两个16位字组合为32位有符号整数"""
        unsigned_value = (high_word << 16) | low_word
        if unsigned_value > 2147483647:  # 2^31 - 1
            return unsigned_value - 4294967296  # 2^32
        return unsigned_value
    
    def from_signed_int32(self, signed_value):
        """将32位有符号整数转换为两个16位字 (高字, 低字)"""
        if signed_value < 0:
            unsigned_value = signed_value + 4294967296  # 2^32
        else:
            unsigned_value = signed_value
        
        high_word = (unsigned_value >> 16) & 0xFFFF
        low_word = unsigned_value & 0xFFFF
        return high_word, low_word
    
    def get_modbus_address(self, register_addr):
        """将4x寄存器地址转换为Modbus地址"""
        return register_addr - 40001
    
    def read_register(self, register_addr):
        """读取单个寄存器"""
        modbus_addr = self.get_modbus_address(register_addr)
        
        if modbus_addr < 0:
            print(f"✗ 寄存器地址{register_addr}无效 (Modbus地址: {modbus_addr} < 0)")
            return None
        
        try:
            rr = self.client.read_holding_registers(address=modbus_addr, count=1, slave=self.unit_id)
            
            if rr.isError():
                print(f"✗ 读取寄存器{register_addr}失败")
                return None
            else:
                value = rr.registers[0]
                signed_value = self.to_signed_int16(value)
                print(f"寄存器{register_addr}: {value} (无符号) / {signed_value} (有符号) / 0x{value:04X}")
                return value
                
        except Exception as e:
            print(f"✗ 读取寄存器{register_addr}异常: {e}")
            return None
    
    def read_register_32bit(self, register_addr):
        """读取32位寄存器 (两个连续的16位寄存器)"""
        modbus_addr = self.get_modbus_address(register_addr)
        
        if modbus_addr < 0:
            print(f"✗ 寄存器地址{register_addr}无效 (Modbus地址: {modbus_addr} < 0)")
            return None
        
        try:
            # 读取两个连续的寄存器
            rr = self.client.read_holding_registers(address=modbus_addr, count=2, slave=self.unit_id)
            
            if rr.isError():
                print(f"✗ 读取32位寄存器{register_addr}失败")
                return None
            else:
                high_word = rr.registers[0]  # 高字
                low_word = rr.registers[1]   # 低字
                
                # 组合为32位值
                unsigned_32 = (high_word << 16) | low_word
                signed_32 = self.to_signed_int32(high_word, low_word)
                
                print(f"32位寄存器{register_addr}-{register_addr+1}:")
                print(f"  高字: {high_word} (0x{high_word:04X})")
                print(f"  低字: {low_word} (0x{low_word:04X})")
                print(f"  32位值: {unsigned_32} (无符号) / {signed_32} (有符号) / 0x{unsigned_32:08X}")
                return unsigned_32
                
        except Exception as e:
            print(f"✗ 读取32位寄存器{register_addr}异常: {e}")
            return None
    
    def read_range(self, start_reg, end_reg):
        """读取寄存器范围"""
        print(f"\n--- 读取寄存器范围 ({start_reg}-{end_reg}) ---")
        
        try:
            # 确保范围正确
            if start_reg > end_reg:
                start_reg, end_reg = end_reg, start_reg
                print(f"自动调整范围为: {start_reg}-{end_reg}")
            
            start_addr = self.get_modbus_address(start_reg)
            count = end_reg - start_reg + 1
            
            # 检查地址范围
            if start_addr < 0:
                print(f"地址超出范围: 起始地址 {start_addr} < 0")
                print("提示: 40001对应Modbus地址0，请使用40001或更大的寄存器地址")
                return
            
            print(f"Modbus地址范围: {start_addr} - {start_addr + count - 1}")
            
            rr = self.client.read_holding_registers(address=start_addr, count=count, slave=self.unit_id)
            
            if not rr.isError():
                for i, value in enumerate(rr.registers):
                    reg_num = start_reg + i
                    signed_value = self.to_signed_int16(value)
                    print(f"寄存器{reg_num}: {value} (无符号) / {signed_value} (有符号) / 0x{value:04X}")
            else:
                print(f"读取范围失败: {rr}")
                
        except Exception as e:
            print(f"读取范围异常: {e}")
    
    def write_register(self, register_addr, value, signed=False):
        """写入寄存器值"""
        print(f"\n--- 写入寄存器{register_addr} ---")
        
        # 处理有符号值
        if signed and value < 0:
            write_value = self.from_signed_int16(value)
            print(f"有符号值 {value} 转换为无符号值 {write_value}")
        else:
            write_value = value
        
        modbus_addr = self.get_modbus_address(register_addr)
        
        if modbus_addr < 0:
            print(f"✗ 寄存器地址{register_addr}无效 (Modbus地址: {modbus_addr} < 0)")
            return False
        
        try:
            print(f"使用4x寄存器映射，Modbus地址: {modbus_addr}")
            
            # 先读取写入前的值
            print("📖 读取写入前的值...")
            rr_before = self.client.read_holding_registers(address=modbus_addr, count=1, slave=self.unit_id)
            if not rr_before.isError():
                before_value = rr_before.registers[0]
                before_signed = self.to_signed_int16(before_value)
                print(f"  写入前: {before_value} (无符号) / {before_signed} (有符号) / 0x{before_value:04X}")
            else:
                print("  ❌ 无法读取写入前的值，停止写入操作")
                return False
            
            print(f"📝 准备写入值: {write_value} (0x{write_value:04X})")
            
            # 写入寄存器
            print("✍️ 执行写入操作...")
            wr = self.client.write_register(address=modbus_addr, value=write_value, slave=self.unit_id)
            
            if wr.isError():
                print(f"✗ 写入失败: {wr}")
                return False
            else:
                print("✓ 写入命令发送成功")
                
                # 等待一小段时间再读取验证
                time.sleep(0.1)
                
                # 读取验证
                print("📖 读取写入后的值...")
                rr_after = self.client.read_holding_registers(address=modbus_addr, count=1, slave=self.unit_id)
                if not rr_after.isError():
                    after_value = rr_after.registers[0]
                    after_signed = self.to_signed_int16(after_value)
                    print(f"  写入后: {after_value} (无符号) / {after_signed} (有符号) / 0x{after_value:04X}")
                    
                    # 比较写入前后的值
                    if before_value == after_value:
                        print("⚠ 写入前后值相同，寄存器可能是只读的")
                    else:
                        print(f"✓ 寄存器值已改变: {before_value} → {after_value}")
                    
                    # 比较期望值和实际值
                    expected_unsigned = write_value & 0xFFFF  # 确保是16位
                    
                    if after_value == expected_unsigned:
                        print("✅ 写入值验证成功")
                        return True
                    else:
                        print(f"❌ 写入值验证失败: 期望{expected_unsigned}, 实际{after_value}")
                        
                        # 分析可能的原因
                        if before_value == after_value:
                            print("  🔒 可能原因: 寄存器是只读的或有写保护")
                        elif after_value == 65535:
                            print("  🔒 可能原因: 寄存器被重置为默认值65535")
                        elif after_value == 0:
                            print("  🔒 可能原因: 寄存器被重置为默认值0")
                        else:
                            print("  ❓ 可能原因: 设备内部逻辑修改了写入值")
                        
                        return False
                else:
                    print("⚠ 无法验证写入结果")
                    return False
                    
        except Exception as e:
            print(f"✗ 写入异常: {e}")
            return False
    
    def write_register_32bit(self, register_addr, value, signed=False):
        """写入32位寄存器值 (写入两个连续的16位寄存器)"""
        print(f"\n--- 写入32位寄存器{register_addr}-{register_addr+1} ---")
        
        # 处理有符号值
        if signed and value < 0:
            if value < -2147483648 or value > 2147483647:
                print(f"✗ 32位有符号值超出范围: {value} (范围: -2147483648 到 2147483647)")
                return False
            high_word, low_word = self.from_signed_int32(value)
            print(f"有符号32位值 {value} 转换为高字: {high_word}, 低字: {low_word}")
        else:
            if value < 0 or value > 4294967295:
                print(f"✗ 32位无符号值超出范围: {value} (范围: 0 到 4294967295)")
                return False
            high_word = (value >> 16) & 0xFFFF
            low_word = value & 0xFFFF
            print(f"无符号32位值 {value} 分解为高字: {high_word}, 低字: {low_word}")
        
        modbus_addr = self.get_modbus_address(register_addr)
        
        if modbus_addr < 0:
            print(f"✗ 寄存器地址{register_addr}无效 (Modbus地址: {modbus_addr} < 0)")
            return False
        
        try:
            print(f"使用4x寄存器映射，Modbus地址: {modbus_addr}-{modbus_addr+1}")
            
            # 先读取写入前的值
            print("📖 读取写入前的值...")
            rr_before = self.client.read_holding_registers(address=modbus_addr, count=2, slave=self.unit_id)
            if not rr_before.isError():
                before_high = rr_before.registers[0]
                before_low = rr_before.registers[1]
                before_32 = (before_high << 16) | before_low
                before_signed_32 = self.to_signed_int32(before_high, before_low)
                print(f"  写入前32位值: {before_32} (无符号) / {before_signed_32} (有符号) / 0x{before_32:08X}")
                print(f"  高字: {before_high} (0x{before_high:04X}), 低字: {before_low} (0x{before_low:04X})")
            else:
                print("  ❌ 无法读取写入前的值，停止写入操作")
                return False
            
            print(f"📝 准备写入32位值: {value} (0x{value:08X})")
            print(f"  高字: {high_word} (0x{high_word:04X}), 低字: {low_word} (0x{low_word:04X})")
            
            # 写入两个寄存器
            print("✍️ 执行写入操作...")
            wr = self.client.write_registers(address=modbus_addr, values=[high_word, low_word], slave=self.unit_id)
            
            if wr.isError():
                print(f"✗ 写入失败: {wr}")
                return False
            else:
                print("✓ 写入命令发送成功")
                
                # 等待一小段时间再读取验证
                time.sleep(0.1)
                
                # 读取验证
                print("📖 读取写入后的值...")
                rr_after = self.client.read_holding_registers(address=modbus_addr, count=2, slave=self.unit_id)
                if not rr_after.isError():
                    after_high = rr_after.registers[0]
                    after_low = rr_after.registers[1]
                    after_32 = (after_high << 16) | after_low
                    after_signed_32 = self.to_signed_int32(after_high, after_low)
                    print(f"  写入后32位值: {after_32} (无符号) / {after_signed_32} (有符号) / 0x{after_32:08X}")
                    print(f"  高字: {after_high} (0x{after_high:04X}), 低字: {after_low} (0x{after_low:04X})")
                    
                    # 比较写入前后的值
                    if before_32 == after_32:
                        print("⚠ 写入前后值相同，寄存器可能是只读的")
                    else:
                        print(f"✓ 寄存器值已改变: {before_32} → {after_32}")
                    
                    # 比较期望值和实际值
                    expected_32 = value & 0xFFFFFFFF  # 确保是32位
                    
                    if after_32 == expected_32:
                        print("✅ 32位写入值验证成功")
                        return True
                    else:
                        print(f"❌ 32位写入值验证失败: 期望{expected_32}, 实际{after_32}")
                        
                        # 分析可能的原因
                        if before_32 == after_32:
                            print("  🔒 可能原因: 寄存器是只读的或有写保护")
                        else:
                            print("  ❓ 可能原因: 设备内部逻辑修改了写入值")
                        
                        return False
                else:
                    print("⚠ 无法验证写入结果")
                    return True
                    
        except Exception as e:
            print(f"✗ 32位写入异常: {e}")
            return False
    
    def interactive_read(self):
        """交互式读取"""
        print("\n--- 交互式寄存器读取 ---")
        
        while True:
            try:
                print("\n选择读取方式:")
                print("1. 读取单个16位寄存器")
                print("2. 读取单个32位寄存器 (VD地址)")
                print("3. 读取寄存器范围")
                print("0. 返回主菜单")
                
                choice = input("请选择 (0-3): ").strip()
                
                if choice == '0':
                    break
                elif choice == '1':
                    reg_input = input("输入寄存器地址 (如40053): ").strip()
                    register_addr = int(reg_input)
                    self.read_register(register_addr)
                elif choice == '2':
                    reg_input = input("输入32位寄存器起始地址 (如40053, 将读取40053-40054): ").strip()
                    register_addr = int(reg_input)
                    self.read_register_32bit(register_addr)
                elif choice == '3':
                    start_input = input("输入起始寄存器地址 (如40010): ").strip()
                    start_reg = int(start_input)
                    
                    end_input = input("输入结束寄存器地址 (如40020): ").strip()
                    end_reg = int(end_input)
                    
                    self.read_range(start_reg, end_reg)
                else:
                    print("无效选择，请重新输入")
                    
            except ValueError:
                print("输入格式错误，请输入数字")
            except KeyboardInterrupt:
                print("\n退出读取")
                break
            except Exception as e:
                print(f"操作异常: {e}")
    
    def interactive_write(self):
        """交互式写入"""
        print("\n--- 交互式寄存器写入 ---")
        
        while True:
            try:
                print("\n选择写入方式:")
                print("1. 写入16位寄存器")
                print("2. 写入32位寄存器 (VD地址)")
                print("0. 返回主菜单")
                
                choice = input("请选择 (0-2): ").strip()
                
                if choice == '0':
                    break
                elif choice == '1':
                    self._write_16bit_register()
                elif choice == '2':
                    self._write_32bit_register()
                else:
                    print("无效选择，请重新输入")
                    
            except KeyboardInterrupt:
                print("\n退出写入")
                break
            except Exception as e:
                print(f"操作异常: {e}")
    
    def _write_16bit_register(self):
        """写入16位寄存器的具体实现"""
        try:
            # 获取寄存器地址
            reg_input = input("\n输入16位寄存器地址 (如40053): ").strip()
            register_addr = int(reg_input)
            
            # 选择数据类型
            data_type = input("数据类型 (u=无符号, s=有符号) [u]: ").strip().lower()
            signed = (data_type == 's')
            
            # 获取要写入的值
            if signed:
                value_input = input("输入要写入的值 (-32768 到 32767): ").strip()
                value = int(value_input)
                if value < -32768 or value > 32767:
                    print("有符号值超出范围 (-32768 到 32767)")
                    return
            else:
                value_input = input("输入要写入的值 (0-65535): ").strip()
                value = int(value_input)
                if value < 0 or value > 65535:
                    print("无符号值超出范围 (0-65535)")
                    return
            
            # 执行写入
            success = self.write_register(register_addr, value, signed)
            
            if success:
                print("✓ 16位写入操作完成")
            else:
                print("✗ 16位写入操作失败")
                
        except ValueError:
            print("输入格式错误，请输入数字")
    
    def _write_32bit_register(self):
        """写入32位寄存器的具体实现"""
        try:
            # 获取寄存器地址
            reg_input = input("\n输入32位寄存器起始地址 (如40053, 将写入40053-40054): ").strip()
            register_addr = int(reg_input)
            
            # 选择数据类型
            data_type = input("数据类型 (u=无符号, s=有符号) [s]: ").strip().lower()
            signed = (data_type != 'u')  # 默认为有符号
            
            # 获取要写入的值
            if signed:
                value_input = input("输入要写入的32位有符号值 (-2147483648 到 2147483647): ").strip()
                value = int(value_input)
                if value < -2147483648 or value > 2147483647:
                    print("32位有符号值超出范围 (-2147483648 到 2147483647)")
                    return
            else:
                value_input = input("输入要写入的32位无符号值 (0 到 4294967295): ").strip()
                value = int(value_input)
                if value < 0 or value > 4294967295:
                    print("32位无符号值超出范围 (0 到 4294967295)")
                    return
            
            # 执行写入
            success = self.write_register_32bit(register_addr, value, signed)
            
            if success:
                print("✓ 32位写入操作完成")
            else:
                print("✗ 32位写入操作失败")
                
        except ValueError:
            print("输入格式错误，请输入数字")
    
    def close(self):
        self.client.close()
        print("\n连接已关闭")

def main():
    mist_cannon_ip = "*************"
    
    print("==============================================")
    print("     雾炮机寄存器测试脚本")
    print("     4x寄存器映射模式 (地址-40001)")
    print("     支持16位和32位寄存器读写")
    print("     特别支持PLC VD信号地址")
    print("==============================================")
    
    tester = MistCannonTester(mist_cannon_ip)
    
    try:
        if not tester.connect():
            return
        
        while True:
            print("\n--- 主菜单 ---")
            print("1. 交互式读取寄存器")
            print("2. 交互式写入寄存器")
            print("0. 退出程序")
            
            choice = input("请选择功能 (0-2): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                tester.interactive_read()
            elif choice == '2':
                tester.interactive_write()
            else:
                print("无效选择，请重新输入")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试异常: {e}")
    finally:
        tester.close()

if __name__ == "__main__":
    main()