<template>
  <div class="content">
    <div class="header">
      <!-- 自定义UI可拖拽元素样式 -->
      <!-- <div class="item ep-draggable-item" tid="defaultModule.text">文本</div>
       <div class="item" tid="defaultModule.table">表格</div>
       <div class="item ep-draggable-item" tid="defaultModule.oval">椭圆</div> -->
      <span>&nbsp;&nbsp;</span>
      <el-button type="primary" plain icon="el-icon-refresh-right" @click="rotatePaper">旋转</el-button>
      <el-button type="primary" @click="print">浏览器打印</el-button>
      <!-- <el-button type="success" @click="print2">直接打印</el-button> -->
      <el-button type="success" @click="getPDF('pdfobjectnewwindow')">预览</el-button>
      <!-- <el-button type="info" @click="exportJson">导出模板数据</el-button> -->
      <!-- <el-button type="warning" @click="importJson(templateStyleData)">导入模板数据</el-button> -->
      <el-button type="danger" @click="clearPaper">清空</el-button>
      <el-button type="warning" @click="open = true;">{{ form.id ? '修改模板' : '保存为模板' }}</el-button>
      <el-button @click="show(printData)">测试按钮</el-button>
    </div>
    <div class="panelsBoxContainer">
      <div class="pageSize">
        <!-- 纸张设置 -->
        <el-button-group>
          <template v-for="(value, type, index) in paperTypes">
            <el-button :type="curPaperType === type ? 'primary' : ''" @click="setPaper(type, value)">
              {{ type }}
            </el-button>
          </template>
          <el-popover v-model="paperPopVisible" placement="top" title="" trigger="click">
            <p style="margin-left: 20px;">设置纸张宽高(mm)</p>
            <div class="pageSizeChoose">
              <el-input type="number" v-model="form.paperWidth" style=" width: 100px; text-align: center"
                placeholder="宽(mm)" />
              ~
              <el-input type="number" v-model="form.paperHeight"
                style="width: 100px; text-align: center; border-left: 0" placeholder="高(mm)" />
            </div>
            <el-button type="primary" style="width: 100%" @click="otherPaper" class="pageSizeChooseSub">确定</el-button>
            <el-button slot="reference" :type="'other' == curPaperType ? 'primary' : ''">自定义纸张</el-button>
          </el-popover>
        </el-button-group>
      </div>
      <div id="panelsBox"></div>

    </div>
    <div class="main">

      <!-- 默认的provider，需要添加 rect-printElement-types -->
      <div class="left rect-printElement-types" id="providerBox"></div>
      <el-row>
        <el-col :span="18">
          <!-- <div class="grid-content bg-purple"></div> -->
          <div class="center" id="designerBox"></div>
        </el-col>
        <el-col :span="6">
          <!-- <div class="grid-content bg-purple-light"></div> -->
          <div class="right" id="optionsBox"></div>
        </el-col>
      </el-row>
    </div>

    <!-- 添加或修改打印模板对话框 -->
    <el-dialog v-if="open" :title="id ? '修改模板' : '保存为模板'" :visible.sync="open" append-to-body width="400px"
      :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" :inline="true">
        <el-form-item label="模板名称：" prop="name">
          <el-input v-model="form.name" placeholder="请输入模板名称" style="width: 217px" />
        </el-form-item>
        <el-form-item label="备注：" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" style="width: 217px" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="loading">确 定</el-button>
        <el-button @click="cancel" :loading="loading">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 预览的dialog -->
    <el-dialog :visible.sync="visible" append-to-body :close-on-click-modal="false" :close-on-press-escape="false"
      title="打印预览" :width="+form.paperWidth + 50 + 'mm'" top="5vh">
      <div style="padding-bottom: 20px;border-bottom: 1px solid #ccc;">
        <el-button :loading="waitShowPrinter" type="primary" icon="el-icon-printer" @click.stop="print">打印</el-button>
        <el-button :loading="waitShowPrinter" type="primary" icon="el-icon-tickets" @click.stop="getPDF">pdf</el-button>

      </div>
      <div v-if="spinning" :style="{width:+form.paperWidth + 10 + 'mm'}" style="height: 60vh;margin: 0 auto;overflow-y: auto;">
        <div id="preview_content_design"></div>
      </div>
      <div slot="footer" class="dialog-footer" align="right" style="padding-top: 20px;border-top: 1px solid #ccc;">
        <el-button @click="hideModal">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 预览 -->
    <!-- <print-preview ref="preView" /> -->

  </div>
</template>

<script>
// import "sv-print/dist/style.css";
import { hiprint, defaultElementTypeProvider, disAutoConnect } from 'vue-plugin-hiprint';
import printPreview from './preview'
import { listPrintTemplateGet, listPrintTemplatePost, getPrintTemplate, delPrintTemplate, addPrintTemplate, updatePrintTemplate } from "@/api/system/printTemplate";
export default {
  props: {
    transferData: {
      type: Object,
      default: () => { return {} }
    },
    // id: {
    //   type: [Number, String],
    //   default: () => { return null }
    // }
  },
  components: { printPreview },
  name: 'test',
  data() {
    return {
      hiprintTemplate: undefined, // 设计器容器
      // 当前纸张
      curPaper: {
        type: 'other',
        width: 220,
        height: 80
      },
      // 纸张类型
      paperTypes: {
        'A3': {
          width: 420,
          height: 296.6
        },
        'A4': {
          width: 210,
          height: 296.6
        },
        'A5': {
          width: 210,
          height: 147.6
        },
        'B3': {
          width: 500,
          height: 352.6
        },
        'B4': {
          width: 250,
          height: 352.6
        },
        'B5': {
          width: 250,
          height: 175.6
        }
      },
      // 自定义纸张
      paperPopVisible: false,
      paperWidth: '220',
      paperHeight: '80',
      lastjson: '',
      modelData: '~',

      templateStyleData: { "panels": [{ "name": null, "height": 80, "width": 220, "paperHeader": 0, "paperFooter": 226.7716535433071, "printElements": [{ "options": { "left": 42, "top": 51, "height": 58.5, "width": 550, "field": "table", "coordinateSync": false, "widthHeightSync": false, "columns": [[{ "width": 275, "title": "姓名\n", "field": "name", "checked": true, "columnId": "name", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0, "tableSummaryTitle": true, "tableSummary": "" }, { "width": 275, "title": "年龄", "field": "age", "checked": true, "columnId": "age", "fixed": false, "rowspan": 1, "colspan": 1, "align": "center", "tableQRCodeLevel": 0, "tableSummaryTitle": true, "tableSummary": "" }]] }, "printElementType": { "title": "空白表格", "type": "table", "editable": true, "columnDisplayEditable": true, "columnDisplayIndexEditable": true, "columnTitleEditable": true, "columnResizable": true, "columnAlignEditable": true, "isEnableEditField": true, "isEnableContextMenu": true, "isEnableInsertRow": true, "isEnableDeleteRow": true, "isEnableInsertColumn": true, "isEnableDeleteColumn": true, "isEnableMergeCell": true } }], "paperNumberLeft": null, "paperNumberTop": null, "paperNumberContinue": true, "watermarkOptions": {} }] },
      open: false, // 是否打开dialog框
      form: {
        name: undefined,
        remark: undefined,
        content: undefined,
        id: undefined,
        paperWidth: '220',
        paperHeight: '80',
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: "模板名称不能为空", trigger: "blur" }
        ],
      },
      loading: false,
      id: this.$route.query.id,

      // 预览dialog的属性
      visible: false, // 是否展示dialog框
      spinning: false, // 是否展示实例
      waitShowPrinter: false,
      printData: {
        table: [
          { name: '张三1', age: 10 },
          { name: '张三2', age: 11 },
          { name: '张三3', age: 12 },
          { name: '张三4', age: 13 },
          { name: '张三5', age: 14 },
          { name: '张三6', age: 15 },
          { name: '张三7', age: 16 },
          { name: '张三8', age: 17 },
          { name: '张三9', age: 18 },
          { name: '张三10', age: 92220 },
          { name: '张三11', age: 22220 },
          { name: '张三12', age: 21222 },
          { name: '张三13', age: 22222 },
          { name: '张三14', age: 22223 },
          { name: '张三15', age: 24222 },
          { name: '张三16', age: 22225 },
        ], name: '李四'
      }

    }
  },

  computed: {
    // 计算纸张的大小
    curPaperType() {
      let type = 'other'
      let types = this.paperTypes
      for (const key in types) {
        let item = types[key]
        let { width, height } = this.curPaper
        if (item.width === width && item.height === height) {
          type = key
        }
      }
      return type
    }
  },

  created() {
    hiprint.init({
      providers: [new defaultElementTypeProvider()]
    })
  },

  mounted() {
    this.buildByHtml();
    this.buildByProvider();
    this.buildDesigner();
    this.otherPaper();
    disAutoConnect(); // 关闭webscokes
  },

  watch: {
    'id': {
      async handler(newVal) {
        if (newVal) {
          const res = await getPrintTemplate(newVal);
          // this.form.content = res.data.content;
          // this.form.name = res.data.name;
          // this.form.remark = res.data.remark;
          // this.form.id = res.data.id;
          // this.form.createTime = res.data.createTime;
          // this.form.createBy = res.data.createBy;
          this.form = res.data;
          // 导入模板
          this.hiprintTemplate.update(JSON.parse(this.form.content));
          console.log(this.form, '查看数据啊');
          this.otherPaper();
        }
      },
      immediate: true
    }
  },

  methods: {
    buildByHtml() {
      hiprint.PrintElementTypeManager.buildByHtml($(".ep-draggble-item"));
    },

    buildByProvider() {
      $("#providerBox").empty(); // 先清空，避免重复构建
      hiprint.PrintElementTypeManager.build($("#providerBox"), "defaultModule");
    },

    // 构建设计器区域
    buildDesigner() {
      $("#designerBox").empty(); // 先清空，避免重复构建
      this.hiprintTemplate = new hiprint.PrintTemplate({
        template: {
          "panels": [{
            "options": { "left": 60, "top": 50, "height": 13, "width": 52, "title": "页眉线", "textAlign": "center" },
          }],
        },
        settingContainer: '#optionsBox', // 元素参数容器
        paginationContainer: '#panelsBox', // 多面板操作的容器
      });
      // 构建 并填充到容器中
      this.hiprintTemplate.design('#designerBox', { grid: true });

    },

    // 打印数据
    print() {
      this.waitShowPrinter = true;
      // let printData = { name: 'Ccxxxxxx', age: 12, n1: '张三', n2: '李四', n3: '王五' };
      let printData = {};
      if (this.transferData.showData) {
        printData = this.transferData.showData
      } else {
        printData = {
          table: [
            { name: '张三1', age: 10 },
            { name: '张三2', age: 11 },
            { name: '张三3', age: 12 },
            { name: '张三4', age: 13 },
            { name: '张三5', age: 14 },
            { name: '张三6', age: 15 },
            { name: '张三7', age: 16 },
            { name: '张三8', age: 17 },
            { name: '张三9', age: 18 },
            { name: '张三10', age: 92220 },
            { name: '张三11', age: 22220 },
            { name: '张三12', age: 21222 },
            { name: '张三13', age: 22222 },
            { name: '张三14', age: 22223 },
            { name: '张三15', age: 24222 },
            { name: '张三16', age: 22225 },
          ], name: '李四'
        }
      }
      this.hiprintTemplate.print(printData); // 这里是替换数据的
      this.waitShowPrinter = false;
    },

    print2() {
      // 打印数据，key对应 元素的 字段名
      let printData = { name: 'Cx222222222222' };
      // 调用浏览器打印
      this.hiprintTemplate.print2(printData, { printer: '打印机名称' });
      this.hiprintTemplate.on("printSuccess", function (e) {
        alert('直接打印完成', e);
      });
      this.hiprintTemplate.on("printError", function (e) {
        alert('直接打印失败', e);
      })
    },

    // 多模板预览
    async getPDF(type) {
      // 获取打印的数据
      let printData = {
        table: [
          { name: '张三1', age: 10 },
          { name: '张三2', age: 11 },
          { name: '张三3', age: 12 },
          { name: '张三4', age: 13 },
          { name: '张三5', age: 14 },
          { name: '张三6', age: 15 },
          { name: '张三7', age: 16 },
          { name: '张三8', age: 17 },
          { name: '张三9', age: 18 },
          { name: '张三10', age: 90 },
          { name: '张三11', age: 20 },
          { name: '张三12', age: 21 },
          { name: '张三13', age: 22 },
          { name: '张三14', age: 23 },
          { name: '张三15', age: 24 },
          { name: '张三16', age: 25 },
        ]
      };
      this.waitShowPrinter = true;
      // 调用预览
      const res = await this.hiprintTemplate.toPdf(printData, '测试的文件名称', { isDownload: false, type: 'pdfobjectnewwindow' });
      this.waitShowPrinter = false;
    },

    // 导出json数据
    exportJson() {
      let json = this.hiprintTemplate.getJson();
      this.templateStyleData = json;
    },

    // 假设导入的json对象数据
    importJson(obj) {
      // let json = this.hiprintTemplate.getJson();
      // console.log('json', json);

      this.hiprintTemplate.update(obj);
    },

    /**
    * 设置纸张大小
    * @param type [A3, A4, A5, B3, B4, B5, other]
    * @param value {width,height} mm
    */
    setPaper(type, value) {
      this.form.paperWidth = value.width;
      this.form.paperHeight = value.height;
      try {
        if (Object.keys(this.paperTypes).includes(type)) {
          this.curPaper = { type: type, width: value.width, height: value.height }
          this.hiprintTemplate.setPaper(value.width, value.height)
        } else {
          this.curPaper = { type: 'other', width: value.width, height: value.height }
          this.hiprintTemplate.setPaper(value.width, value.height)
        }
      } catch (error) {
        this.$message.error(`操作失败: ${error}`)
      }
    },
    // 自定义纸张大小
    otherPaper() {
      let value = {}
      value.width = this.form.paperWidth
      value.height = this.form.paperHeight
      this.paperPopVisible = false
      this.setPaper('other', value)
    },

    // 清空模板
    clearPaper() {
      this.$modal.confirm('是否清空画布?').then(function () {
      }).then(() => {
        this.hiprintTemplate.clear();
        this.$modal.msgSuccess("清空成功");
      }).catch((error) => {
        this.$message.error(`清空失败: ${error}`);
      });
    },

    // 旋转事件
    rotatePaper() {
      if (this.hiprintTemplate) {
        this.hiprintTemplate.rotatePaper()
      }
    },

    // 保存为模板
    async submitForm() {

      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.content = JSON.stringify(this.hiprintTemplate.getJson());
          console.log(this.form);
          if (this.form.id) {
            updatePrintTemplate(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
            });
          } else {
            addPrintTemplate(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
            });
          };
          this.$router.push({ path: '/system/printTemplate' });
        }
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
    },


    /**
     * this.$refs.preView.show的参数说明
     * @param this.hiprintTemplate: hiprintTemplate的实例
     * @param printData: 要展示的数据
     * @returns 
     */
    // 测试按钮
    testButton() {
      this.visible = true;
      return;
      console.log(this.hiprintTemplate.editingPanel, '++++++', this.hiprintTemplate.editingPanel.width);
      let printData = {
        table: [
          { name: '张三1', age: 10 },
          { name: '张三2', age: 11 },
          { name: '张三3', age: 12 },
          { name: '张三4', age: 13 },
          { name: '张三5', age: 14 },
          { name: '张三6', age: 15 },
          { name: '张三7', age: 16 },
          { name: '张三8', age: 17 },
          { name: '张三9', age: 18 },
          { name: '张三10', age: 90 },
          { name: '张三11', age: 20 },
          { name: '张三12', age: 21 },
          { name: '张三13', age: 22 },
          { name: '张三14', age: 23 },
          { name: '张三15', age: 24 },
          { name: '张三16', age: 25 },
        ]
      };
      console.log(this.hiprintTemplate, 'this.hiprintTemplate');
      this.$refs.preView.show(this.hiprintTemplate, printData);
      console.log(this.$refs.preView.visible, 'this.$refs.preView.visible');
    },

    // 打开预览的dialog框
    show(printData, width = '250') {
      this.spinning = true
      this.width = this.hiprintTemplate.editingPanel ? this.hiprintTemplate.editingPanel.width : width;
      // this.width += 10;
      // this.hiprintTemplate = hiprintTemplate
      this.printData = printData
      setTimeout(() => {
        // eslint-disable-next-line no-undef
        $('#preview_content_design').html(this.hiprintTemplate.getHtml(printData))
        // this.spinning = false
      }, 500);
      this.visible = true
    },

    // 关闭预览的dialog框
    hideModal() {
      this.visible = false;
    },

    // 在父组件打开预览dialog框的
    showDialog(templateData, printData, width='250') {
      this.importJson(templateData); // 改变模板
      // setTimeout(() => {
        this.show(printData, width);
      // }, 0);
    }

  },
}
</script>

<style lang="scss" scoped>
.content {
  background: #fff;
  margin-left: 10px;
  margin-right: 10px;
  border-radius: 10px;
  padding: 20px;

  .header {
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 0 4px #ccc;
  }

  .panelsBoxContainer {
    width: 100%;
    // position: relative;
    height: 50px;
    display: flex;
    justify-content: left;

    .pageSize {
      margin-top: 10px;
    }

    #panelsBox {
      // position: absolute;
      // left: 50%;
      // transform: translateX(-50%);
      margin-left: 130px;
      margin-top: 0;

    }
  }

  #providerBox {
    height: 130px;
    display: flex;

    ::v-deep .hiprint-printElement-type {
      display: flex;

      .title {
        margin-bottom: 5px;
      }
    }

    ::v-deep .ep-draggable-item {
      height: 30px;
      line-height: 4px;
    }
  }

  #optionsBox {
    width: 100%;
    height: 500px;
    overflow-y: auto;

    ::v-deep .hiprint-option-items {
      width: 95%;
    }

    ::v-deep .hiprint-option-item-settingBtn {
      cursor: pointer;
    }
  }

  #designerBox {
    overflow: auto;
    height: 500px;
    margin-bottom: 50px;
  }
}

.pageSizeChoose {
  border: 1px solid #ccc;
  border-radius: 5px;
  margin: 0 10px 10px;

  ::v-deep .el-input__inner {
    border: none;
    text-align: center;
  }
}

.pageSizeChooseSub {
  width: 90% !important;
  margin-left: 5%;
  margin-bottom: 20px;
}

::v-deep .hiprint-printPaper-content {

  // 修改 页眉/页脚线 样式
  .hiprint-headerLine,
  .hiprint-footerLine {
    border-color: red !important;
  }

  .hiprint-headerLine:hover,
  .hiprint-footerLine:hover {
    border-top: 3px dashed red !important;
  }

  .hiprint-headerLine:hover:before {
    content: "页眉线";
    left: calc(50% - 18px);
    position: relative;
    background: #ffff;
    top: -12px;
    color: red;
    font-size: 12px;
  }

  .hiprint-footerLine:hover:before {
    content: "页脚线";
    left: calc(50% - 18px);
    position: relative;
    color: red;
    background: #ffff;
    top: -12px;
    font-size: 12px;
  }
}
</style>