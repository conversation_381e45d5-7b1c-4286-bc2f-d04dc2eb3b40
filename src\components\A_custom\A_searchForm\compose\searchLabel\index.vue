<template>
    <div class="searchLabelBox" >
        <div class="searchLabelBox_left" >
            <div class="searchLabel" >{{label}}</div>
            <div v-if="$slots.default" style="flex:1;" >
                <slot></slot>
            </div>
        </div>
        <!-- <div v-if="$slots.right" class="searchLabelBox_right" >
            <slot name="right"></slot>
        </div> -->
    </div>
</template>

<script>
    export default {
        props:{
            label:{
                type:String,
            },
            colon:{
                type:[Boolean],
                default:()=>{return false}
            },
        },
        created(){
            this.colon && (this.label = this.label + " :");
        },
    }
</script>

<style lang="scss" scoped>
$labelColor:#1b2944;
$inputHeight:28px;
.searchLabelBox{
    border-bottom:1px solid #ffffff;
    display:flex;
    justify-content: space-between;
    align-items:center;
}
.searchLabelBox_left{
    display: flex;
    padding:10px 0;
    font-size:16px;
    .searchLabel{
        height:$inputHeight;
        margin-right:20px;
        color:$labelColor;
        font-size:16px;
        user-select:none;
        line-height:$inputHeight;
    }
}
</style>