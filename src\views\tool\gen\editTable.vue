<template>
  <el-card>
    <el-tabs v-model="activeName">
      <el-tab-pane label="基本信息" name="basic">
        <basic-info-form ref="basicInfo" :info="info" />
      </el-tab-pane>
      <el-tab-pane label="字段信息" name="columnInfo">
        <el-table ref="dragTable" :data="columns" row-key="columnId" :max-height="tableHeight" border>
          <el-table-column label="序号" type="index" min-width="50" class-name="allowDrag" />
          <el-table-column label="字段列名" prop="columnName" min-width="120" :show-overflow-tooltip="true" />
          <el-table-column label="字段描述" min-width="150" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <el-input v-model="scope.row.columnComment"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="物理类型" prop="columnType" min-width="110" :show-overflow-tooltip="true" />
          <el-table-column label="Java类型" min-width="110" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <el-select v-model="scope.row.javaType">
                <el-option label="Long" value="Long" />
                <el-option label="String" value="String" />
                <el-option label="Integer" value="Integer" />
                <el-option label="Double" value="Double" />
                <el-option label="BigDecimal" value="BigDecimal" />
                <el-option label="Date" value="Date" />
                <el-option label="Boolean" value="Boolean" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="java属性" min-width="150" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <el-input v-model="scope.row.javaField"></el-input>
            </template>
          </el-table-column>

          <el-table-column label="插入" min-width="80" align="center">
            <template slot="header" slot-scope="scope">
              <el-checkbox :indeterminate="allObj.isInsert.indeterminate" v-model="allObj.isInsert.all"
                @change="handleAllInsert($event, 'isInsert')" class="checkText">插入</el-checkbox>
            </template>
            <template slot-scope="scope">
              <el-checkbox true-label="1" false-label="0" v-model="scope.row.isInsert"
                @change="handleIsInsert($event, 'isInsert')"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="编辑" min-width="80" align="center">
            <template slot="header" slot-scope="scope">
              <el-checkbox :indeterminate="allObj.isEdit.indeterminate" v-model="allObj.isEdit.all"
                @change="handleAllInsert($event, 'isEdit')" class="checkText">编辑</el-checkbox>
            </template>
            <template slot-scope="scope">
              <el-checkbox true-label="1" false-label="0" v-model="scope.row.isEdit"
                @change="handleIsInsert($event, 'isEdit')"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="列表" min-width="80" align="center">
            <template slot="header" slot-scope="scope">
              <el-checkbox :indeterminate="allObj.isList.indeterminate" v-model="allObj.isList.all"
                @change="handleAllInsert($event, 'isList')" class="checkText">列表</el-checkbox>
            </template>
            <template slot-scope="scope">
              <el-checkbox true-label="1" false-label="0" v-model="scope.row.isList"
                @change="handleIsInsert($event, 'isList')"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="查询" min-width="80" align="center">
            <template slot="header" slot-scope="scope">
              <el-checkbox :indeterminate="allObj.isQuery.indeterminate" v-model="allObj.isQuery.all"
                @change="handleAllInsert($event, 'isQuery')" class="checkText">查询</el-checkbox>
            </template>
            <template slot-scope="scope">
              <el-checkbox true-label="1" false-label="0" v-model="scope.row.isQuery"
                @change="handleIsInsert($event, 'isQuery')"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="查询方式" min-width="100">
            <template slot-scope="scope">
              <el-select v-model="scope.row.queryType">
                <el-option label="=" value="EQ" />
                <el-option label="!=" value="NE" />
                <el-option label=">" value="GT" />
                <el-option label=">=" value="GTE" />
                <el-option label="<" value="LT" />
                <el-option label="<=" value="LTE" />
                <el-option label="LIKE" value="LIKE" />
                <el-option label="BETWEEN" value="BETWEEN" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="默认" min-width="80" align="center">
            <template slot="header" slot-scope="scope">
              <el-checkbox :indeterminate="allObj.defaultFlag.indeterminate" v-model="allObj.defaultFlag.all"
                @change="handleAllInsert($event, 'defaultFlag')" class="checkText">默认</el-checkbox>
            </template>
            <template slot-scope="scope">
              <el-checkbox true-label="1" false-label="0" v-model="scope.row.defaultFlag"
                @change="handleIsInsert($event, 'defaultFlag')"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="排序" min-width="80" align="center">
            <template slot="header" slot-scope="scope">
              <el-checkbox :indeterminate="allObj.sortFlag.indeterminate" v-model="allObj.sortFlag.all"
                @change="handleAllInsert($event, 'sortFlag')" class="checkText">排序</el-checkbox>
            </template>
            <template slot-scope="scope">
              <el-checkbox true-label="1" false-label="0" v-model="scope.row.sortFlag"
                @change="handleIsInsert($event, 'sortFlag')"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="必填" min-width="80" align="center">
            <template slot="header" slot-scope="scope">
              <el-checkbox :indeterminate="allObj.isRequired.indeterminate" v-model="allObj.isRequired.all"
                @change="handleAllInsert($event, 'isRequired')" class="checkText">必填</el-checkbox>
            </template>
            <template slot-scope="scope">
              <el-checkbox true-label="1" false-label="0" v-model="scope.row.isRequired"
                @change="handleIsInsert($event, 'isRequired')"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="显示类型" min-width="130">
            <template slot-scope="scope">
              <el-select v-model="scope.row.htmlType">
                <el-option label="文本框" value="input" />
                <el-option label="数字框" value="number" />
                <el-option label="文本域" value="textarea" />
                <el-option label="引用数据" value="reflection" />
                <el-option label="下拉框" value="select" />
                <el-option label="单选框" value="radio" />
                <el-option label="复选框" value="checkbox" />
                <el-option label="日期控件" value="datetime" />
                <el-option label="图片上传" value="imageUpload" />
                <el-option label="文件上传" value="fileUpload" />
                <el-option label="富文本控件" value="editor" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="系统字典" min-width="130">
            <template slot-scope="scope">
              <el-select v-model="scope.row.dictType" clearable filterable placeholder="请选择">
                <el-option v-for="dict in dictOptions" :key="dict.dictType" :label="dict.dictName"
                  :value="dict.dictType">
                  <span style="float: left">{{ dict.dictName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ dict.dictType }}</span>
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="用户字典" min-width="130">
            <template slot-scope="scope">
              <el-select v-model="scope.row.userDictType" clearable filterable placeholder="请选择">
                <el-option v-for="dict in userDictType" :key="dict.dictType" :label="dict.dictName"
                  :value="dict.dictType">
                  <span style="float: left">{{ dict.dictName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ dict.dictType }}</span>
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="关联查询" min-width="120">
            <template slot-scope="scope">
              <el-input v-model="scope.row.reflection"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="权限标识符" min-width="120">
            <template slot-scope="scope">
              <el-input v-model="scope.row.permission"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="列表宽度" min-width="100">
            <template slot-scope="scope">
              <el-input v-model="scope.row.tableWidth"></el-input>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="生成信息" name="genInfo">
        <gen-info-form ref="genInfo" :info="info" :tables="tables" :menus="menus" />
      </el-tab-pane>
    </el-tabs>
    <el-form label-width="100px">
      <el-form-item style="text-align: center;margin-left:-100px;margin-top:10px;">
        <el-button type="primary" @click="submitForm()">提交</el-button>
        <el-button @click="close()">返回</el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script>
import { getGenTable, updateGenTable } from "@/api/tool/gen";
import { optionselect as getDictOptionselect } from "@/api/system/dict/type";
import { optionselect as getuserDictTypeselect } from "@/api/system/ydDict/type";
import { listMenu as getMenuTreeselect } from "@/api/system/menu";
import basicInfoForm from "./basicInfoForm";
import genInfoForm from "./genInfoForm";
import Sortable from 'sortablejs'

export default {
  name: "GenEdit",
  components: {
    basicInfoForm,
    genInfoForm
  },
  data() {
    return {
      allObj: {
        //全选插入
        isInsert: {
          all: false,
          indeterminate: true
        },
        isEdit: {
          all: false,
          indeterminate: true
        },
        isList: {
          all: false,
          indeterminate: true
        },
        isQuery: {
          all: false,
          indeterminate: true
        },
        defaultFlag: {
          all: false,
          indeterminate: true
        },
        sortFlag: {
          all: false,
          indeterminate: true
        },
        isRequired: {
          all: false,
          indeterminate: true
        },
      },


      // 选中选项卡的 name
      activeName: "columnInfo",
      // 表格的高度
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 表信息
      tables: [],
      // 表列信息
      columns: [],
      // 系统字典信息
      dictOptions: [],
      //用户字典
      userDictType: [],
      // 菜单信息
      menus: [],
      // 表详细信息
      info: {},
      columnsLength: 0
    };
  },
  created() {
    const tableId = this.$route.params && this.$route.params.tableId;
    if (tableId) {
      // 获取表详细信息
      getGenTable(tableId).then(res => {
        this.columns = res.data.rows;
        this.info = res.data.info;
        this.tables = res.data.tables;
        this.columnsLength = res.data.rows.length
        this.getAll(res.data.rows)

      });
      /** 查询系统字典下拉列表 */
      getDictOptionselect().then(response => {
        this.dictOptions = response.data;
      });
      /** 查询用户字典下拉列表 */
      getuserDictTypeselect().then(response => {
        this.userDictType = response.data;
      });

      /** 查询菜单下拉列表 */
      getMenuTreeselect().then(response => {
        this.menus = this.handleTree(response.data, "menuId");
      });
    }
  },
  methods: {
    getAll(list) {
      let isInsertNum= 0, isEditNum= 0, isListNum= 0, isQueryNum= 0, defaultFlagNum= 0, sortFlagNum= 0, isRequiredNum = 0;
      list.forEach(item => {
        if (item.isInsert == '1') {
          isInsertNum++
        }
        if (item.isEdit == '1') {
          isEditNum++
        }
        if (item.isList == '1') {
          isListNum++
        }
        if (item.isQuery == '1') {
          isQueryNum++
        }
        if (item.defaultFlag == '1') {
          defaultFlagNum++
        }
        if (item.sortFlag == '1') {
          sortFlagNum++
        }
        if (item.isRequired == '1') {
          isRequiredNum++
        }
      })
      this.allObj = {
        isInsert: {
          all: isInsertNum == this.columnsLength && this.columnsLength > 0,
          indeterminate: isInsertNum > 0 && isInsertNum < this.columnsLength
        },
        isEdit: {
          all: isEditNum == this.columnsLength && this.columnsLength > 0,
          indeterminate: isEditNum > 0 && isEditNum < this.columnsLength
        },
        isList: {
          all: isListNum == this.columnsLength && this.columnsLength > 0,
          indeterminate: isListNum > 0 && isListNum < this.columnsLength
        },
        isQuery: {
          all: isQueryNum == this.columnsLength && this.columnsLength > 0,
          indeterminate: isQueryNum > 0 && isQueryNum < this.columnsLength
        },
        defaultFlag: {
          all: defaultFlagNum == this.columnsLength && this.columnsLength > 0,
          indeterminate: defaultFlagNum > 0 && defaultFlagNum < this.columnsLength
        },
        sortFlag: {
          all: sortFlagNum == this.columnsLength && this.columnsLength > 0,
          indeterminate: sortFlagNum > 0 && sortFlagNum < this.columnsLength
        },
        isRequired: {
          all: isRequiredNum == this.columnsLength && this.columnsLength > 0,
          indeterminate: isRequiredNum > 0 && isRequiredNum < this.columnsLength
        }
      }
    },
    //插入选择全选方法
    handleAllInsert(val, key) {
      this.allObj[key].all = val
      this.allObj[key].indeterminate = false;
      if (val) {
        this.columns.forEach(item => {
          item[key] = '1'
        })
      } else {
        this.columns.forEach(item => {
          item[key] = '0'
        })
      }
    },
    handleIsInsert(e, key) {
      let num = 0
      this.columns.forEach(item => {
        if (item[key] == '1') {
          num++
        }
      })
      console.log(num, this.columnsLength);
      if (num == this.columnsLength && this.columnsLength > 0) {
        this.allObj[key].all = true
        this.allObj[key].indeterminate = false;
      } else if (num > 0) {
        this.allObj[key].all = false
        this.allObj[key].indeterminate = true;
      } else {
        this.allObj[key].all = false
        this.allObj[key].indeterminate = false;
      }
    },
    /** 提交按钮 */
    submitForm() {
      const basicForm = this.$refs.basicInfo.$refs.basicInfoForm;
      const genForm = this.$refs.genInfo.$refs.genInfoForm;
      Promise.all([basicForm, genForm].map(this.getFormPromise)).then(res => {
        const validateResult = res.every(item => !!item);
        if (validateResult) {
          const genTable = Object.assign({}, basicForm.model, genForm.model);
          genTable.columns = this.columns;
          genTable.params = {
            treeCode: genTable.treeCode,
            treeName: genTable.treeName,
            treeParentCode: genTable.treeParentCode,
            parentMenuId: genTable.parentMenuId
          };
          updateGenTable(genTable).then(res => {
            this.$modal.msgSuccess(res.msg);
            if (res.code === 150) {
              this.close();
            }
          });
        } else {
          this.$modal.msgError("表单校验未通过，请重新检查提交内容");
        }
      });
    },
    getFormPromise(form) {
      return new Promise(resolve => {
        form.validate(res => {
          resolve(res);
        });
      });
    },
    /** 关闭按钮 */
    close() {
      const obj = { path: "/tool/gen", query: { t: Date.now(), pageNum: this.$route.query.pageNum } };
      this.$tab.closeOpenPage(obj);
    }
  },
  mounted() {
    const el = this.$refs.dragTable.$el.querySelectorAll(".el-table__body-wrapper > table > tbody")[0];
    const sortable = Sortable.create(el, {
      handle: ".allowDrag",
      onEnd: evt => {
        const targetRow = this.columns.splice(evt.oldIndex, 1)[0];
        this.columns.splice(evt.newIndex, 0, targetRow);
        for (let index in this.columns) {
          this.columns[index].sort = parseInt(index) + 1;
        }
      }
    });
  }
};
</script>
<style>
.checkText {
  color: #515a6e;
  font-size: 13px;
  font-weight: bold;
}
</style>