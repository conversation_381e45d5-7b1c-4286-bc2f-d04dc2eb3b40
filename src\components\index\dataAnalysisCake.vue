<template>
  <div>
    <div ref="dataAnalysis_cake" :style="{width:width,height:width}"></div>
  </div>
</template>

<script>
export default {
  props: {
    width: {
      type: String,
      default: "110px"
    },
    list: [Array],
  },
  data() {
    return {

    }
  },
  watch: {
    list: {
      deep: true,
      handler() {
        this.cakeRef();
      }
    }
  },
  mounted() {
    this.cakeRef();
  },
  methods: {
    // 第二个块,的饼图
    cakeRef() {
      let chartDom = this.$refs["dataAnalysis_cake"];
      let myChart = this.echarts.init(chartDom);
      var that = this;
      var option = {
        tooltip: {
          trigger: 'item',
          width: 80
        },
        series: [
          {
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 15,
                fontWeight: 'bold'
              }
            },
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
              normal: {
                color: function (colors) {
                  return that.list[colors.dataIndex].color;
                }
              },
            },
            type: 'pie',
            radius: ['70%', '90%'],
            data: that.list
          }
        ]
      };
      myChart.setOption(option);
    },
  }
}
</script>

<style lang="scss" scoped>
</style>