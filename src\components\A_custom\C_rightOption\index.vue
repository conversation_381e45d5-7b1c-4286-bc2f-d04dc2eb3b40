<template>
<!-- 右键弹出框 -->
    <div class="rightOption" :style="[styles]" v-show="rightClickShow">
        <div class="tableRightContent" ref="tableRightContent">
            <slot></slot>
        </div>
    </div>
</template>

<script>
    export default {
        props:{
            /*
                点击数据位置参数 x,y 数据
            */ 
            rightClickData:{
                type:Object,
                default:()=>{return { x:0,y:0} }
            },
             /*
                控制展示
            */ 
            rightClickShow:{
                type:Boolean,
                default:()=>{return false }
            },
        },
        data(){
            return{
                styles:{},
            }
        },
        watch: {
            rightClickData(){
                let {x,y} = this.rightClickData.config;
                console.log(this.rightClickData)
                this.styles = {"--X":x+"px","--Y":y+"px","--theme":this.$store.state.settings.theme};
            },
        },
        created(){
            this.bodyRendering();
            document.addEventListener('click', (e) => {
                if(e.target.className == "rightClickClass"){return}
                this.$emit("update:rightClickShow",false);
            });
        },
        beforeDestroy(){
            const body = document.querySelector('body');
            body.removeChild(this.$el);
        },
        methods:{
            bodyRendering(){
                const body = document.querySelector('body');
                 this.$nextTick(() => {
                    if (body.append) {
                        body.append(this.$el);
                    } else {
                        body.appendChild(this.$el);
                    }
                 })
                // body.removeChild(this.$el);
            },
        },
    }
</script>

<style lang="scss">
.rightOption{
    position: fixed;
    top:var(--Y);
    left:var(--X);
    background:#ffffff;
    border:1px solid #d8dce5;
    border-radius:4px;
    box-shadow: 0px 7px 7px -7px #cdcdcd;
    padding: 0px 10px;
    z-index:999999;
}
.tableRightContent{
    display: inline-grid;
    justify-items: start;
    padding: 4px 0;
}

.tableRightContent .el-link--inner{
    font-size: 13px;
    margin: 3px 0;
}
.tableRightContent .el-link{
    width: 100%;
    justify-content: flex-start;
}
</style>