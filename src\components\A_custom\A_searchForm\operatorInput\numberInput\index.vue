<template>
    <div>
    <!-- 单个 -->
        <el-input-number v-if="!config.range" class="schemeQueryInput" :disabled="config.disabled" @change="inputValue" size="mini" :value="value" controls-position="right" :length="6" />
    <!-- 范围 -->
        <div v-else >
            <el-input-number class="schemeQueryInput" :disabled="config.disabled" @change="rangeFrontChange" size="mini" v-model="rangeFront" controls-position="right" :length="6" />
            <span style="margin-left:5px" >~</span>
            <el-input-number class="schemeQueryInput" :disabled="config.disabled" @change="rangeAfterChange" size="mini" v-model="rangeAfter" controls-position="right" :length="6" />
        </div>
    </div>
</template>

<script>
import {inputMixin} from "../mixins/index.js"
    export default {
        props:{
            value:{
                type:[Number,String],
                default:()=>{return undefined}
            },
        },
        mixins:[inputMixin],
        data(){
            return{
                rangeFront:undefined,
                rangeAfter:undefined,
            }
        },
        created(){
            !this.value && this.$emit('input', undefined)
        },
        methods:{
            inputValue(val){
                this.$emit('input', val);
            },
            rangeFrontChange(val){
                let strList = [val,this.rangeAfter];
                this.$emit('input', strList.join());
            },
            rangeAfterChange(val){
                let strList = [this.rangeFront,val];
                this.$emit('input', strList.join());
            }
        },
    }
</script>

<style lang="scss" scoped>

</style>