// 创建请求ID
const createRequestId = (device, type) => {
    return `${type}_${device.ip}_${Date.now()}`;
};

// 从请求ID中解析IP
const parseRequestId = (requestId) => {
    // 使用IP地址格式的正则表达式来匹配
    const ipMatch = requestId.match(/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/);
    if (ipMatch && ipMatch[0]) {
        return ipMatch[0]; // 返回IP地址
    }
    return null;
};
// 发送websocket消息
const sendWebSocketMessage = (ws, type, device, controlType = null, controlValue = null) => {
    if (!['GET_STATUS','CONTROL_DEVICE'].includes(type)) {
        throw new Error('无效的消息类型');
    }
    if (ws && ws.isConnected()) {
        ws.send({
            type: type,
            requestId: createRequestId(device, type),
            payload: {
            ip: device.ip,
            deviceType: parseInt(device.type),
            type: controlType,
            value: controlValue
        }
        });
    } else {
        console.warn('WebSocket未连接，无法发送消息:', type, device, controlType, controlValue);
    }
};

// 导出函数
export { createRequestId, parseRequestId, sendWebSocketMessage };