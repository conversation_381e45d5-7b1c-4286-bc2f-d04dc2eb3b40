<template>
    <div class="logDetailPage">
        <div class="logDetailPage_head" >
            <h2><strong>执行日志 </strong> Console</h2>
        </div>
        <div class="logDetailPage_content" >
            <div style="white-space: pre-wrap;" v-html="html" ></div>
        </div>
    </div>
</template>

<script>
    import {dispatchingLogDetailCatApi,dispatchingLogDetailPageApi} from "../api/dispatchingLog.js";
    export default {
        data(){
            return{
                queryParams:{
                    logId:null,
                },
                html:null,
            }
        },
        created(){
           this.queryParams.logId = this.$route.query.id;
           this.oneEvent();
        },
        methods:{
            oneEvent(){
                dispatchingLogDetailPageApi({id:this.$route.query.id}).then(res=>{
                    dispatchingLogDetailCatApi({...res.data,fromLineNum:1}).then(ress=>{
                        this.html = ress.content.logContent
                        console.log(ress)
                    })
                })
                
            }
        },
    }
</script>

<style lang="scss" scoped>
    .logDetailPage{
        &_head {
            padding:0 50px;
            width:100%;
            background:#3c8dbc;
            height:60px;
            color:#ffffff;
            display:flex;
            justify-content: space-between;
		    align-items: center;
        }
        &_content{
            margin:20px;
            padding: 10px 15px;
            background:#f5f5f5;
            border:1px solid #cccccc;
            min-height:500px;
            border-radius:5px;
            line-height:30px;
        }
    }
</style>