<template>
  <div class="login">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form"
      :class="{ 'login-form-code': isCodeLogin }" style="position: relative;z-index: 99;">
      <div class="login-left">
        <el-carousel indicator-position="none" :interval="5000" style="width: 550px;" class="loginBanner">
          <el-carousel-item v-for="item in swiperList" :key="item" style="width: 550px;">
            <img :src="item" alt="logo">
          </el-carousel-item>
        </el-carousel>
      </div>
      <div class="login-right">
        <div class="title">
          <img v-if="logo" :src="logo" alt="logo">
          <h3>广州链销科技有限公司</h3>
        </div>
        <div v-show="!isCodeLogin">
          <el-form-item prop="username" label="用户名" :class="screenHeight > 700 ? 'mb22' : 'mb10'">
            <el-input v-model="loginForm.username" type="text" auto-complete="off" style="width: 100% !important;"
              placeholder="账号">
              <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
            </el-input>
          </el-form-item>
          <el-form-item prop="password" label="密码" :class="screenHeight > 700 ? 'mb22' : 'mb10'">
            <el-input v-model="loginForm.password" type="password" auto-complete="off" style="width: 100% !important;"
              placeholder="密码" @keyup.enter.native="handleLogin">
              <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
            </el-input>
          </el-form-item>
          <el-form-item prop="code" v-if="captchaOnOff" :class="screenHeight > 700 ? 'mb22' : 'mb10'">
            <el-input v-model="loginForm.code" auto-complete="off" placeholder="验证码" style="width: 63% !important;"
              @keyup.enter.native="handleLogin">
              <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
            </el-input>
            <div class="login-code">
              <img :src="codeUrl" @click="getCode" class="login-code-img" />
            </div>
          </el-form-item>
          <el-form-item style="width:100%;" :class="screenHeight > 700 ? 'mb22' : 'mb10'">
            <el-button :loading="loading" size="medium" type="primary" style="width:100%;"
              @click.native.prevent="handleLogin">
              <span v-if="!loading">登 录</span>
              <span v-else>登 录 中...</span>
            </el-button>
            <div style="float: right;" v-if="register">
              <router-link class="link-type" :to="'/register'">立即注册</router-link>
            </div>
          </el-form-item>
        </div>

      </div>

    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © https://www.lianxiao.cloud All Rights Reserved.</span>
    </div>
    <div class="backgorundImage">
    </div>
  </div>
</template>

<script>
import { getCodeImg, homeTokenApi } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from '@/utils/jsencrypt'
import logoImg from '@/assets/logo/logo.png'
import { getToken, setToken } from '@/utils/auth'

export default {
  name: "Login",
  data() {
    return {
      logo: logoImg,
      codeUrl: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: "",
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" }
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }]
      },
      loading: false,
      // 验证码开关
      captchaOnOff: true,
      // 注册开关
      register: false,
      redirect: undefined,
      isCodeLogin: false,
      swiperList: [
        'https://www.chengwenit.com/img/jiimg/jst2kd.png',
        'https://www.chengwenit.com/img/jiimg/dj2ll.png',
        'https://www.chengwenit.com/img/jiimg/dd2yy.png',
        'https://www.chengwenit.com/img/jiimg/fx2shr.png'
      ],

      // 拿到屏幕的高度
      screenHeight: document.body.offsetHeight,
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  created() {
    var isComWx = /wxwork/i.test(navigator.userAgent)
    if (isComWx) {
      console.log("🚀 ~ created ~ isComWx:", isComWx)
      this.checkWeChatLogin()
    } else {
      this.getCode();
      this.getCookie();
    }
  },
  methods: {
    checkWeChatLogin() {
      this.loading = true;
      let href = window.location.href;
      let query = href.substring(href.indexOf("?") + 1);
      let vars = query.split("&");
      let obj = {};
      for (let i = 0; i < vars.length; i++) {
        let pair = vars[i].split("=");
        obj[pair[0]] = pair[1];
      }
      if (!getToken() && !obj.code) {
        location.href = process.env.VUE_APP_BASE_API + `/wechat/work/auth?redirect=${encodeURIComponent(window.location.href)}`
        this.loading = false;
      } else if (!getToken() && obj.code) {
        // const code = obj.code
        const code = 'test'
        homeTokenApi({ code: code }).then(res => {
          setToken(res.data.token);
          this.$store.commit('SET_TOKEN', res.data.token)
          this.loading = false;
          this.$router.push({ path: this.redirect || "/" }).catch(() => { });
        }).catch(error => {
          alert(error)
        })
      } else {
        this.$router.push({ path: this.redirect || "/" }).catch(() => { });
      }
    },
    codeLogin() {
      this.isCodeLogin = true;
    },
    getCode() {
      getCodeImg().then(res => {
        console.log(res);
        this.captchaOnOff = res.captchaOnOff === undefined ? true : res.captchaOnOff;
        if (this.captchaOnOff) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.loginForm.uuid = res.uuid;
        }
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 });
            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove('rememberMe');
          }
          this.$store.dispatch("Login", this.loginForm).then(() => {
            this.$router.push({ path: this.redirect || "/" }).catch(() => { });
          }).catch(() => {
            this.loading = false;
            if (this.captchaOnOff) {
              this.getCode();
            }
          });
        }
      });
    }
  }
};
</script>

<style lang="scss">
// 页面高度 700px - 1920px 的情况
@media screen and (min-height: 700px) and (max-height: 1920px) {
  .login {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    // background-image: url("../assets/images/login-background.png");
    // background-image: url("../assets/images/login-back.png");
    background: #ffffff;
    background-size: cover;
    position: relative;
    // .el-input{
    //   width: 100% !important;
    // }
  }

  .backgorundImage {
    position: absolute;
    top: 2%;
    z-index: 0;
    width: 98%;
    height: 48%;
    background-image: url("../assets/images/loginBackgroundImage.png");
    border-radius: 15px;
    background-position: 0 -420px;

    // background-clip: border-box;
    // background-size: contain;
    // background-repeat: no-repeat;
    .backgorundTitle {
      color: #fff;
      font-size: 33px;
      position: absolute;
      top: 12%;
      left: 50%;
      transform: translateX(-50%);
      font-family: "仿宋";
      font-weight: 800;
    }
  }

  .backgorundImage:before {
    content: '';
    display: block;
    clear: both;
    background-color: rgba($color: #3181cc, $alpha: .68);
    width: 100%;
    height: 100%;
    border-radius: 15px;
    box-shadow: 0px 3px 10px 0px #888;
  }

  .title {
    margin: 0px auto 30px auto;
    text-align: center;
    color: #1e1b1b;
    font-weight: 600;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
      width: 90px;
    }
  }

  .login-form {
    border-radius: 6px;
    background: #fff;
    width: auto;
    // min-width: 960px;
    height: 550px;
    min-height: 550px;
    padding: 25px 25px 5px 25px;
    box-shadow: 0 0 3px 1px #ccc;
    display: flex;
    // margin-top: 150px;

    .login-regulations {
      font-size: 13px;
      color: #333;

      a {
        color: #225592;
        cursor: pointer;
      }
    }

    .login-left {
      padding: 90px 0;

      img {
        width: 550px;
      }
    }

    .login-right {
      padding: 20px 0;
      position: relative;
      margin: 0 40px;
      width: 300px;

      .logn-QRcode {
        position: absolute;
        right: -23px;
        top: -25px;

        img {
          width: 65px;
          cursor: pointer;
        }
      }

      .code-content {
        .qrcode-box {
          width: 134px;
          height: 134px;
          border: 1px solid #D3D3D3;
          text-align: center;
          margin: 0 auto;

          img {
            width: 132px;
            height: 132px;
          }
        }

        .back-ac {
          color: #225592;
          cursor: pointer;
          text-align: center;
          font-size: 12px;
          margin-top: 20px;
        }

        .code-title {
          font-weight: 600;
          font-size: 14px;
          text-align: center;
          margin-bottom: 20px;
        }
      }
    }

    .el-input {
      height: 40px;

      input {
        height: 40px;
      }
    }

    .input-icon {
      height: 39px;
      width: 14px;
      margin-left: 2px;
    }
  }

  .login-form-code {
    height: 400px;
    min-height: 400px;

    .login-left {
      padding: 30px 0;
    }
  }

  .login-tip {
    font-size: 13px;
    text-align: center;
    color: #bfbfbf;
  }

  .login-code {
    width: 33%;
    height: 38px;
    float: right;

    img {
      cursor: pointer;
      vertical-align: middle;
    }
  }

  .el-login-footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #000000;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
  }

  .login-code-img {
    height: 40px;
  }
}

// 页面高度 300px - 700px 的情况
@media screen and (min-height: 300px) and (max-height: 700px) {
  .login {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    // background-image: url("../assets/images/login-background.png");
    // background-image: url("../assets/images/login-back.png");
    background: #ffffff;
    background-size: cover;
    position: relative;
    // .el-input{
    //   width: 100% !important;
    // }
  }

  .backgorundImage {
    position: absolute;
    top: 2%;
    z-index: 0;
    width: 98%;
    height: 48%;
    background-image: url("../assets/images/loginBackgroundImage.png");
    border-radius: 15px;
    background-position: 0 -420px;

    // background-clip: border-box;
    // background-size: contain;
    // background-repeat: no-repeat;
    .backgorundTitle {
      color: #fff;
      font-size: 33px;
      position: absolute;
      top: 12%;
      left: 50%;
      transform: translateX(-50%);
      font-family: "仿宋";
      font-weight: 800;
    }
  }

  .backgorundImage:before {
    content: '';
    display: block;
    clear: both;
    background-color: rgba($color: #3181cc, $alpha: .68);
    width: 100%;
    height: 100%;
    border-radius: 15px;
    box-shadow: 0px 3px 10px 0px #888;
  }

  .title {
    margin: 0px auto 10px auto;
    text-align: center;
    color: #1e1b1b;
    font-weight: 600;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
      width: 60px;
    }

    h3 {
      margin-top: 10px;
      margin-bottom: 0px;
    }
  }

  .login-form {
    border-radius: 6px;
    background: #fff;
    width: auto;
    // min-width: 960px;
    height: 400px;
    min-height: 400px;
    padding: 25px;
    box-shadow: 0 0 3px 1px #ccc;
    display: flex;
    // margin-top: 150px;

    .login-regulations {
      font-size: 13px;
      color: #333;

      a {
        color: #225592;
        cursor: pointer;
      }
    }

    .login-left {
      // padding: 90px 0;
      position: relative;

      img {
        width: 550px;
      }

      .loginBanner {
        height: 100%;

        >div {
          height: 100%;
        }
      }
    }

    .login-right {
      padding: 0px 0;
      position: relative;
      margin: 0 10px;
      width: 320px;

      .logn-QRcode {
        position: absolute;
        right: -23px;
        top: -25px;

        img {
          width: 65px;
          cursor: pointer;
        }
      }

      .code-content {
        .qrcode-box {
          width: 134px;
          height: 134px;
          border: 1px solid #D3D3D3;
          text-align: center;
          margin: 0 auto;

          img {
            width: 132px;
            height: 132px;
          }
        }

        .back-ac {
          color: #225592;
          cursor: pointer;
          text-align: center;
          font-size: 12px;
          margin-top: 20px;
        }

        .code-title {
          font-weight: 600;
          font-size: 14px;
          text-align: center;
          margin-bottom: 20px;
        }
      }

      ::v-deep .el-form-item {
        margin-bottom: 5px !important;
      }
    }

    .el-input {
      height: 30px;

      input {
        height: 30px;
      }
    }

    .input-icon {
      height: 29px;
      width: 14px;
      margin-left: 2px;
    }
  }

  .login-form-code {
    height: 150px;
    min-height: 150px;

    .login-left {
      padding: 30px 0;
    }
  }

  .login-tip {
    font-size: 13px;
    text-align: center;
    color: #bfbfbf;
  }

  .login-code {
    width: 33%;
    height: 28px;
    float: right;

    img {
      cursor: pointer;
      vertical-align: middle;
    }
  }

  .el-login-footer {
    height: 30px;
    line-height: 30px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #000000;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
  }

  .login-code-img {
    height: 30px;
  }

}
</style>
