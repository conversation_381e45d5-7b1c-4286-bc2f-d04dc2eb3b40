import request from '@/utils/request'

// 获取全部数据统计
export function statisticsAll(query) {
  return request({
    url: '/home/<USER>',
    method: 'get',
    params: query
  })
}
// 获取卡片统计
export function listPanel(query) {
  return request({
    url: '/home/<USER>',
    method: 'get',
    params: query
  })
}
//获取销售排行列表
export function listRank(query) {
  return request({
    url: '/home/<USER>',
    method: 'get',
    params: query
  })
}
// 获取版本号、更新时间等
export function noticeConfig() {
  return request({
    url: '/system/config/noticeConfig',
    method: 'get',
  })
}
// 获取公告
export function getNotice() {
  return request({
    url: '/system/notice/content',
    method: 'get',
  })
}