<template>
  <div class="bi-screen">
    <!-- 顶部状态栏 -->
    <div class="header">
      <div class="header-left">
        <h1 class="title">智能设备监控大屏</h1>
      </div>
      <div class="header-center">
        <div class="datetime">{{ currentDateTime }}</div>
      </div>
      <div class="header-right">
        <div class="weather">
          <span class="weather-icon">☀️</span>
          <span class="temperature">{{ environmentData.temperature }}</span>
          <span class="humidity">{{ environmentData.humidity }}</span>
          <span class="wind">{{ environmentData.windDirection }} {{ environmentData.windSpeed }}</span>
        </div>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 左侧数据统计 -->
      <div class="left-panel">
        <div class="data-card">
          <div class="card-title">设备状态概览</div>
          <div class="status-overview">
            <div class="status-item" v-for="item in statusOverview" :key="item.type">
              <div class="status-icon" :class="item.type">
                <i :class="item.icon"></i>
              </div>
              <div class="status-info">
                <div class="status-count">{{ item.count }}</div>
                <div class="status-name">{{ item.name }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="data-card">
          <div class="card-title">设备类型分布</div>
          <div ref="deviceTypeChart" class="chart-container"></div>
        </div>
      </div>

      <!-- 中间图表区 -->
      <div class="center-panel">
        <div class="map-container">
          <div class="map-title">设备分布图</div>
          <div class="map-content" ref="mapChart"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
import 'echarts/map/js/china';
import gsap from 'gsap';

// 环境数据
const environmentData = ref({
  temperature: '28°C',
  humidity: '65%',
  windDirection: '东南风',
  windSpeed: '3级'
});

// 当前时间
const currentDateTime = ref('');

// 状态概览数据
const statusOverview = ref([
  { type: 'online', name: '在线设备', count: 128, icon: 'icon-online' },
  { type: 'offline', name: '离线设备', count: 12, icon: 'icon-offline' },
  { type: 'warning', name: '告警设备', count: 8, icon: 'icon-warning' },
  { type: 'error', name: '故障设备', count: 3, icon: 'icon-error' }
]);

// 图表实例
let deviceTypeChart = null;
let mapChart = null;

// 更新时间
const updateDateTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  
  currentDateTime.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 初始化图表
const initCharts = () => {
  // 初始化设备类型图表
  if (deviceTypeChart) {
    deviceTypeChart.dispose();
  }
  deviceTypeChart = echarts.init(document.querySelector('.chart-container'));
  
  // 初始化地图图表
  if (mapChart) {
    mapChart.dispose();
  }
  mapChart = echarts.init(document.querySelector('.map-content'));
  
  // 设置图表配置
  const deviceTypeOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: ['雾炮机', '水炮机', '水泵']
    },
    series: [
      {
        name: '设备类型',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '20',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 1048, name: '雾炮机' },
          { value: 735, name: '水炮机' },
          { value: 580, name: '水泵' }
        ]
      }
    ]
  };
  
  const mapOption = {
    title: {
      text: '设备分布图',
      left: 'center',
      textStyle: {
        color: '#fff'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}<br/>{c} (台)'
    },
    visualMap: {
      min: 0,
      max: 1000,
      text: ['高', '低'],
      realtime: false,
      calculable: true,
      inRange: {
        color: ['#50a3ba', '#eac736', '#d94e5d']
      },
      textStyle: {
        color: '#fff'
      }
    },
    series: [
      {
        name: '设备数量',
        type: 'map',
        map: 'china',
        label: {
          show: true,
          color: '#fff'
        },
        emphasis: {
          label: {
            color: '#fff'
          }
        },
        data: [
          { name: '北京', value: 100 },
          { name: '上海', value: 200 },
          { name: '广东', value: 300 },
          { name: '江苏', value: 150 },
          { name: '浙江', value: 180 }
        ]
      }
    ]
  };
  
  // 设置图表配置
  deviceTypeChart.setOption(deviceTypeOption);
  mapChart.setOption(mapOption);
};

// 处理窗口大小变化
let resizeTimer = null;
const handleResize = () => {
  if (resizeTimer) clearTimeout(resizeTimer);
  resizeTimer = setTimeout(() => {
    if (deviceTypeChart) deviceTypeChart.resize();
    if (mapChart) mapChart.resize();
  }, 200);
};

// 检测屏幕尺寸
const checkScreenSize = () => {
  // 根据屏幕尺寸调整布局
  const width = window.innerWidth;
  if (width < 1200) {
    // 小屏幕布局
    console.log('小屏幕布局');
  } else {
    // 大屏幕布局
    console.log('大屏幕布局');
  }
};

// 组件挂载
onMounted(() => {
  // 更新并设置时间
  updateDateTime();
  setInterval(updateDateTime, 1000);
  
  // 初始化图表
  setTimeout(() => {
    initCharts();
  }, 100);
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
  
  // 检测屏幕尺寸
  checkScreenSize();
  
  // 页面加载动画
  const timeline = gsap.timeline({
    defaults: { duration: 0.6, ease: "power2.out" }
  });

  timeline
    .from('.header', { y: -30, opacity: 0, duration: 0.5 })
    .from('.left-panel', { x: -50, opacity: 0, duration: 0.5 }, '-=0.3')
    .from('.center-panel', { x: 50, opacity: 0, duration: 0.5 }, '-=0.3');
});

// 组件卸载前
onBeforeUnmount(() => {
  if (deviceTypeChart) {
    deviceTypeChart.dispose();
  }
  if (mapChart) {
    mapChart.dispose();
  }
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('resize', checkScreenSize);
});
</script>

<style scoped>
/* 全局样式 */
:root {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --text-color: #fff;
  --text-secondary: rgba(255, 255, 255, 0.65);
  --background: #0f1c3f;
  --card-bg: #1a2f5e;
  --card-border: #2a3f6f;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body, html {
  width: 100%;
  height: 100%;
  font-family: 'Microsoft YaHei', sans-serif;
  color: var(--text-color);
  background: var(--background);
  overflow: hidden;
}

.bi-screen {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* 顶部状态栏 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 20px;
  background: rgba(26, 47, 94, 0.8);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 10;
}

.header-left .title {
  font-size: 20px;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 0 10px rgba(24, 144, 255, 0.5);
}

.header-center .datetime {
  font-size: 16px;
  color: #fff;
}

.header-right .weather {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 14px;
}

.weather-icon {
  font-size: 20px;
}

/* 主内容区 */
.main-content {
  display: flex;
  flex: 1;
  padding: 20px;
  gap: 20px;
  overflow: hidden;
}

/* 左侧面板 */
.left-panel {
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 数据卡片 */
.data-card {
  background: rgba(26, 47, 94, 0.5);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(42, 63, 111, 0.8);
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #fff;
  display: flex;
  align-items: center;
}

.card-title::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 16px;
  background: var(--primary-color);
  margin-right: 8px;
  border-radius: 2px;
}

/* 状态概览 */
.status-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.status-item {
  background: rgba(26, 47, 94, 0.8);
  border-radius: 6px;
  padding: 12px;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.status-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

.status-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: #fff;
}

.status-icon.online {
  background: var(--success-color);
}

.status-icon.offline {
  background: #8c8c8c;
}

.status-icon.warning {
  background: var(--warning-color);
}

.status-icon.error {
  background: var(--error-color);
}

.status-info {
  flex: 1;
}

.status-count {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 2px;
}

.status-name {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 图表容器 */
.chart-container,
.map-content {
  width: 100%;
  height: 300px;
  background: rgba(26, 47, 94, 0.3);
  border-radius: 6px;
  margin-top: 10px;
}

/* 中间面板 */
.center-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.map-container {
  flex: 1;
  background: rgba(26, 47, 94, 0.5);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(42, 63, 111, 0.8);
  display: flex;
  flex-direction: column;
}

.map-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #fff;
  display: flex;
  align-items: center;
}

.map-title::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 16px;
  background: var(--primary-color);
  margin-right: 8px;
  border-radius: 2px;
}

.map-content {
  flex: 1;
  height: auto;
  min-height: 400px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }
  
  .left-panel {
    width: 100%;
  }
  
  .status-overview {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .status-overview {
    grid-template-columns: 1fr 1fr;
  }
  
  .header {
    flex-direction: column;
    height: auto;
    padding: 10px;
    text-align: center;
  }
  
  .header-left,
  .header-center,
  .header-right {
    margin: 5px 0;
  }
}
</style>
