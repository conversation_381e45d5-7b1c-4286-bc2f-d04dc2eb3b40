<template>
    <div >

        <el-form-item  :label="label">
            <slot></slot>
        </el-form-item>
        <!-- <div class="searchLabelBox_left" >
            <div class="searchLabel" >{{label}}</div>
            <div v-if="$slots.default" style="flex:1;" >
                <slot></slot>
            </div>
        </div> -->
        <!-- <div v-if="$slots.right" class="searchLabelBox_right" >
            <slot name="right"></slot>
        </div> -->
    </div>
</template>

<script>
    export default {
        props:{
            label:{
                type:String,
            },
            colon:{
                type:[<PERSON>olean],
                default:()=>{return false}
            },
        },
        created(){
            this.colon && (this.label = this.label + " :");
        },
    }
</script>

<style lang="scss" scoped>
</style>