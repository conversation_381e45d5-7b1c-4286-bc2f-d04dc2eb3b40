# 雾炮机 Modbus 从机模拟器使用说明

## 概述

本项目提供了两个雾炮机 Modbus 从机模拟器脚本，用于模拟8个雾炮机设备的行为。

## 脚本文件

### 1. mist_cannon_simulator.py (IP版本)
- **功能**: 模拟8个雾炮机，使用不同IP地址
- **IP地址范围**: ************* - *************
- **端口**: 502 (标准Modbus端口)
- **注意**: 需要配置网络接口支持多个IP地址

### 2. mist_cannon_simulator_ports.py (端口版本) - 推荐使用
- **功能**: 模拟8个雾炮机，使用不同端口
- **IP地址**: localhost (127.0.0.1)
- **端口范围**: 502 - 509
- **优势**: 无需特殊网络配置，易于测试

## 安装依赖

```bash
pip install pymodbus
```

## 运行模拟器

### 方法1: 直接运行端口版本 (推荐)
```bash
python mist_cannon_simulator_ports.py
```

### 方法2: 通过主调试脚本启动
```bash
python device_control_debugger.py
# 选择菜单项 "4. 启动雾炮从机模拟器"
```

## 连接信息

### 端口版本连接方式
```
雾炮机 1: localhost:502
雾炮机 2: localhost:503
雾炮机 3: localhost:504
雾炮机 4: localhost:505
雾炮机 5: localhost:506
雾炮机 6: localhost:507
雾炮机 7: localhost:508
雾炮机 8: localhost:509
```

### IP版本连接方式
```
雾炮机 1: *************:502
雾炮机 2: *************:502
雾炮机 3: *************:502
雾炮机 4: *************:502
雾炮机 5: *************:502
雾炮机 6: *************:502
雾炮机 7: *************:502
雾炮机 8: *************:502
```

## 寄存器映射

模拟器实现了完整的雾炮机寄存器映射 (4x寄存器模式):

### 控制寄存器
| 寄存器地址 | 功能 | 说明 |
|-----------|------|------|
| 40001 | 喷雾控制 | 1536=启动, 0=停止 |
| 40002 | 喷射方式 | 256=摇摆, 0=定点 |
| 40003 | 风机运行状态 | 只读状态 |
| 40004 | 水泵运行状态 | 只读状态 |
| 40005 | 上升状态 | 只读状态 |
| 40006 | 下降状态 | 只读状态 |
| 40007 | 左转状态 | 只读状态 |
| 40008 | 右转状态 | 只读状态 |
| 40009 | 手动模式 | 默认为1 |
| 40010 | 遥控器模式 | 状态寄存器 |
| 40011 | 远程模式 | 状态寄存器 |
| 40012 | 故障状态 | 只读状态 |
| 40013 | 遥控运行状态 | 只读状态 |
| 40014 | 缺水故障 | 只读状态 |
| 40015 | 设置零位 | 写入1触发零位设置 |
| 40016 | 故障复位 | 写入1触发故障复位 |

### 角度寄存器
| 寄存器地址 | 功能 | 说明 |
|-----------|------|------|
| 40017 | 摇摆起始角度 | 有符号16位整数 |
| 40019 | 摇摆结束角度 | 有符号16位整数 |
| 40020 | 当前旋转位置 | 只读，有符号16位整数 |
| 40021 | 当前俯仰位置 | 只读，有符号16位整数 |
| 40022 | 定点旋转角度 | 有符号16位整数 |
| 40023 | 定点俯仰角度 | 有符号16位整数 |
| 40026 | 左限位设置 | 有符号16位整数 |
| 40027 | 右限位设置 | 有符号16位整数 |
| 40028 | 每转脉冲数 | 默认3600 |

## 模拟功能

### 1. 喷雾控制
- 写入40001寄存器值1536启动喷雾
- 写入40001寄存器值0停止喷雾
- 启动时自动设置风机和水泵运行状态

### 2. 喷射模式
- 写入40002寄存器值256设置为摇摆模式
- 写入40002寄存器值0设置为定点模式

### 3. 摇摆运动模拟
- 在摇摆模式下，设备会在起始角度和结束角度之间来回摇摆
- 摇摆速度: 每100ms移动2度
- 自动更新左转/右转状态寄存器

### 4. 定点位置模拟
- 在定点模式下，设备会移动到指定的旋转和俯仰角度
- 移动速度: 每100ms移动1度
- 自动更新上升/下降/左转/右转状态寄存器

### 5. 故障模拟
- 随机生成缺水故障 (0.1%概率)
- 支持故障复位功能

### 6. 零位设置
- 写入40015寄存器值1触发零位设置
- 自动将当前位置重置为0度

## 测试示例

### 使用 device_control_debugger.py 测试
1. 启动模拟器
2. 运行 `python device_control_debugger.py`
3. 选择 "2. 控制雾炮"
4. 输入IP地址: `localhost` (端口版本) 或 `*************` (IP版本)
5. 选择相应的控制操作

### 使用Modbus客户端工具测试
可以使用任何Modbus TCP客户端工具连接到模拟器进行测试，例如:
- ModbusPoll
- QModMaster
- 自定义Python脚本

## 注意事项

1. **端口版本推荐**: 端口版本更容易部署和测试，推荐日常使用
2. **IP版本要求**: IP版本需要系统支持绑定多个IP地址
3. **防火墙**: 确保防火墙允许相应端口的TCP连接
4. **资源占用**: 8个模拟器会占用一定的CPU和内存资源
5. **日志输出**: 模拟器会输出详细的操作日志，便于调试

## 故障排除

### 常见问题
1. **端口被占用**: 确保指定端口未被其他程序使用
2. **IP地址绑定失败**: IP版本需要配置网络接口
3. **连接超时**: 检查防火墙设置和网络连接
4. **寄存器读写失败**: 确认寄存器地址映射正确

### 调试建议
1. 查看控制台日志输出
2. 使用Modbus客户端工具验证连接
3. 检查网络配置和防火墙设置
4. 确认pymodbus版本兼容性

## 扩展功能

模拟器支持以下扩展:
1. 添加更多设备类型
2. 自定义寄存器映射
3. 增加更复杂的故障模拟
4. 支持更多Modbus功能码
5. 添加数据记录和分析功能
