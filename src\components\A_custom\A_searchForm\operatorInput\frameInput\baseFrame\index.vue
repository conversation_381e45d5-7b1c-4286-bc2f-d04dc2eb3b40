<template>
    <el-dialog class="baseFrame" top="8vh" :title="config.title" width="870px" :visible.sync="show" :close-on-click-modal="false" append-to-body :before-close="beforeClose" >
        <div class="searchLayout" >
            <searchLabel label="方案查询">
                <div class="schemeQuery">
                    <schemeQueryInput :enablerShow="false" v-model="item.select" :val.sync="item" @keyReturnEvent="keyReturnEvent" v-for="item,index in searchListComputed()" :key="index" :label="item.name"></schemeQueryInput>
                </div>
            </searchLabel>
        </div>

        <div class="frameSalesman_head row-ver-center">
            <div class="row-between alignCenter grow1">
                <div class="ml5 mtb5">
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="searchForClick">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </div>
                <el-pagination
                    @current-change="(e)=>{queryParams.pageNum = e;getList()}"
                    :page-size.sync="queryParams.pageSize"
                    style="display: flex;justify-content: flex-end"
                    layout="prev, pager, next"
                    :current-page.sync="queryParams.pageNum"
                    :total="total">
                </el-pagination>
            </div>
            <div class="frameSalesmanSelect_head" >
                <div class="row-between ptb8" >
                    <div class="cursor-pointer ml8 bold">{{({0:'单选',1:'已选'})[Number(config.multiple)]}}</div>
                    <div class="color5582f3 cursor-pointer mr8  " @click="toggleSelectionAll()">清空</div>
                </div>
            </div>
        </div>

        <div class="row-between">
            <el-table @select="select" v-loading="loading" border ref="frameSalesman_table" :class="['frameSalesman_table',{'single_table':!config.multiple}]" @row-click="rowClick" :row-key="config.rowKey" :data="tableDataList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" :selectable="selectableDisable"  v-if="configDefault.checkbox" />
                <el-table-column v-hasPermi="item.permission" :show-overflow-tooltip="item.textOverflow" :width="item.tableWidth||200" v-for="item,index in finalList" :key="item.id" :label="item.name" :align="configDefault.align" :prop="item.property" >
                    <template slot-scope="{row}">
                        <slot :name="item.property" :row="row" v-if="!!$scopedSlots[item.property]" ></slot>
                        <div v-else >
                            <proxyModule v-if="item.tempalte" :row.sync="row" :item.sync="item"></proxyModule>
                            <dict-tag v-else-if="item.selectType==2" :options="dicts[item.sysDict||item.dict]||[]" :value="row[item.property]"/>
                            <div v-else>{{row[item.property]}}</div>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div class="plr8 scrollbarClass frameSalesmanSelect_content" :style="{height:tableHeight+'px'}">
                <div class="row-between frameSalesmanSelect_item mtb5" v-for="item,index in selectedList" :key="index" >
                    <div class="text-ellipsis" >{{item[config.echo[1]]}}</div>
                    <div class="cursor-pointer userNone" @click="deleteSelectItem(item)">✖</div>
                </div>
            </div>
        </div>

        <div slot="footer" class="dialog-footer" align="center">
            <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>

    </el-dialog>
</template>

<script>
import pageMixin from "@/components/A_custom/mixins/page.js"
import tableMixin from "@/components/A_custom/mixins/table.js"
import searchMixin from "@/components/A_custom/mixins/search.js"
import {tansParams} from '@/utils/ruoyi.js'
import request from '@/utils/request'
    export default {
        mixins:[pageMixin,tableMixin,searchMixin],
        props:{
            show:{
                type:Boolean,
                default:()=>{return false}
            },
            title:{
                type:[String,Number],
                default:()=>{return "测试弹框"}
            },
            config:{
                type:Object,
                default:()=>{return{}}
            },
            defaultQueryKeys:{
                type:Object,
                default:()=>{return{}}
            },
        },
        data(){
            return {
                dictApi:{
                    sysDict:require("@/api/system/ydDict/data.js").getDicts,
                    dict:require("@/api/system/dict/data.js").getDicts,
                },
                selectQueryList:[],
                buttonLoading:false,//确定按钮加载
                selectedList:[],//选中的数据
                tableHeight:495, // 列表高度
                tableDataList:[], // 列表值数据
                tableKeysList:[],//列表keys数据
                cache:false,
                queryParams:{},
                total:0,
                optionObj:{},//下拉集合
                originalArray:[],//原数组
                url:null,
            }
        },
        watch:{
            show:{
                handler(N){
                    if(N && !this.cache){
                        this.oneEvent();
                    }
                },
                deep:true,
            },
        },
        computed: {
            // 控制默认值  弹框使用
            searchListComputed:function() {
                return function(){
                    // 是否可以查询：selectable ， isDefault：全部条件 
                    if(this.tableKeysList && this.tableKeysList.length>0){
                        let list = this.tableKeysList.filter(x=>{
                            return (x.isDefault && x.selectable)
                        })
                        return list
                    }
                }
            },
        },
        methods:{
            // 搜索
            searchForClick(){
                let listFilter = this.tableKeysList.filter(x=>{
                    return this.queryParamsKeyList.includes(x.id)
                });
                let newList = JSON.parse(JSON.stringify(listFilter));
                    newList.forEach(x => {
                        if(x.value==undefined||!x.value){x.value = null}
                });
                this.selectQueryList = newList;
                this.getList();
            },
            // 列表选择按钮禁用函数:需要从部传入配置
            selectableDisable(row){
                if(this.config?.checkboxDisable){
                    return this.config.checkboxDisable(row)
                }else{
                    return true;
                }
            },
            submitForm(){
                if(this.selectedList.length==0) return this.$message.error('请选择一条数据奥！');
                this.$emit('frameSalesmanRetureEvent',this.selectedList);
                this.$emit("update:show",false)
            },
            cancel(){
                this.$emit("beforeClose","beforeClose")
                this.$emit("update:show",false)
            },
            async oneEvent(){
                // 弹框打开 获取 frameName ，通过 frameName 找到 页面的列定义
                if(this.config.other){
                    try{
                        await (this.tableKeysList = require(`@/views/${this.config.url}/js.js`).default.fieldList)
                    }catch(err){
                        await (this.tableKeysList = require(`@/views/${this.config.url}/config.js`).fieldList)
                    }
                }else{
                    try{
                        await (this.tableKeysList = require(`@/views/${this.config.data.reflection}/js.js`).default.fieldList)
                    }catch(err){
                        await (this.tableKeysList = require(`@/views/${this.config.data.reflection}/config.js`).fieldList)
                    }
                }
                // 在数组每条数据中，添加 select 作为 点击复选框控制
                this.tableKeysList.forEach(x=>{ 
                    x.select = false;
                });
                this.tableListApi = (paging,data)=>{
                    return request({
                        url: this.config.apiUrl+ "?" + tansParams(paging),
                        method: 'post',
                        data: data
                    })
                }
                await (this.cache = this.config.cache); //缓存
                await (this.selectQueryList = this.tableKeysList.filter(x=>{return x.select})); //搜索框选中数据返回
                await this.dictEvnet(this.tableKeysList);//字典

                console.log(this.config.defaultQueryKeys,"--------this.defaultQueryKeys")
                await this.getList();
            },
            select(selectedList){
                // 限制单选 和 多选
                if(!this.config.multiple && selectedList.length > 1){
                    let del_row = selectedList.shift()
                    this.$refs.frameSalesman_table.toggleRowSelection(del_row, false)
                }
            },
            defaultQueryKeysListEvent(judge){
                if(!this.config.defaultQueryKeys) return this.inspectFile();
                let list = this.inspectFile();
                let keys = Object.keys(this.config.defaultQueryKeys)
                keys.forEach(key=>{
                    if(this.config.defaultQueryKeys[key]){
                        let index = null
                        let obj = list.find((x,i)=>{ index = i ; return key == x.property });
                        if(obj) list[index] = {...obj,select:true,value:typeof this.config.defaultQueryKeys[key] == "string" ? this.config.defaultQueryKeys[key] : this.config.defaultQueryKeys[key].value};
                    }
                })
                if(!judge) return list.filter(x=>{return x.select});
                return this.fnSerialize(false,JSON.parse(JSON.stringify(list)))
            },
            // 点击选中数据
            rowClick(row,event){
                let judge = true;
                if(this.config?.checkboxDisable){
                    judge = !this.config?.checkboxDisable(row)
                }
                if(!judge) return;
                console.log(row)
                if(this.config.multiple){ //多选
                    let index = null;
                    let obj = this.selectedList.find((x,i)=>{
                        index = i;
                        return row[this.config.keys] == x[this.config.keys]; 
                    })
                    if(obj){
                        this.$refs.frameSalesman_table.toggleRowSelection(row,false);
                    }else{
                        this.$refs.frameSalesman_table.toggleRowSelection(row,true);
                    }
                }else{//单选
                    if(this.selectedList.length > 0){ //判断有没有值 , 有值时候清空,没有值,添加
                        this.$refs.frameSalesman_table.clearSelection();
                        this.$refs.frameSalesman_table.toggleRowSelection(row,true);
                    }else{
                        this.$refs.frameSalesman_table.toggleRowSelection(row,true);
                    }
                }
            },
            beforeClose(){
                this.$emit("update:show",false)
                this.$emit("beforeClose","beforeClose")
            },
            resetQuery(){
                if(this.config.other){
                    try{
                        (this.tableKeysList = JSON.parse(JSON.stringify(require(`@/views/${this.config.url}/js.js`).default.fieldList)))
                    }catch(err){
                        (this.tableKeysList = JSON.parse(JSON.stringify(require(`@/views/${this.config.url}/config.js`).fieldList)))
                    }
                }else{
                    try{
                        (this.tableKeysList = JSON.parse(JSON.stringify(require(`@/views/${this.config.data.reflection}/js.js`).default.fieldList)))
                    }catch(err){
                        (this.tableKeysList = JSON.parse(JSON.stringify(require(`@/views/${this.config.data.reflection}/config.js`).fieldList)))
                    }
                }
                this.deep.resetting = !this.deep.resetting
            },
            // 清空所有选中
            toggleSelectionAll(){
                this.$refs.frameSalesman_table.clearSelection();
                this.selectList = [];
            },
            // 删除选中的数据
            deleteSelectItem(row){
                let index= null
                let obj = this.$refs.frameSalesman_table.selection.find((x,i)=>{ index = i; return row[this.config.keys] == x[this.config.keys]});
                this.$refs.frameSalesman_table.toggleRowSelection(obj,false);
            },
        },
    }
</script>

<style lang="scss">
.baseFrame{
    .el-dialog__body{
        padding-top: 0px;
    }
}
.baseFrame .schemeQuery{
    display: flex;
}
.frameSalesman{
    &_head{
        margin-top: 15px;
        border-top:1px solid #e2e7ef;
        border-left:1px solid #e2e7ef;
        border-right:1px solid #e2e7ef;  
    }
}
.frameSalesmanSelect{
    &_head{
        width: 250px;
        border-left:1px solid #e2e7ef;
    }
    &_content{
        width: 250px;
        border-top:1px solid #e2e7ef;
        border-right:1px solid #e2e7ef;
        border-bottom:1px solid #e2e7ef;
        overflow:auto;
    }
}
.single_table tr th .cell .el-checkbox__inner{
    display: none;
    position: relative;
}
.totalCommissionRules{
    .el-input-number--medium{
        width: 100% !important;
    }
}
.searchFrame_input{
    .el-input__inner{
        text-overflow: ellipsis !important;
        white-space: nowrap;
        overflow: hidden;
    }
}
.el-popover{
    padding:0;
}
// .frameSalesman_table .el-table__body-wrapper::-webkit-scrollbar {
//     width: 10px !important;
//     height: 10px !important;
// }
.frameSalesman_table .el-checkbox__label {
    display: none;
}
</style>