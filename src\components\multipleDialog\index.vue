<!-- 
    多选框的dialog：
        传参：:getData="getData
            传递一个getData函数，return对象；对象需要包含的属性：title:标题；interface:列表数据请求接口；configUrl:config.js文件路径；formInfo: this.form；传递给后端的表单
        传参例子：
            getData() {
                return {
                    title: '用户管理列表(多选)', // 标题
                    interface: listDemo, // 列表数据请求接口
                    configUrl: 'order/template/demo', // config.js文件路径
                    formInfo: this.form, // 传递给后端的表单
                }
            }

        传参2：keyWord：处理列表回显的字段(为字符串)
        传参例子：keyWord="subDemoList" (subDemoList正常应为数组)
 -->
<template>
    <div>
        <el-input
            placeholder="请输入内容"
            v-model="showUserDialogNickName"
            class="userDialog"
            :disabled="true">
            <el-button type="danger" slot="append" size="mini" icon="el-icon-search" @click="isShowUserDialog" ></el-button>
        </el-input>

        <div>
            <el-dialog
                :title=title
                :visible.sync="isShowDialog"
                width="60%"
                :close-on-click-modal="false"
                :append-to-body="true"
                :before-close="handleClose">
                <!-- 搜索过滤条件 -->
                <searchForm2 @change="searchFormChange" :searchList.sync="fieldList" :keys="url"
                    :formItemNum="formItemNum" @getList="getList" :dicts="dicts"></searchForm2>

                <div class="mainContent">
    
                    <el-table v-loading="loading" ref="stepTable" :data="tableDataList" row-key="id" @selection-change="handleSelectionChange" @select-all="onSelectAll($event,tableDataList)" @select="oneSelect">
    
                        <el-table-column type="selection" :reserve-selection="true" align="center" width="50" />
    
                        <el-table-column v-for="item in fieldList" :key="item.id" :label="item.name" align="center" :prop="item.columnName" width="150">
                            <template slot-scope="{row}">
                                <dict-tag v-if="item.selectType == 2" :options="dicts[item.sysDict || item.dict] || []"
                                :value="row[item.property]" />
                                <div v-else>{{ row[item.columnName] }}</div>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 表格右侧查看已选的表 -->
                    <div class="rightShowSelectList">
                        <div class="selectItemTitle">已选项</div>
                        <div class="listContent">
                            <div class="selectItem" v-for="(item, index) in selectUserDialog" :key="index">
                                <div>{{ item.name }}</div>
                                <i class="el-icon-circle-close" @click="delSelectItem(item)"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <pagination
                    v-show="total>0"
                    :total="total"
                    :page.sync="queryParams.pageNum"
                    :limit.sync="queryParams.pageSize"
                    @pagination="getList"
                    />

                <span slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="finish(true)">确 定</el-button>
                    <el-button @click="finish(false)">取 消</el-button>
                </span>
            </el-dialog>
        </div>
    </div>

</template>

<script>
import { listDemo } from "@/api/system/demo";
import pageMixins from "@/components/A_custom/mixins/page.js"
  export default {
    mixins: [pageMixins],

    props: {
        getData: {
            type: Function,
            default: () => {}
        },
        keyWord:{
            type: String,
            default: () => '' 
        }
    },

    data() {
      return {
        userList: [], // 存储列表数据
        loading: false, // 遮盖层
        isShowDialog: false,
        title: '提示',
        fieldList: null,
        // isOneSelect: this.getData().isOneSelect, // 是否单选
        // 日期范围
        dateRange: [],
        // 总条数
        total: 0,
       
        // 存储当前页面的选中信息
        currentSelectUserDialog: [],

        // 存储回显的用户选中信息
        copySelectUserDialog: [],

        // 存储用户选中的信息
        selectUserDialog: [],

        // input框展示的信息
        showUserDialogNickName: '',

        interfaceAPI: () => {},

        selectedRow: {}, // 存储当前单选选中的行数据

        // 存储字典
        // dicts: {},
        
        tableListApi: this.getData().interface, // 发送接口请求

        url: this.getData().configUrl,
        formItemNum: 10, //默认搜索展示多少个条件

        // 默认查询参数
        defaultQueryKeys: {

        },

        // 存储双选回显的时候的id
        popUp: [],
        nameList:[],

         // 存储单选回显的时候的id
         remark: '',

         isUseSelectFun: false, // 是否启用点击的功能
      };
    },

    async created(){
        this.interfaceAPI = this.getData().interface // 父组件传递过来的
        this.title = this.getData().title
        this.selectUserDialog = this.getData().formInfo[this.keyWord] || []
        this.showUserDialogNickName = this.selectUserDialog.map(item => item.name).join(',')

        let configJS = this.inspectFile()
        this.fieldList = configJS.filter(item => item.listable == true).sort((a, b) => {
            return a.sort - b.sort; // 按照 sort 属性的大小进行升序排序
        }) // config数据

    },
    
    watch: {
        isShowDialog(){ // 控制页面打开的bool
            if(this.isShowDialog && this.selectUserDialog?.length) {
                this.selectUserDialog = this.getData().formInfo[this.keyWord] || []
                this.selectUserDialog.forEach(item => {
                    setTimeout(() => {
                        let result = this.tableDataList.find(items => items.id == item.id)
                        if(!result) return
                        this.$refs.stepTable.toggleRowSelection(result, true)
                    }, 0);
                })

            }else if(this.isShowDialog && this.remark){ // 这是单选的。先不管
                // 页面第一次打开的时候，要做回显，之后就不用了
                this.selectUserDialog = this.tableDataList.filter(item => item.remark == this.remark);
                setTimeout(() => {
                    this.$refs.stepTable.toggleRowSelection(this.selectUserDialog, true)
                }, 0);
                // 回显完毕了把那个删除了，不让他再走
                this.remark = ''
            }
            setTimeout(() => {
                    this.isUseSelectFun = true
                }, 50);
        },
        // dicts() {
        //     this.getList()
        // },
        // 监听列表数据变化
        tableDataList() {
            if(this.isShowDialog) {
                // this.selectUserDialog = this.tableDataList.filter(item => this.popUp.includes(item.id.toString()));
                if(!this.selectUserDialog.length) return
                setTimeout(() => {
                    this.$refs.stepTable.clearSelection()
                }, 0);
                this.selectUserDialog.forEach(item => {
                    setTimeout(() => {
                        let result = this.tableDataList.findIndex(items => items.id == item.id)
                        if(result == -1) return
                        this.$refs.stepTable.toggleRowSelection(this.tableDataList[result], true)
                    }, 0);
                })
            }
        },

        currentSelectUserDialog(newVal, oldVal) { // 当前页面选中的存储起来
            if((newVal.length < oldVal.length && newVal.length != 0)){  //这是减少数据的
                let missingObjects = oldVal.filter(obj => !newVal.find(newObj => newObj.id === obj.id));
                this.selectUserDialog = this.selectUserDialog.filter(obj2 => !missingObjects.some(obj1 => obj1.id === obj2.id));
                // this.currentSelectUserDialog = this.currentSelectUserDialog.filter(obj2 => !missingObjects.some(obj1 => obj1.id === obj2.id));
            }
        }

    },
    
    methods: {
        handleRowClick(row) {
            // 点击行时触发选中单选框
            this.selectedRow = row;
            },
        handleRadioChange(row) {
            // 单选框改变时触发，同步选中行数据
            this.$refs.stepTable.toggleRowSelection(row);
        },

        handleClose(done) {
            this.isShowDialog = false
        },

        // 多选框
        handleSelectionChange(selectInfo) {
        },

        // 上面的全选按钮
        onSelectAll(event, listData) {
            // event : 当前所有页面选中的值； listData : 当前页面的列表数据
            if(event.length == 0) {
                // 这个是点击了全选然后全部没选中的。
                let filteredSelectUserDialog = []
                listData.forEach(obj => {
                    if (event.some(e => e.id !== obj.id)) {
                        filteredSelectUserDialog.push(obj);
                    }
                });
                this.selectUserDialog = this.selectUserDialog.filter(obj => !listData.some(item => item.id === obj.id));
            }else{
                // 遍历listData的每一项，如果当前项的id与event数组里面的对象的id不同，并且当前项的id与selectUserDialog数组里面的对象的id相同，则把删除selectUserDialog数组里面的和当前项id相同的对象；如果当前项的id与event数组里面的对象的id相同，并且当前项的id不在selectUserDialog数组里面的每一项的对象id，则把当前对象添加到selectUserDialog里面去
                listData.forEach(obj => {
                    const idExistsInArr1 = event.some(item => item.id === obj.id);
                    const idExistsInArr3 = this.selectUserDialog.some(item => item.id === obj.id);

                    if (!idExistsInArr1 && idExistsInArr3) {
                        this.selectUserDialog = this.selectUserDialog.filter(item => item.id !== obj.id); // 从 selectUserDialog 中删除当前对象
                    } else if (idExistsInArr1 && !this.selectUserDialog.some(item => item.id === obj.id)) {
                        this.selectUserDialog.push(obj); // 将当前对象添加到 selectUserDialog 中
                    }
                });

            }
        },

        // 点击确定或取消按钮
        finish(bool) {
            if(bool){ // 点确定
                this.copySelectUserDialog = this.selectUserDialog
                this.showUserDialogNickName = this.copySelectUserDialog.sort((a, b) => a.id - b.id).map(item => item.name).join(',')
                this.$emit('backData', this.selectUserDialog, false)
            }
            this.isShowDialog = false
        },

        // 点击弹窗事件
        isShowUserDialog() {
            this.isShowDialog = true
        },

        // 右侧按钮
        scopedSlotsEmit() {

        },

        // 右侧表格的删除按钮--多选
        delSelectItem(item) {
            this.selectUserDialog = this.selectUserDialog.filter(i => i.id != item.id)
            this.tableDataList.forEach(i => {
                if(i.id == item.id){
                    setTimeout(() => {
                        this.$refs.stepTable.toggleRowSelection(i, false)
                    }, 0);
                }
            })
            if(!this.selectUserDialog.length){
                this.$refs.stepTable.clearSelection()
            }
        },

        // 选中的
        oneSelect(selection, row) {
            // selection：所有已加载的选中事件(未加载的拿不到);  row：当前点击的那一行的数据
            let result = this.selectUserDialog.find(item => item.id == row.id)
            // 如果 row 不在 selection 里面，则证明点的是增加数据
            if(!result) {
                this.selectUserDialog.push(row)
            }else{
                // 如果 row 在 selection 里面，则证明点的是减少数据
                this.selectUserDialog = this.selectUserDialog.filter(item => item.id !== row.id)
            }
        },

    }
  };
</script>

<style scoped lang="scss">
.userDialog{
    width: 223px;
    ::v-deep .el-input-group__append{
        background: #2391fb !important;
        color: #fff !important;
    }
}
.mainContent{
    display: flex;
    .rightShowSelectList{
        width: 200px;
        height: 490px;
        margin-left: 20px;
        border: 1px solid #Ccc;
        // box-shadow: 0 0 3px -1px #333;
        padding: 10px 0;
        // overflow-y: scroll;
        // scrollbar-width: none;
        // -ms-overflow-style: none;

        ::-webkit-scrollbar {
            /*隐藏滚轮*/
            display: none;
        }
        .listContent{
            width: 200px;
            height: 430px;
            overflow-y: scroll;
            scrollbar-width: none;
        }
        .selectItemTitle{
            height: 30px;
            line-height: 30px;
            margin-left: 15px;
            font-size: 13px;
            color: #000;
            font-weight: 800;
            margin-bottom: 10px;
        }
        .selectItem{
            width: 98%;
            height: 40px;
            line-height: 40px;
            padding-left: 20px;
            padding-right: 8px;
            display: flex;
            justify-content: space-between;
            div{
                width: 85%;
                white-space: nowrap; 
                overflow: hidden; 
                text-overflow: ellipsis;
                color: #1890ff;
                padding-left: 5px;
                border-bottom: 1px solid #dfe6ec;
            }
            i{
                line-height: 40px;
                cursor: pointer;
            }
            i:hover{
                color: #5cb6ff;
            }
        }
        .selectItem:hover{
            background: #f5f7fa;
        }
    }
}
.oneSelect{
    /* 使用 CSS 隐藏全选按钮 */
    ::v-deep .cell>.el-checkbox>.el-checkbox__input {
        display: none !important;
    }
}
</style>