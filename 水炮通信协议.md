 # 水炮设备 WebSocket 通信协议

 ## 1. 概述

本文档定义了客户端与服务端之间通过 WebSocket 对水炮设备进行状态查询和远程控制的通信协议。

- **WebSocket URL**: `ws://<your_server_address>/ws/device`

 ## 2. 通信格式

所有消息均为 JSON 格式，并遵循统一的 `WebSocketMessage` 结构。

```json
{
  "type": "MESSAGE_TYPE",
  "requestId": "UNIQUE_ID",
  "payload": { ... }
}
```

- `type`: 消息类型 (string)
- `requestId`: 客户端生成的唯一请求ID (string)，用于异步匹配响应。
- `payload`: 消息载荷 (object)，具体结构取决于 `type`。

 ## 3. 消息类型

### 3.1. 客户端 -> 服务端

#### 3.1.1. 获取水炮状态 (GET_STATUS)

**说明**:
客户端发送此消息以获取指定IP地址的水炮的实时状态。

**Payload 结构**:
```json
{
  "ip": "*************",
  "deviceType": 1
}
```

- `ip` (string, required): 水炮设备的IP地址。
- `deviceType` (integer, required): 设备类型，对于水炮，此值固定为 `1`。

#### 3.1.2. 控制水炮 (CONTROL_DEVICE)

**说明**:
客户端发送此消息以控制水炮的特定功能，例如开关阀门、调整角度等。

**Payload 结构**:
```json
{
  "ip": "*************",
  "deviceType": 1,
  "type": "valve_on",
  "value": 1
}
```

- `ip` (string, required): 水炮设备的IP地址。
- `deviceType` (integer, required): 设备类型，固定为 `1`。
- `type` (string, required): 控制指令类型。例如 `valve_on`, `pan_left`, `tilt_up` 等。
- `value` (integer, required): 控制指令的值。通常 `1` 表示启动/开启，`0` 表示停止/关闭。

### 3.2. 服务端 -> 客户端

#### 3.2.1. 水炮状态更新 (STATUS_UPDATE)

**说明**:
服务端在收到 `GET_STATUS` 请求后，返回水炮的当前状态。

**Payload 结构**:
```json
{
  "valve_status": true,
  "horizontal_angle": 45.5,
  "vertical_angle": 30.0
  // ... 其他状态字段
}
```

- `valve_status` (boolean): 阀门状态 (true: 开启, false: 关闭)。
- `horizontal_angle` (float): 水平角度。
- `vertical_angle` (float): 垂直角度。

#### 3.2.2. 控制结果 (CONTROL_RESULT)

**说明**:
服务端在收到 `CONTROL_DEVICE` 请求后，返回操作的执行结果。

**Payload 结构**:
```json
{
  "success": true
}
```

- `success` (boolean): 指令是否执行成功。

#### 3.2.3. 错误响应 (ERROR)

**说明**:
当处理发生错误时，服务端会发送此消息。

**Payload 结构**:
```json
{
  "message": "错误信息描述"
}
```

- `message` (string): 错误的详细描述。
