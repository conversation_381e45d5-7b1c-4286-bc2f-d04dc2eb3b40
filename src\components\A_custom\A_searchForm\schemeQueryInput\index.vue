<template>
    <div class="schemeQuery_item">
        <checkboxLabel v-model="val.select" ref="checkboxLabel" @input="checkboxInput" :label="label" :disabled="disabled" ></checkboxLabel>
        <!-- 操作符 -->
        <el-select v-if="enablerShow" class="operatorclass" @change="operatorChange" filterable size="mini" v-model="val.compType" placeholder="操作符">
            <el-option v-for="item,index in operatorOptionsFilter" :key="index" :label="item.label" :value="Number(item.value)" />
        </el-select>
        <!-- 值 -->
        <components :dicts="dicts" ref="inputComponents" v-model="val.value" :config="{data:val,...config}" :is="selectTypeName[val.selectType]||'textInput'" ></components>
        <!-- 删除 -->
        <el-button v-if="!disabled" class="schemeQuery_item_delete" size="mini" icon="el-icon-delete" circle></el-button>
    </div>
</template>

<script>
import {insertConfiguration,inputJudgeOperator,selectTypeName} from "./index.js"
import checkboxLabel from "../compose/checkboxLabel/index.vue"//表头
import textInput from "../operatorInput/textInput/index.vue"//普通
import searchInput from "../operatorInput/searchInput/index.vue"//下拉
import numberInput from "../operatorInput/numberInput/index.vue"//数字
import timeInput from "../operatorInput/timeInput/index.vue"//时间
import frameInput from "../operatorInput/frameInput/index.vue"//弹框
    export default {
        inject:[ "deep" ],
        components:{
            checkboxLabel,textInput,searchInput,numberInput,timeInput,frameInput
        },
        props:{
            disabled:{
                type:Boolean,
                default:()=>{return true}
            },
            operatorOptions:{
                type:Array,
                default:()=>{ return [] }
            },
            label:{
                type:String,
                default:()=>{return ""}
            },
            enablerShow:{
                type:Boolean,
                default:()=>{return true}
            },
            val:{
                type:[Object],
                default:()=>{
                    return {
                        property:"",//字段名
                        name:"",// 展示表头名
                        javaType:"",//类型
                        value:null,//值
                        operType:"",//操作类型 [增删改查]
                        compType:null,// 操作符号
                        sort:1,
                        dict:null,//字典
                        isDefault:false,//是否默认字段
                        selectType:null, // 控件类型
                        select:false,//控制复选框
                        /*
                            2:数字类型,
                            3:选择框,
                            4:时间,
                            5:弹框,
                        */  
                    }
                }
            },
            dicts: {
                type: Object,
                default: () => { return {} },
            }
        },
        data(){
            return{
                checkboxValue:false,
                selectTypeName:{
                    1:"textInput",//输入框
                    2:"searchInput",//选择下拉
                    3:"numberInput",//数字输入框
                    4:"timeInput",//数字输入框
                    5:"frameInput",//弹框输入
                },
                config:{
                    multiple:false,//是否多选
                    disabled:false,//禁用
                    range:false,//范围
                },
                operatorOptionsFilter:[], // 过滤之后的操作符选项
                operatorOptionsOneJudge:true,//执行一次
            }
        },
        mounted() {
            if(this.operatorOptionsOneJudge){
                let judgeList = inputJudgeOperator[this.val.selectType]||[];
                this.operatorOptionsFilter = this.operatorOptions.filter(x=>{
                    return judgeList.includes(x.value)
                });
                this.operatorOptionsOneJudge = false;
            }
            this.config = insertConfiguration(this.val.compType,this.val.selectType);
        },
        computed:{
            deepComputed(){
                return this.deep()
            }
        },
        watch:{
            "deepComputed.resetting":{ //重置
                handler(e){
                    this.operatorChange(this.val.compType)
                },
                deep:true,
            },
            "val.value":{
                handler(e){
                    // 输入后默认选择勾选或者关闭勾选
                   (!this.$refs.checkboxLabel.values && (e != "" || e != null || e!=undefined)) && (this.$refs.checkboxLabel.values = true,this.$refs.checkboxLabel.$emit('input',true));
                   (this.$refs.checkboxLabel.values && (e == "" || e == null || e==undefined)) && (this.$refs.checkboxLabel.values = false,this.$refs.checkboxLabel.$emit('input',false));
                },
                deep:true,
            },
        },
        methods:{
            checkboxInput(judge){
                this.$emit('keyReturnEvent',{data:this.val,judge});
            },
            operatorChange(e){
                this.config = insertConfiguration(e,this.val.selectType);
                if(!this.val.select) this.$refs.inputComponents.inputClean(); //清空每个子级的输入框
                this.$refs.checkboxLabel.inputClean();
            },
        },
    }
</script>

<style lang="scss">
.schemeQuery_item{
    display:flex;
    align-items:center;
    margin-right:10px;
    &_delete{
            margin:0 5px !important;
    }
    .schemeQueryInput{
        width:170px;
        margin-left:5px;
    }
    .operatorclass{
        width:110px;
        margin-left:5px;
    }
}
.schemeQuery_item:not(:last-child){
    margin-bottom:5px;
}

</style>