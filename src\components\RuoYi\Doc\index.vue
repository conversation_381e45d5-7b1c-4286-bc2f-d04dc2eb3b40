<template>
  <div>
    <el-dropdown trigger="click" @command="handleMessage">
    <el-badge :value="messageOptions.length" class="item" :max="99">
      <svg-icon  icon-class="message" style="color:#fff;font-size:20px"/>
    </el-badge>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item v-for="item of messageOptions" :key="item.id"  :command="item.id" :class="item.flag?'read':'unread'" >
        <div class="row-between row-ver-center">
          <div class="bold">{{ item.title }}</div>
          <i :class="item.flag?'el-icon-check':''"></i>
        </div>
        <div class="message"><i class="el-icon-chat-line-round"></i>{{ item.message }}</div>
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
    
  </div>
</template>
 
<script>
export default {
  name: 'RuoYiDoc',
  data() {
    return {
      messageOptions: [
        {id:1, title: '首个信息提醒', message: '你有资源包即将用完，点击去购买按需选购资源包。数据仅供参考，请以实际使用情况为准。',flag:false },
        {id:2, title: '首个信息提醒1', message: '你有资源包即将用完，点击去购买按需选购资源包。数据仅供参考，请以实际使用情况为准。',flag:true },
        {id:3, title: '首个信息提醒2', message: '你有资源包即将用完，点击去购买按需选购资源包。数据仅供参考，请以实际使用情况为准。',flag:true },
        {id:4, title: '首个信息提醒3', message: '你有资源包即将用完，点击去购买按需选购资源包。数据仅供参考，请以实际使用情况为准。',flag:true  }
      ]
    }
  },
  methods: {
    goto() {
      window.open(this.url)
    },
    handleMessage(){

    }
  }
}
</script>
<style>
.el-badge__content.is-fixed{
  top: 7px !important;
  right: 10px !important;
}
.el-badge__content{
  font-size: 10px;
  line-height: 16px;
  padding: 0 5px;
}
.read{
  color: #a2a4a8;
}
.unread{
  color: #000000;
}
.message{
  width: 230px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-bottom: 1px solid #EFF3FB;
}
</style>