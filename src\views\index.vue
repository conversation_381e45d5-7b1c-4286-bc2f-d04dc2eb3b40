<template>
  <div class="box">
    <div class="container">
      <StatisticalData v-if="true" />
      <div class="consoleBox mt10" v-else>
        <!-- <img src="../assets/images/jinei.png" class="leftImg" alt=""> -->
        <div v-for="(item, index) in routerList" :key="index">
          <div class="consoleBox-item row-ver-center" v-if="!item.hidden && item.meta" @click="goPath(item)">
            <div :style="{ 'background': randomHexColor(index) }"
              style="padding: 10px 0 10px 12px;border-radius: 10px;margin-right: 10px;box-shadow: 0px 4px 8px 0px rgba(0,0,0,0.2);">
              <svg-icon :icon-class="item.meta.icon" v-if="item.meta.icon"></svg-icon>
              <svg-icon icon-class="list" v-else></svg-icon>
            </div>
            <div>{{ item.meta.title }}</div>
          </div>
        </div>
      </div>
      <div style="width: 100%;height: 10px;"></div>
      <!-- <div class="container column-ver-center">
        <img src="../assets/logo/logo.png" alt=""
          style="width: 300px;height: 300px;margin-top: 50px;margin-bottom: 20px;border: 1px solid rgba(0, 21, 41, 0.35);box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35)">
        <h1 style="text-shadow: 2px 0 6px rgba(0, 21, 41, 0.35)">广州链销科技有限公司</h1>
      </div> -->
    </div>
    <div class="fixed">
      <div class="row-center bottom ">
        <div>版本号：{{ configData.version }}</div>
        <div style="margin-left: 20px;">{{ configData.date }}</div>
        <div style="margin-left: 20px;"><el-link type="primary" @click="openAnnouncement">更新日志</el-link>
        </div>
      </div>
      <el-dialog :title="title" :visible.sync="open" width="80%" append-to-body>
        <div class="dialog-content">
          <div v-html="richTextHtml"></div>
        </div>
      </el-dialog>
    </div>
  </div>
  <!-- <div class="box">

  </div> -->
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'
import { mapGetters, mapState } from "vuex";
import Item from '../layout/components/Sidebar/Item'
import StatisticalData from '@/components/statisticalData/index.vue'
import { getNotice, noticeConfig } from "@/api/home";
import { getVersion, setVersion } from '@/utils/auth'

export default {
  components: {
    Item, StatisticalData
  },
  data() {
    return {
      // 版本信息
      configData: {
        version: '1.1.0',
        date: '2024-08-19',
      },
      title: '',
      open: false,
      richTextHtml: '',

      routerList: [],
      randomHexColor(index) {
        let color = null
        switch (index) {
          case 0:
            color = '#176cb2'
            break;
          case 1:
            color = '#3975C6'
            break;
          case 2:
            color = '#006A85'
            break;
          case 3:
            color = '#008D8E'
            break;
          case 4:
            color = '#25a969'
            break;
          case 5:
            color = '#f1c40f'
            break;
          case 6:
            color = '#c0392b'
            break;
          case 7:
            color = '#00a1ff'
            break;
          case 8:
            color = '#34495e'
            break;
          case 9:
            color = '#9b59b6'
            break;
          case 10:
            color = '#2980b9'
            break;
          case 11:
            color = '#81a7f2'
            break;
          case 12:
            color = '#A2A2A7'
            break;
          default:
            color = '#' + ("00000" + ((Math.random() * 0x1000000) << 0).toString(16)).substr(-6)
            break;
        }
        return (
          color
        );
      },
    };
  },
  mounted() {
    let list = this.sidebarRouters.filter(item => {
      return !item.hidden
    })
    list.forEach(item => {
      if (item.children) {
        item.children.forEach(child => {
          if (!child.hidden) {
            let obj = {
              ...child,
              basePath: item.path,

            }
            this.routerList.push(obj)
          }
        })
      }
    })
    this.getVersionFun()
  },
  created() {
  },
  computed: {
    ...mapGetters(["sidebarRouters"]),
  },
  methods: {
    // 获取版本号
    async getVersionFun() {
      try {
        const res = await noticeConfig()
        if (res.code == 200) {
          this.configData = res.data
          console.log("🚀 ~ getVersionFun ~ getVersion:", getVersion(),res.data.version)
          if (getVersion() != res.data.version) {
            setVersion(res.data.version)
            this.openAnnouncement()
          }
        }
      } catch (err) {
        console.error(err);
      }
    },
    /**
     * 打开公告
     */
    openAnnouncement() {
      this.open = true
      this.title = '更新日志'
      this.getNotice()
    },
    // 获取富文本公告
    async getNotice() {
      try {
        const res = await getNotice()
        if (res.code == 200) {
          this.richTextHtml = res.data.content
        }
      } catch (err) {
        console.error(err);
      }
    },
    goPath(item) {
      this.$router.push(this.resolvePath(item))
    },
    resolvePath(item) {
      if (isExternal(item.path)) {
        return item.path
      }
      if (isExternal(item.basePath)) {
        return item.basePath
      }
      return path.resolve(item.basePath, item.path)
    }
  }
};
</script>

<style lang="scss" scoped>
.consoleBox {
  width: 98%;
  height: calc(100vh - 90px);
  overflow-y: auto;
  background-color: rgb(255 255 255 / 95%);
  border-radius: 5px;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;

  .leftImg {
    width: 330px;
    margin: 10px;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.2);
    border-radius: 5px;
  }

  .consoleBox-item {
    background-color: #fff;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    width: 240px;
    margin: 10px;
    padding: 10px;

    .svg-icon {
      margin-right: 10px;
      font-size: 35px;
      color: #fff;
    }
  }
}

.fixed {
  width: 100%;
  height: 25px;
  position: relative;

  .bottom {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
  }
}

.dialog-content {
  max-height: 60vh;
  /* 或者其他适合的高度 */
  overflow-y: auto;
  /* 启用垂直滚动 */
}
</style>