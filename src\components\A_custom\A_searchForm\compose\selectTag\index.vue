<template>
    <!-- 选择的tag -->
    <div class="selectTag">
        <el-tag class="selectTag_item" v-for="item, indx in list" :type="indx == inx ? '' : 'info'" :key="indx"
            @click="tagClick(item, indx)" @close="handleClose(item, indx)" :closable="clearEvent(item)">{{
                item[echo.name] }}</el-tag>
    </div>
</template>

<script>
export default {
    props: {
        value: {
            type: [Array, Number, String],
        },
        list: {
            type: Array,
            default: () => { return [] }
        },
        index: {
            type: Number,
        },
        multiple: {
            type: Boolean,
            default: () => { return false }
        },
        echo: {
            type: Object,
            default: () => { return { id: "id", name: "name" } }
        },
    },
    data() {
        return {
            ids: [],
            inx: 1
        }
    },
    created() {
        this.inx = this.index
        if (String(this.index)) {
            this.ids = [this.inx]
        }
    },
    watch: {
        list: {
            handler(val) {
                // this.inx = 0
            },
            deep: true
        },
    },
    methods: {
        clearEvent(item) {
            if (item.name == "全部条件" || item.name == "方案一") return false
            if (item.clear == undefined || item.clear == null) {
                return true
            } else {
                return judge
            }
        },
        tagClick(data, index) {
            if (!this.multiple) {
                this.inx = index
                // this.ids = [data[this.echo.id]];
                this.$emit('input', data[index]);
            } else {
                let index = null;
                let judge = this.ids.some((x, i) => {
                    index = i
                    return x == data[this.echo.id];
                });
                if (judge) {
                    this.ids.splice(index, 1);
                } else {
                    this.ids = [...this.ids, data[this.echo.id]]
                }
                this.$emit('input', this.ids);
            }
            console.log(data, 'data');
            this.$emit('change', data);
        },
        typeEvent(data, index) {
            if (!this.multiple) {
                if (this.inx == index) {
                    return ""
                } else {
                    return "info"
                }
            } else {
                console.log(2);
                if (this.ids.includes(data[this.echo.id])) {
                    return ""
                } else {
                    return "info"
                }
            }
        },
        // 更新index
        upDataIndex(data, index) {
            this.inx = index
        },
        handleClose(data, index) {
            this.$emit('close', data);
        },
    },
}
</script>

<style lang="scss" scoped>
.selectTag {
    .selectTag_item {
        margin-right: 15px;
        cursor: pointer;
        margin-bottom: 5px;
    }
}
</style>