<template>
<!-- 下拉选择 -->
    <div class="lableInput_selectInput" >
        <div v-if="lable" class="lableInput_selectInput_lable" >{{lable}}</div>
        <el-select :style="[styles]" v-bind="$attrs" @change="changeEvent" :value="value" @input="(e)=>{$emit('input',e)}" :placeholder="placeholder">
            <slot></slot>
        </el-select>
    </div>
</template>

<script>
    export default {
        props:{
            width:{
                type:String,
                default:()=>{return "255px"}
            },
            lable:{
                type:String,
            },
            value:{
                type:[String,Number,Array]
            },
            placeholder:{
                type:[String],
                default:()=>{return "请选择" }
            },
        },
        data(){
            return{
                styles:{"--width":this.width},
            }
        },
        methods:{
            changeEvent(e){
                this.$emit('change', e);
            }
        },
    }
</script>

<style lang="scss">
    .lableInput_selectInput{
        display:flex;
        &_lable{
            display:flex;
            justify-content: center;
            align-items:center;
            height:36px;
            border:1px solid #dcdfe6;
            border-right:none;
            padding:0 8px;
            box-sizing:border-box;
            color:#1c2943;
            white-space: nowrap;
            margin-left:10px;
            font-size:14px;
            flex:none;
        }
        input{
            border-radius:0px;
            width:var(--width);
        }
    }
</style>