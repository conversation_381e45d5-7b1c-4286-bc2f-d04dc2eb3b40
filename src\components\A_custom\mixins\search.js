/*
    搜索 混入方法 
    使用中:[
        "baseFrame/index",
        "schemeQueryInput/index"
    ]
*/ 
export default {
    provide(){ return {  deep:()=>{return this.deep } }  },
    dicts:["sys_comp_type"], //操作符的字段
    components:{
        schemeQueryInput:()=>import("@/components/A_custom/A_searchForm/schemeQueryInput/index.vue"),
        selectTag:()=>import("@/components/A_custom/A_searchForm/compose/selectTag/index.vue"),
        searchLabel:()=>import("@/components/A_custom/A_searchForm/compose/searchLabel/index.vue"),
        checkboxLabel:()=>import("@/components/A_custom/A_searchForm/compose/checkboxLabel/index.vue"),
    },
    data() {
      return {
        queryParamsKeyList:[],
        deep:{
            resetting:true, //重置事件执行
        },
      }
    },
    created(){

    },
    destroyed(){

    }, 
    methods: {
        // 点击复选框获取这条搜索的唯一值ID 
        keyReturnEvent({data,judge}){
            if(judge){
                this.queryParamsKeyList.push(data.id)
            }else{
                this.queryParamsKeyList = this.queryParamsKeyList.filter(x=>{
                    return x!= data.id
                })
            }
        },
    }
  }
  