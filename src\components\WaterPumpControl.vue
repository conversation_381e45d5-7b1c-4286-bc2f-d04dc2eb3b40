<script setup>
import { ref, onMounted, inject, onBeforeUnmount, watch } from 'vue';
import { PoweroffOutlined } from '@ant-design/icons-vue';
import { getWebSocketInstance } from '../utils/websocket.js';
import { createRequestId, parseRequestId, sendWebSocketMessage } from './device.js';

// 注入GSAP
const gsap = inject('gsap');

// 接收父组件传递的设备数据
const props = defineProps({
  devices: {
    type: Array,
    default: () => []
  }
});

// 网络状态
const isOnline = ref(navigator.onLine);
const checkNetworkStatus = () => {
  isOnline.value = navigator.onLine;
};

// WebSocket实例
const ws = ref(null);

// 水泵状态数据
const waterPumpStatus = ref({});

// 设备空payload跟踪数据
const deviceEmptyPayloadTracker = ref({});

// 监听网络状态变化
onMounted(() => {
  window.addEventListener('online', checkNetworkStatus);
  window.addEventListener('offline', checkNetworkStatus);
});

onBeforeUnmount(() => {
  window.removeEventListener('online', checkNetworkStatus);
  window.removeEventListener('offline', checkNetworkStatus);
  
  // 停止定时查询
  stopStatusQuery();
  
  // 关闭WebSocket连接
  if (ws.value) {
    ws.value.close();
  }
});

// 初始化WebSocket连接
const initWebSocket = () => {
  ws.value = getWebSocketInstance({
    reconnectInterval: 5000,
    maxReconnectAttempts: 10,
    heartbeatInterval: 30000
  });

  // 监听WebSocket事件
  ws.value.on('open', () => {
    console.log('水泵WebSocket连接成功');
    // 连接成功后立即查询所有水泵状态
    queryAllStatus();
  });

  ws.value.on('message', handleWebSocketMessage);
  
  ws.value.on('error', (error) => {
    console.error('水泵WebSocket错误:', error);
  });

  ws.value.on('close', () => {
    console.log('水泵WebSocket连接关闭');
  });

  // 连接到WebSocket服务器
  ws.value.connect('ws://localhost:8080/ws/device');
};

// 处理WebSocket消息
const handleWebSocketMessage = (data) => {
  console.log('收到水泵WebSocket消息:', data);
  
  if (data.type === 'STATUS_UPDATE') {
    // 从requestId中提取设备IP
    const deviceIp = parseRequestId(data.requestId);
    
    if (deviceIp) {
      // 初始化设备跟踪器
      if (!deviceEmptyPayloadTracker.value[deviceIp]) {
        deviceEmptyPayloadTracker.value[deviceIp] = {
          emptyPayloadStartTime: null,
          consecutiveEmptyCount: 0
        };
      }
      
      // 检查payload是否为空或无效
      const isEmptyPayload = !data.payload || Object.keys(data.payload).length === 0;
      
      if (isEmptyPayload) {
        // 收到空payload
        if (!deviceEmptyPayloadTracker.value[deviceIp].emptyPayloadStartTime) {
          // 开始记录空payload时间
          deviceEmptyPayloadTracker.value[deviceIp].emptyPayloadStartTime = Date.now();
          deviceEmptyPayloadTracker.value[deviceIp].consecutiveEmptyCount = 1;
        } else {
          // 增加连续空payload计数
          deviceEmptyPayloadTracker.value[deviceIp].consecutiveEmptyCount++;
        }
        console.log(`设备 ${deviceIp} 收到空payload，连续次数: ${deviceEmptyPayloadTracker.value[deviceIp].consecutiveEmptyCount}`);
      } else {
        // 收到有效payload，重置空payload跟踪
        deviceEmptyPayloadTracker.value[deviceIp].emptyPayloadStartTime = null;
        deviceEmptyPayloadTracker.value[deviceIp].consecutiveEmptyCount = 0;
        
        // 根据实际返回的字段更新状态
        waterPumpStatus.value[deviceIp] = {
          ...waterPumpStatus.value[deviceIp],
          pressure: data.payload.current_pressure || 0,
          flow: data.payload.output_freq || 0,
          current: data.payload.output_current || 0,
          power: data.payload.output_power || 0,
          voltage: data.payload.output_voltage || 0,
          setPressure: data.payload.set_pressure || 0,
          running: data.payload.run_control === 1,
          faultType: data.payload.fault_type || 0,
          faults: {
            overload: (data.payload.fault_type & 1) !== 0,
            phaseLoss: (data.payload.fault_type & 2) !== 0,
            overheating: (data.payload.fault_type & 4) !== 0,
            leakage: (data.payload.fault_type & 8) !== 0
          },
          lastUpdate: Date.now(),
          isOnline: true
        };
        console.log(`设备 ${deviceIp} 状态更新成功`);
      }
    }
  } else if (data.type === 'CONTROL_RESULT') {
    console.log('水泵控制结果:', data.payload);
    if (data.payload?.success) {
      // 控制成功后立即查询状态
      setTimeout(() => {
        queryAllStatus();
      }, 1000);
    }
  } else if (data.type === 'ERROR') {
    console.error('水泵操作错误:', data.payload?.message);
  }
};

// 查询所有设备状态
const queryAllStatus = () => {
  props.devices.forEach(device => {
    queryStatus(device.ip);
  });
};

// 查询单个设备状态
const queryStatus = (ip) => {
  const payload = {
    ip: ip,
    deviceType: 3
  };
  
  try {
    sendWebSocketMessage(ws.value, 'GET_STATUS', {ip: ip, type: 3});
  } catch (error) {
    console.warn('查询水泵状态失败:', error.message);
  }
};

// 控制水泵压力
const setPumpPressure = (ip, pressure) => {
  try {
    sendWebSocketMessage(ws.value, 'CONTROL_DEVICE', {ip: ip, type: 3}, 'set_pressure', pressure);
  } catch (error) {
    console.warn('控制水泵压力失败:', error.message);
  }
};

// 控制水泵开关
const setPumpRunControl = (ip, runControl) => {
  try {
    sendWebSocketMessage(ws.value, 'CONTROL_DEVICE', {ip: ip, type: 3}, 'run_control', runControl);
  } catch (error) {
    console.warn('控制水泵开关失败:', error.message);
  }
};

// 定时查询状态
let statusQueryInterval = null;

// 开始定时查询状态（每10秒）
const startStatusQuery = () => {
  if (statusQueryInterval) {
    clearInterval(statusQueryInterval);
  }
  
  statusQueryInterval = setInterval(() => {
    queryAllStatus();
    checkDeviceOnlineStatus(); // 检查设备在线状态
  }, 10000);
};

// 检查设备在线状态
const checkDeviceOnlineStatus = () => {
  Object.keys(deviceEmptyPayloadTracker.value).forEach(deviceIp => {
    const tracker = deviceEmptyPayloadTracker.value[deviceIp];
    const status = waterPumpStatus.value[deviceIp];
    
    if (tracker && tracker.emptyPayloadStartTime && status) {
      const emptyDuration = Date.now() - tracker.emptyPayloadStartTime;
      
      // 调试日志：显示当前空payload持续时间
      if (emptyDuration > 30000) { // 超过30秒时开始记录
        console.log(`设备 ${deviceIp} 空payload持续时间: ${Math.round(emptyDuration/1000)}秒`);
      }
      
      // 如果连续60秒收到空payload，标记设备为离线
      if (emptyDuration >= 60000) {
        if (status.isOnline !== false) {
          console.log(`设备 ${deviceIp} 连续60秒收到空payload，标记为离线`);
          waterPumpStatus.value[deviceIp] = {
            ...status,
            isOnline: false
          };
        }
      }
    }
  });
};

// 停止定时查询
const stopStatusQuery = () => {
  if (statusQueryInterval) {
    clearInterval(statusQueryInterval);
    statusQueryInterval = null;
  }
};

// 控制水泵开关
const togglePump = (device) => {
  const currentStatus = waterPumpStatus.value[device.ip];
  const newRunControl = currentStatus?.running ? 0 : 1; // 切换运行状态
  
  setPumpRunControl(device.ip, newRunControl);
  
  // 卡片状态动画
  animatePumpToggle(device);
};

// 处理压力滑块变化
const handlePressureChange = (device, value) => {
  setPumpPressure(device.ip, value);
};

// 获取设备状态
const getDeviceStatus = (device) => {
  return waterPumpStatus.value[device.ip] || {
    pressure: 0,
    flow: 0,
    current: 0,
    power: 0,
    voltage: 0,
    setPressure: 0,
    running: false,
    faultType: 0,
    faults: {
      overload: false,
      phaseLoss: false,
      overheating: false,
      leakage: false
    },
    isOnline: true, // 默认在线状态
    lastUpdate: null
  };
};

// 判断设备是否在线
const isDeviceOnline = (device) => {
  const status = waterPumpStatus.value[device.ip];
  const tracker = deviceEmptyPayloadTracker.value[device.ip];
  
  // 如果没有状态记录，认为离线
  if (!status) return false;
  
  // 如果设备状态中明确标记为在线，返回true
  if (status.isOnline === true) return true;
  
  // 如果没有空payload跟踪记录，使用原有的30秒逻辑作为兜底
  if (!tracker) {
    return status.lastUpdate && (Date.now() - status.lastUpdate) < 30000;
  }
  
  // 检查是否连续60秒收到空payload
  if (tracker.emptyPayloadStartTime) {
    const emptyDuration = Date.now() - tracker.emptyPayloadStartTime;
    // 如果连续60秒收到空payload，认为离线
    if (emptyDuration >= 60000) {
      console.log(`设备 ${device.ip} 连续60秒收到空payload，判定为离线`);
      return false;
    }
  }
  
  // 其他情况认为在线
  return true;
};

// 监听设备数据变化
watch(() => props.devices, (newDevices) => {
  if (newDevices && newDevices.length > 0) {
    // 初始化设备状态
    newDevices.forEach(device => {
      if (!waterPumpStatus.value[device.ip]) {
        waterPumpStatus.value[device.ip] = getDeviceStatus(device);
      }
      // 初始化空payload跟踪器
      if (!deviceEmptyPayloadTracker.value[device.ip]) {
        deviceEmptyPayloadTracker.value[device.ip] = {
          emptyPayloadStartTime: null,
          consecutiveEmptyCount: 0
        };
      }
    });
    
    // 如果WebSocket已连接，查询状态
    if (ws.value && ws.value.isConnected()) {
      queryAllStatus();
    }
  }
}, { immediate: true });

// 状态切换动画
const animatePumpToggle = (pump) => {
  const pumpCard = document.querySelector(`.pump-card-${pump.id}`);
  if (pumpCard) {
    // 背景色动画 - 保持一致的颜色
    gsap.to(pumpCard, {
      backgroundColor: '#0D275A',
      duration: 0.5,
      ease: "power2.inOut",
      clearProps: "transform", // 确保清除transform属性
      onComplete: () => {
        // 动画完成后强制清除transform属性
        pumpCard.style.transform = "none";
      }
    });

    // 状态标签动画
    const statusTag = pumpCard.querySelector('.status-tag');
    if (statusTag) {
      gsap.fromTo(statusTag,
        { scale: 0.8, opacity: 0.7 },
        {
          scale: 1,
          opacity: 1,
          duration: 0.3,
          ease: "back.out(1.7)",
          clearProps: "all" // 清除所有动画属性
        }
      );
    }

    // 电源按钮动画
    const powerBtn = pumpCard.querySelector('.power-btn');
    if (powerBtn) {
      gsap.to(powerBtn, {
        rotation: pump.running ? 180 : 0,
        duration: 0.5,
        ease: "back.out(1.7)",
        clearProps: "all" // 清除所有动画属性
      });
    }
  }
};

onMounted(() => {
  // 初始化WebSocket连接
  initWebSocket();
  
  // 开始定时查询状态
  startStatusQuery();
  
  // 卡片入场动画
  const cards = document.querySelectorAll('.pump-card');
  gsap.from(cards, {
    y: 30,
    opacity: 0,
    duration: 0.6,
    stagger: 0.1,
    ease: "power2.out",
    clearProps: "transform,opacity" // 确保动画结束后清除所有属性
  });

  // 信息入场动画
  gsap.from('.pump-info', {
    opacity: 0,
    x: -20,
    duration: 0.5,
    stagger: 0.1,
    delay: 0.3,
    clearProps: "all" // 确保动画结束后清除所有属性
  });
});
</script>

<template>
  <div class="water-pump-container">
    <a-row :gutter="[16, 24]">
      <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="6" v-for="device in props.devices" :key="device.id">
        <a-card
          :title="device.name"
          :bordered="false"
          :class="['pump-card', `pump-card-${device.id}`, {'running': getDeviceStatus(device).running, 'offline': !isDeviceOnline(device)}]"
          :headStyle="{color: '#FFFFFF', backgroundColor: '#0D275A', borderBottom: 'none'}"
        >
          <template #extra>
            <span class="network-status" :class="{ 'online': isDeviceOnline(device), 'offline': !isDeviceOnline(device) }">
              <span class="status-dot"></span>
              {{ isDeviceOnline(device) ? '在线' : '离线' }}
            </span>
            <a-tag :color="getDeviceStatus(device).running ? '#2ECC71' : '#E74C3C'" class="status-tag">
              {{ getDeviceStatus(device).running ? '运行中' : '已停止' }}
            </a-tag>
            <a-button
              type="primary"
              shape="circle"
              size="small"
              class="power-btn"
              style="margin-left: 8px; background-color: #3498DB; border-color: #3498DB;"
              @click="togglePump(device)"
              :disabled="!isDeviceOnline(device)"
            >
              <PoweroffOutlined />
            </a-button>
          </template>
          <div style="font-size:13px;line-height:1.6;" class="pump-info">
            <div class="info-row">
              <span class="info-label">设备IP：</span>
              <span class="ip-value">{{ device.ip }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">压力：</span>
              <span class="pressure-value">{{ Math.round(getDeviceStatus(device).pressure) / 10 }}</span>
              <div class="progress-bar pressure-bar">
                <div class="progress-fill" :style="{ width: `${Math.min(getDeviceStatus(device).pressure * 10, 100)}%` }"></div>
              </div>
            </div>
            <div class="info-row">
              <span class="info-label">流量：</span>
              <span class="flow-value">{{ Math.round(getDeviceStatus(device).flow * 10) / 10 }}</span>
              <div class="progress-bar flow-bar">
                <div class="progress-fill" :style="{ width: `${Math.min(getDeviceStatus(device).flow, 100)}%` }"></div>
              </div>
            </div>
            <div class="info-row">
              <span class="info-label">电流：</span>
              <span class="current-value">{{ Math.round(getDeviceStatus(device).current * 10) / 10 }}A</span>
              <div class="progress-bar current-bar">
                <div class="progress-fill" :style="{ width: `${(getDeviceStatus(device).current / 35) * 100}%` }"></div>
              </div>
            </div>
            <div class="info-row">
              <span class="info-label">功率：</span>
              <span class="power-value">{{ Math.round(getDeviceStatus(device).power * 10) / 10 }}W</span>
              <div class="progress-bar power-bar">
                <div class="progress-fill" :style="{ width: `${(getDeviceStatus(device).power / 20) * 100}%` }"></div>
              </div>
            </div>
            <div class="info-row">
              <span class="info-label">电压：</span>
              <span class="voltage-value">{{ Math.round(getDeviceStatus(device).voltage * 10) / 10 }}V</span>
            </div>
            <div class="info-row">
              <span class="info-label">设定压力({{getDeviceStatus(device).setPressure}})：</span>
              <div class="pressure-control">
                <a-slider
                  :min="0"
                  :max="10"
                  :value="getDeviceStatus(device).setPressure"
                  @change="(value) => handlePressureChange(device, value)"
                  :disabled="!isDeviceOnline(device)"
                  :tooltip-formatter="(value) => `${value}%`"
                />
              </div>
            </div>
            <div class="info-row" v-if="getDeviceStatus(device).faultType > 0">
              <span class="info-label">故障状态：</span>
              <div class="fault-container">
                <a-tag color="red" v-if="getDeviceStatus(device).faults.overload" size="small">过载</a-tag>
                <a-tag color="red" v-if="getDeviceStatus(device).faults.phaseLoss" size="small">缺相</a-tag>
                <a-tag color="red" v-if="getDeviceStatus(device).faults.overheating" size="small">过热</a-tag>
                <a-tag color="red" v-if="getDeviceStatus(device).faults.leakage" size="small">漏电</a-tag>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<style scoped>
/* 网络状态样式 */
.network-status {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  margin-right: 8px;
  transition: all 0.3s;
}

.network-status .status-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 4px;
}

.network-status.online {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.network-status.online .status-dot {
  background-color: #2ecc71;
  box-shadow: 0 0 6px #2ecc71;
}

.network-status.offline {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.network-status.offline .status-dot {
  background-color: #e74c3c;
  box-shadow: 0 0 6px #e74c3c;
}

/* 卡片悬停效果 */
.pump-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.5s ease;
}

.pump-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 20px rgba(0,0,0,0.4);
}

.pump-card.running {
  background-color: #0D275A; /* 保持一致的颜色 */
}

/* 按钮动画 */
.power-btn {
  transition: all 0.3s ease;
}
.power-btn:hover {
  transform: rotate(180deg);
  background-color: #2980B9 !important;
}

/* 状态标签动画 */
.status-tag {
  transition: all 0.3s ease;
}

/* 开关动画 */
.control-switch {
  transition: all 0.4s ease !important;
}
.control-switch.ant-switch-checked {
  transform: scale(1.05);
}

/* 进度条样式 */
.progress-bar {
  height: 4px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  margin-top: 2px;
  margin-bottom: 5px;
  overflow: hidden;
  width: 100%;
}

.progress-fill {
  height: 100%;
  background-color: #3498DB;
  border-radius: 2px;
  transition: width 1s ease;
}

.current-bar .progress-fill {
  background-color: #F39C12;
}

.power-bar .progress-fill {
  background-color: #2ECC71;
}

.current-value, .power-value, .status-value {
  font-weight: bold;
  transition: all 0.3s ease;
}

.water-pump-container {
  width: 100%;
}

.section-title {
  margin-bottom: 20px;
  color: #FFFFFF;
  font-weight: bold;
}

.pump-card {
  background: #0D275A;
  box-shadow: 0 8px 16px rgba(0,0,0,0.3);
  border-radius: 12px;
  margin: 0;
  color: #FFFFFF;
  border: none;
  height: auto; /* 自适应高度 */
  min-height: 280px; /* 增加最小高度 */
  display: flex;
  flex-direction: column;
  transform: none !important; /* 防止transform属性影响布局 */
  will-change: auto; /* 优化动画性能 */
}

.fault-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.fault-tag {
  margin-bottom: 8px;
}

.control-buttons {
  display: flex;
  justify-content: center;
}

.info-row {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}

.info-label {
  width: 100px;
  text-align: right;
  padding-right: 10px;
  color: rgba(255, 255, 255, 0.8);
}

.status-value,
.current-value,
.power-value,
.pressure-value,
.flow-value,
.voltage-value,
.ip-value {
  min-width: 60px;
  display: inline-block;
  margin-right: 8px;
  text-align: right;
  font-weight: bold;
}

.progress-bar {
  flex: 1;
  min-width: 120px;
  margin-left: 8px;
}

/* 压力控制样式 */
.pressure-control {
  flex: 1;
  min-width: 120px;
  margin-left: 8px;
  display: flex;
  align-items: center;
}

.pressure-control .ant-slider {
  margin: 0;
  width: 100%;
}

.pressure-control .ant-slider-rail {
  background-color: rgba(255, 255, 255, 0.2);
  height: 6px;
}

.pressure-control .ant-slider-track {
  background-color: #3498DB;
  height: 6px;
}

.pressure-control .ant-slider-handle {
  border-color: #3498DB;
  background-color: #3498DB;
  width: 16px;
  height: 16px;
  margin-top: -5px;
}

.pressure-control .ant-slider-handle:hover {
  border-color: #2980B9;
  background-color: #2980B9;
}

.pressure-control .ant-slider-handle:focus {
  border-color: #2980B9;
  background-color: #2980B9;
  box-shadow: 0 0 0 5px rgba(52, 152, 219, 0.2);
}

.pressure-control .ant-slider:hover .ant-slider-rail {
  background-color: rgba(255, 255, 255, 0.3);
}

.pressure-control .ant-slider:hover .ant-slider-track {
  background-color: #2980B9;
}



/* 移动端适配 */
@media (max-width: 768px) {
  .pump-card {
    margin-bottom: 4px;
    padding: 4px 0;
  }
  .section-title {
    margin-bottom: 8px;
    font-size: 16px;
  }
  .a-row {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  .a-col {
    padding-left: 2px !important;
    padding-right: 2px !important;
  }
  
  .pressure-control {
    min-width: 120px;
  }
}
</style>
