<template>
  <div class="lookImage" >
    <el-dialog title="附件查看" v-if="dialog" :visible.sync="show" width="865px" append-to-body :before-close="noEvent" :close-on-click-modal="false">
      <div v-if="imageList.length>0">
        <div class="mb10 bold">图片：</div>
        <el-image class="mr5 ml5 mb5" v-for="item,index in imageList" :key="index"  style="width: 150px; height: 150px" :src="item"  :preview-src-list="imageList"></el-image>
      </div>
      <div v-if="textList.length > 0" >
        <div class="mb10 mt20 bold">其他：</div>
        <div v-for="item,index in textList" :key="index" class="row alignCenter mb5 ml10" >
          <div class="mr10 lookImage_text">{{item.split("/")[item.split("/").length-1]}}</div>
          <el-button type="success" size="mini" @click="textDownload(item)" >下载</el-button>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="$emit('update:show',false)">取 消</el-button>
      </div>
    </el-dialog>

    <div v-else>
      <div v-if="imageList.length>0">
        <div class="mb10 bold" style="color: rgb(125, 125, 125);">图片：</div>
        <el-image class="mr5 ml5 mb5" v-for="item,index in imageList" :key="index"  style="width: 90px; height: 90px" :src="item"  :preview-src-list="imageList"></el-image>
      </div>
      <div v-if="textList.length > 0" >
        <div class="mb10 mt20 bold" style="color: rgb(125, 125, 125);">其他：</div>
        <div v-for="item,index in textList" :key="index" class="row alignCenter mb5 ml10" >
          <div class="mr10 lookImage_text">{{item.split("/")[item.split("/").length-1]}}</div>
          <el-button type="success" size="mini" @click="textDownload(item)" >下载</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
      show:{
          type:Boolean,
          default:()=>false
      },
      value:{
          type:[String,Array],
          default:()=>""
      },
      dialog:{
        type:[Boolean],
        default:()=> true
      },
  },
  data() {
    return {
      imageList:[],
      textList:[],
      // 存储桶是否有权限 true 是 ,false 否
      judge:true,
    };
  },
  watch:{
    show(){
      this.imageList = [];
      this.textList = [];
      if(this.show){
        this.oneEvent();
      }
    }
  },
  created(){
    if(!this.dialog){
      this.oneEvent();
    }
  },
  methods: {
    // 两种模式查看图片 , 有权限 ,没权限
      oneEvent(){
        if(!this.value)return
        let list = this.value.split(",");
        list.forEach(x => {
          let str = x.split(".")[x.split(".").length-1];
          if(!this.judge){
            if(str=="jpg"||str=="png"){
              // this.imageList.push(window.location.origin + process.env.VUE_APP_BASE_API + "/"  + x)
              this.imageList.push("http://"+ x); //没有限制的
            }else{
              this.textList.push(x)
            }
          }else{
            if(str=="jpg"||str=="png"){
              this.gainCosJsSdk(x).then(res=>{
                this.imageList.push(res)
              })
            }else{
              this.gainCosJsSdk(x).then(res=>{
                this.textList.push(res)
              })
            }
          }
        });
        console.log(list,"-----------------")
      },
      noEvent(){
          this.$emit('update:show',false)
      },
      textDownload(src){
        console.log(src,"----")
        let strType = src.split(".")[src.split(".").length-1];
        this.download2(src, {}, `${new Date().getTime()}.${strType}`)
      },
  }
};
</script>
<style scoped lang="scss">
.lookImage_text{
  overflow:hidden;  
  text-overflow:ellipsis;
  white-space:nowrap;
}
</style>

